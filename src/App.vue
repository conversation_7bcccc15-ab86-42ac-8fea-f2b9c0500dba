<script>
import config from './config';
import store from '@/store';
import { getToken } from '@/utils/auth';
import silenceUpdate from '@/uni_modules/rt-uni-update/js_sdk/silence-update.js'; //引入静默更新
import { getApkUpdate } from '@/api/cloudData/index.js';
import { appBoundUser } from '@/api/login';

export default {
  onLaunch: function () {
    this.initApp();
  },
  onShow() {
    //#ifdef APP-PLUS
    var clientid;
    var timer = setInterval(function () {
      if (clientid != null && clientid != 'null') {
        clearInterval(timer);
        appBoundUser({ cid: clientid }).then((res) => {});
        return;
      }
      clientid = plus.push.getClientInfo().clientid;
      // console.log(plus.push.getClientInfo());
    }, 1000);
    // #endif
    // uni.navigateTo({
    // 	url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update'
    // });
  },
  onLoad() {},
  methods: {
    // 初始化应用
    initApp() {
      let _self = this;
      // 初始化应用配置
      this.initConfig();
      // 检查用户登录状态
      //#ifdef H5
      this.checkLogin();

      //#endif
      //#ifdef APP-PLUS
      this.checkUpdate();
      // var client = uni.getSystemInfoSync().platform;

      // Vue.prototype.$api.listenTranMsg()
      // var info = plus.push.getClientInfo();
      // tao-ios-放开
      /* 5+  push 消息推送 ps:使用:H5+的方式监听，实现推送*/
      plus.push.addEventListener(
        'click',
        function (msg) {
          var clent = uni.getSystemInfoSync().platform;

          if (clent == 'ios') {
            //如果是IOS
            var payload;
            if (msg.type == 'click') {
              //APP离线点击包含click属性，这时payload是JSON对象
              payload = msg.payload;
            } else {
              //APP在线，收到消息不会包含type属性,这时的payload是JSON字符串，需要转为JSON对象
              payload = JSON.parse(msg.payload);
            }
            if (payload != null || payload != undefined) {
              _self.setUniPush_state('1');
              _self.setUniPush_url(payload.payload);
              uni.reLaunch({
                // url: '/pages/index/index'
                url: '/pages/login',
              });
            }
          }
          if (clent == 'android') {
            //如果是Android，收到playload均是是JSON字符串，需要转为JSON对象
            var payload = msg.payload;

            if (payload != null || payload != undefined) {
              _self.setUniPush_state('1');
              _self.setUniPush_url(payload);
              uni.reLaunch({
                // url: '/pages/index/index'
                url: '/pages/login',
              });
            }
          }
          //这里可以写跳转业务代码
        },
        false
      );
      // 监听在线消息事件
      plus.push.addEventListener(
        'receive',
        function (msg) {
          // plus.ui.alert(2);
          var clent = uni.getSystemInfoSync().platform;

          if (clent == 'ios') {
            //如果是IOS
            var payload = msg.payload;
            //【APP离线】收到消息，但没有提醒（发生在一次收到多个离线消息时，只有一个有提醒，但其他的没有提醒）
            //【APP在线】收到消息，不会触发系统消息,需要创建本地消息，但不能重复创建。必须加msg.type验证去除死循环
            if (msg.aps == null && msg.type == 'receive') {
              var messageTitle = payload.messageTitle;
              var messageContent = payload.messageContent;
              //创建本地消息,发送的本地消息也会被receive方法接收到，但没有type属性，且aps是null
              plus.push.createMessage(messageContent, JSON.stringify(payload), {
                title: messageTitle,
              });
            }
            // else{
            //  var payload = JSON.parse(msg.payload);
            //  plus.push.createMessage(payload.messageContent, JSON.stringify(payload.payload), {title: payload.messageTitle});
            // }
          }
          if (clent == 'android') {
            //如果是Android，当APP在线时，收到透传消息不会进入系统消息，需要发送本地提醒。
            var payload = JSON.parse(msg.content);
            var messageTitle = payload.messageTitle;
            var messageContent = payload.messageContent;
            plus.push.createMessage(messageContent, msg.payload, {
              title: messageTitle,
            });
            plus.runtime.setBadgeNumber(1);
          }
        },
        false
      );
      // #endif
    },
    initConfig() {
      this.globalData.config = config;
    },
    checkLogin() {
      if (!getToken()) {
        //this.$tab.reLaunch('/pages/mine/index');
      }
    },
    checkUpdate() {
      // 获取本地应用资源版本号
      plus.runtime.getProperty(plus.runtime.appid, (inf) => {
        let platform = uni.getSystemInfoSync().platform;
        //获取服务器的版本号
        let params = {
          type: platform == 'android' ? 1 : 2,
          editionNumber: inf.versionCode,
        };
        getApkUpdate(params).then((res) => {
          let updateData = {
            describe: res.data?.describe,
            edition_url: res.data?.editionUrl, // //安装包下载地址或者通用应用市场地址
            edition_force: Number(res.data?.editionForce), //是否强制更新 0代表否 1代表是
            package_type: Number(res.data?.packageType), // 0是整包升级 1是wgt升级
            edition_number: Number(res.data?.editionNumber),
            edition_name: res.data?.editionName,
            edition_issue: Number(res.data?.editionIssue),
          };

          if (
            Number(updateData.edition_number) > Number(inf.versionCode) &&
            updateData.edition_issue == 1
          ) {
            if (platform == 'ios') {
              if (updateData.package_type == 1) {
                silenceUpdate(updateData.edition_url);
              } else {
                setTimeout(() => {
                  uni.navigateTo({
                    url:
                      '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
                      JSON.stringify(updateData),
                  });
                }, 1000);
              }
            } else if (platform == 'android') {
              // 如果是热更新并且是静默更新
              if (updateData.package_type == 1) {
                silenceUpdate(updateData.edition_url);
              } else {
                setTimeout(() => {
                  uni.navigateTo({
                    url:
                      '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
                      JSON.stringify(updateData),
                  });
                }, 1000);
              }
            }
          }
        });
      });
    },
  },
};
</script>

<style lang="scss">
@import '@/static/scss/index.scss';
@import 'uview-ui/index.scss';
</style>
