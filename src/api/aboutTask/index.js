import request from '@/utils/request';

//获取待办工单
export const queryMyTaskRequest = (path, query) => {
  return request({
    url: `/flowable/task/${path}`,
    method: 'get',
    params: query,
  });
};

//获取工单类型字典
export const getDicts = (dictType) => {
  return request({
    url: '/system/dict/data/type/' + dictType,
    method: 'get',
  });
};

//获取工单状态
export const getMaintenanceRecordNum = (query) => {
  return request({
    url: `/flowable/definition/getTaskNodeCountByDefId?procDefId=${query}`,
    method: 'get',
  });
};

// 查询维修记录列表
export const getMaintenanceRecordList = (query) => {
  return request({
    url: '/flowable/definition/getTaskNodeByDefId',
    method: 'get',
    params: query,
  });
};

// 查询工单类型管理列表
export const getProcessCategoryList = (query) => {
  return request({
    url: '/flowable/processCategory/list',
    method: 'get',
    params: query,
  });
};

// 流程节点表单
export const queryFlowTaskFormRequest = (query) => {
  return request({
    url: '/flowable/task/flowTaskForm',
    method: 'get',
    params: query,
  });
};

// 获取流程变量
export const getProcessVariables = (query) => {
  return request({
    url: '/flowable/task/processVariables/',
    method: 'get',
    params: query,
  });
};

// 查询未读数量
export function getUnreadNum(query) {
  return request({
    url: '/system/notice/selectUnreadNum',
    method: 'get',
    params: query,
  });
}

// 代办工单类型数量
export function todoProcessTypeNum(query) {
  return request({
    url: '/flowable/task/todoListTypeCount',
    method: 'get',
    params: query,
  });
}

// 添加扫描时间
export function updateScanCodeTime(data) {
  return request({
    url: '/flowable/pollBasicInfo/updateScanCodeTime',
    method: 'put',
    data,
  });
}
// 任务流转记录
export const flowRecord = (query) => {
  return request({
    url: '/flowable/task/flowRecord',
    method: 'get',
    params: query,
  });
};
// 驳回任务
export function rejectTask(data) {
  return request({
    url: '/flowable/task/reject',
    method: 'post',
    data: data,
  });
}
// 审批拒绝
export function refuseTask(data) {
  return request({
    url: '/flowable/task/refuse',
    method: 'post',
    data: data,
  });
}
// 通过并派单
export function flowPassSend(data) {
  return request({
    url: '/flowable/task/complete',
    method: 'post',
    data: data,
  });
}
// 查询审批人
export function getApproveUser(query) {
  return request({
    url: '/flowable/task/getApproveGroup',
    method: 'get',
    params: query,
  });
}
// 转办任务
export function switchTask(data) {
  return request({
    url: '/flowable/task/assignTask',
    method: 'post',
    data: data,
  });
}
// 查询供应商资源
export function getSupplierProduct(query) {
  return request({
    url: '/supply/product/getSupplierProduct',
    method: 'get',
    params: query,
  });
}
// 完成工作
export function flowCompleteOrder(data) {
  return request({
    url: '/flowable/repairBasicInfo/completeOrder',
    method: 'post',
    data: data,
  });
}
// 完成应急工单
export function completeEmergency(data) {
  return request({
    url: '/flowable/emergency/complete',
    method: 'put',
    data: data,
  });
}

/*
  查询最新工作流列表
*/
export function queryLastVersionList(data) {
  return request({
    url: '/flowable/definition/lastVersionList',
    method: 'get',
    params: data,
  });
}
// 跨部门预约申请
export function launchApply(data) {
  return request({
    url: '/transDeptApply/launchApply',
    method: 'post',
    data: data,
  });
}
// 通行权限申请
export function passApply(data) {
  return request({
    url: '/transitAuth/apply',
    method: 'post',
    data: data,
  });
}
export function applyInfo(id) {
  return request({
    url: `/transitAuth/apply/${id}`,
    method: 'get',
  });
}

export function applyList(data) {
  return request({
    url: `/transitAuth/apply/list`,
    method: 'get',
    params: data,
  });
}
export function workflowHistory(data) {
  return request({
    url: `/workflowHistory/list`,
    method: 'get',
    params: data,
  });
}
