import request from "@/utils/request";

// 告警记录 APP
export const queryAlarmListRequest = queryData => {
	return request({
		url: "/transitAlarm/getAlarmRecord",
		method: "get",
		params: queryData
	});
};

// 我的告警APP
export const transitAlarmGetMyAlarm = queryData => {
	return request({
		url: "/transitAlarm/getMyAlarm",
		method: "get",
		params: queryData
	});
};

// 查询详情
export const transitAlarmGetDetail = alarmId => {
	return request({
		url: `/transitAlarm/getDetail/${alarmId}`
	});
};

// 处理告警
export const transitAlarmDisposeAlarm = data => {
	return request({
		url: "/transitAlarm/disposeAlarm",
		method: "POST",
		data
	});
};

// 
export const alarmDisposeGetDetail = id => {
	return request({
		url: `/alarm/dispose/getDetail/${id}`
	});
};

// 新增告警（演示）
export const addAlarm = data => {
	return request({
		url: "/alarm/insertShow",
		method: "POST",
		data
	});
};
// 告警配置
export function strategyList(query) {
  return request({
    url: "/alarm/config/getList",
    method: "get",
    params: query
  });
}
// 告警配置
export function getCarList(query) {
  return request({
    url: "/dispatch/car/list",
    method: "get",
    params: query
  });
}
// 处理告警
export const handleAlarm = data => {
	return request({
		url: "/transitAlarm/disposeAlarm",
		method: "POST",
		data
	});
};