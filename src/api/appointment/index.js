import request from '@/utils/request';

// 预约记录列表
export const getAppointmentRecordList = (query) => {
  return request({
    url: `/appointment/record/list`,
    method: 'get',
    params: query,
  });
};

// 获取预约对象列表
export const getAppointmentObjectList = (query) => {
  return request({
    url: `/appointment/object/list`,
    method: 'get',
    params: query,
  });
};

// 获取启用预约对象列表
export const getEnabledAppointmentObjectList = (query) => {
  return request({
    url: `/appointment/object/enabled`,
    method: 'get',
    params: query,
  });
};

// 访客获取来访事由
export function applyVisitReason() {
  return request({
    url: '/appointment/invitation/visitReason',
    method: 'get',
  });
}

// 发起邀约
export const getApplyInvitation = (query) => {
  return request({
    url: `/appointment/invitation`,
    method: 'post',
    data: query,
  });
};

// 获取预约对象列表
export const getAppointmentRecord = (recordId) => {
  return request({
    url: `/appointment/record/${recordId}`,
    method: 'get',
  });
};

// 预约审核
export const getAppointmentAapprove = (recordId) => {
  return request({
    url: `/appointment/apply/testGeneratePass/${recordId}`,
    method: 'post',
  });
};

// 根据id查询预约对象详情
export const getAppointmentObject = (objId) => {
  return request({
    url: `/appointment/object/${objId}`,
    method: 'get',
  });
};

export function testExtendLeaveApplyPass(applyId) {
  return request({
    url: `/appointment/apply/testExtendLeaveApplyPass/${applyId}`,
    method: 'post',
  });
}
// 获取公交路线-站点
export function getBusStation() {
  return request({
    url: '/bus/station',
    method: 'get',
  });
}

// 公交接驳申请
export function busAppointment(data) {
  return request({
    url: '/bus/appointment',
    method: 'post',
    data: data,
  });
}
// 获取邀约个人列表
export const getInvitationList = (query) => {
  return request({
    url: `/appointment/invitation/myInvitation`,
    method: 'get',
    params: query,
  });
};
// 获取邀约详情
export const getInvitationInfo = (invitationId) => {
  return request({
    url: `/appointment/invitation/${invitationId}`,
    method: 'get',
  });
};
