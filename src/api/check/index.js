import request from "@/utils/request";

/*
  查询计划列表
*/
export const getPlanList = (page, query) => {
  return request({
    url: `/${page}/plan/newList`,
    method: "get",
    params: query
  });
}

// 巡检计划-计划详情
export const getInspectionPlanById = (id) => {
  return request({
    url: `/inspection/plan/getPlanById/${id}`,
    method: "get"
  });
};
// 保养计划-计划详情
export const getMaintenancePlanById = (id) => {
  return request({
    url: `/maintenance/plan/getPlanById/${id}`,
    method: "get"
  });
};

// 巡检计划-模板类型
export const getInspectionTemplateList = () => {
  return request({
    url: `/inspection/template/list`,
    method: "get"
  });
};
// 保养计划-模板类型
export const getMaintenanceTemplateList = () => {
  return request({
    url: `/maintenance/template/list`,
    method: "get"
  });
};

// 保养计划-计划执行预览
export const getPlanPreview = (data) => {
  return request({
    url: `/inspection/plan/getPlanPreview`,
    method: "get",
    params: data
  });
};

//巡检保养上传结果
export function addResultRequest(data) {
  return request({
    url: "/flowable/pollBasicInfo/update",
    method: "put",
    data
  });
}

// 完成任务
export function complete(data) {
  return request({
    url: "/flowable/task/complete",
    method: "post",
    data: data
  });
}

// 下一节点
export function getNextFlowNode(data) {
  return request({
    url: "/flowable/task/nextFlowNode",
    method: "post",
    data: data
  });
}

// 驳回任务
export function rejectTask(data) {
  return request({
    url: "/flowable/task/reject",
    method: "post",
    data: data
  });
}

/*
  根据ID查询设备详情
*/
export function getDeviceInfo(id) {
  return request({
    url: `/device/list/getDeviceInfo/${id}`,
    method: "get"
  });
}

// 转办任务
export function switchTask(data) {
  return request({
    url: "/flowable/task/assignTask",
    method: "post",
    data: data
  });
}