import request from "@/utils/request";

//查询根据用户查询oss列表
export const queryOssListRequestByUser = (query) => {
	return request({
		url: '/system/oss/app/list',
		method: 'get',
		params: query
	})
}

//查询所有oss列表
export const queryAllOssListRequest = (query) => {
	return request({
		// url: '/system/oss/list',
		url: '/regulation/queryRegulation',
		method: 'get',
		params: query
	})
}

//删除oss对象
export const delOssRequest = (ossId) => {
	return request({
		url: '/system/oss/' + ossId,
		method: 'delete'
	})
}
export const getApkUpdate = (query) => {
	return request({
		url: '/system/version/getEnableVersion',
		// url:'/regulation/queryRegulation',
		method: 'get',
		params: query
	})
}