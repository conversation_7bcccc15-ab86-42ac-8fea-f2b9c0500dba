import request from '@/utils/request';
//绑定门岗

export function bindingGate(areaId) {
  return request({
    url: `/passCheckInfo/bindingGate/${areaId}`,
    method: 'post',
  });
}
//获取已绑定门岗
export function getGate() {
  return request({
    url: '/passCheckInfo/getGate',
    method: 'get',
  });
}
// 门岗核验-今日通行列表
export function getPassList(params) {
  return request({
    url: '/passCheckInfo/passList',
    method: 'get',
    params,
  });
}
//通行证列表详情
export function getPassInfo(passId) {
  return request({
    url: `/passCheckInfo/passInfo/${passId}`,
    method: 'get',
  });
}
//验证二维码可行性
export function checkPassIsValid(data) {
  return request({
    url: '/passCheckInfo/checkPassIsValid',
    method: 'get',
    params: data,
  });
}
