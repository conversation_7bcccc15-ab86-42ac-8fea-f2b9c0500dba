import request from '@/utils/request'

// 登录方法
// export function login(username, password, code, uuid) {
// 	const data = {
// 		username,
// 		password,
// 		code,
// 		uuid
// 	}
// 	return request({
// 		'url': '/appLogin',
// 		headers: {
// 			isToken: false
// 		},
// 		'method': 'post',
// 		'data': data
// 	})
// }
export function login(phonenumber, smsCode) {
	const data = {
		phonenumber,
		smsCode
	}
	return request({
		'url': '/appSmsLogin',
		headers: {
			isToken: false
		},
		'method': 'post',
		'data': data
	})
}

// 注册方法
export function register(data) {
	return request({
		url: '/register',
		headers: {
			isToken: false
		},
		method: 'post',
		data: data
	})
}

// 获取用户详细信息
export function getInfo() {
	return request({
		'url': '/getInfo',
		'method': 'get'
	})
}

// 退出方法
export function logout() {
	return request({
		'url': '/logout',
		'method': 'post'
	})
}

// 获取验证码
export function getCodeImg() {
	return request({
		'url': '/captchaImage',
		headers: {
			isToken: false
		},
		method: 'get',
		timeout: 20000
	})
}

// 字典查询
export const getDictionaryQueries = (query) => {
	return request({
		url: `/system/dict/data/type/${query}`,
		method: "get"
	});
};

// app客户端绑定用户
export function appBoundUser(data) {
	return request({
		url: "/updateUserClient",
		method: "post",
		data: data
	});
}
// app验证码登录
export function getVerification(data) {
	return request({
		url: "/getAppSmsCode",
		method: "post",
		params: data
	});
}

// 获取图形验证码
export function getImgeCodeImg() {
	return request({
		url: '/smsCaptchaImage',
		method: 'get',
		timeout: 20000
	})
}