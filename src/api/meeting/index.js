import request from '@/utils/request'

// 查询会议记录
export const queryMeetingMinutesList = (params) => {
  return request({
    url: '/meeting/meetingRecordQuery',
    params
  })
}

// 取消会议预约
export const cancelReservation = (meetingId) => {
  return request({
    method: 'POST',
    url: `/meeting/cancel/${meetingId}`
  })
}

// 获取所有人的会议记录
export function getReservedTime(data) {
  return request({
    url: `/meeting/getReservedTime`,
    method: 'get',
    params: data
  })
}

// 查询会议室列表
export function getMeetingList(data) {
  return request({
    url: `/meetingRoom/list`,
    method: 'get',
    params: data
  })
}

// 参会人数据
export function gainUserByType(query) {
  return request({
    url: '/system/user/selectUserListByType',
    method: 'get',
    params: query
  })
}

// 新增预定会议记录
export function reserve(data) {
  return request({
    url: `/meeting/reserve`,
    method: 'post',
    data
  })
}

// 修改预定会议记录
export function update(data) {
  return request({
    url: `/meeting/update`,
    method: 'post',
    data
  })
}

// 查看会议室
export const queryRoomDetailRequest = roomId => {
  return request({
    url: `/meetingRoom/${roomId}`
  });
};

// 编辑会议室
export const updateMeetingRoom = data => {
  return request({
    method: "POST",
    url: "/meetingRoom/update",
    data
  });
};

// 根据设备id查询当前用户有没有该设备的待办工单
export function getTodoListByDeviceId(deviceId) {
  let query = {
    deviceId
  }
  return request({
    url: '/flowable/task/todoListByDeviceId',
    method: 'get',
    params: query
  })
}


