import request from "@/utils/request";

// 查询我的车辆
export const getMyCar = () => {
  return request({
    url: `/dispatch/car/getMyCar`,
  });
};

// 绑定/解绑我的车辆
export const bindMyCar = (data) => {
  return request({
    url: `/dispatch/car/bindMyCar`,
    method: "post",
    data,
  });
};

//用车申请记录
export const queryApplyCarRecordRequest = (query) => {
  return request({
    url: "/dispatch/record/apply",
    params: query,
  });
};

//用车记录详情
export const queryApplyCarDetailRequest = (id) => {
  return request({
    url: `/dispatch/record/apply/${id}`,
  });
};
//作业车辆-绑定历史
export const carBindList = (data) => {
  return request({
    url: `/dispatch/car/bindList`,
    method: "get",
    data,
  });
};