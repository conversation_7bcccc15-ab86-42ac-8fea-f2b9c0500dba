import request from "@/utils/request";

// 获取公交路线
export const getBusRoute = () => {
	return request({
		url: `/bus/route`,
		method: "get"
	});
}

// 园区公交排班
export const getBusScheduling = (routeId) => {
	return request({
		url: `/bus/scheduling/${routeId}`,
		method: "get"
	});
}
// 园区公交排班    
export const getBusInfo = () => {
	return request({
		url: `/bus/info`,
		method: "get"
	});
}

//作业路线列表
export const queryWorkLinesRequest = query => {
	return request({
		url: "/bus/route",
		params: query
	});
};
//app列表查询
export const getAppList = (query) => {
	return request({
		url: `/park/bus/appList`,
		method: "get",
		params: query
	});
}
//园内公交详情
export const getAppDetail = (taskId) => {
	return request({
		url: `/park/bus/appDetail?taskId=${taskId}`,
		method: "get",
	});
}
//查看公交任务信息
export const getBusTaskDetail = (taskId) => {
	return request({
		url: `/bus/task/${taskId}`,
		method: "get",
	});
}
//查看公交历史轨迹
export const getBusHistoryTrack = (taskId) => {
	return request({
		url: `/gps/getDeviceTrack/${taskId}`,
		method: "get",
	});
}
//获取实时点位
export const getBusPositioning = (taskId) => {
	return request({
		url: `/gps/geCarPositioning/${taskId}`,
		method: "get",
	});
}

