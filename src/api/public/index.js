import request from "@/utils/request";

export const deleteImgRequest = ossIds => {
	return request({
		url: `/system/oss/${ossIds}`,
		method: "delete"
	});
};

//查询设备列表
export const queryDeviceListRequest = (query) => {
	return request({
		url: "/device/list/query",
		method: "get",
		params: query
	});
}

//省份字典.
export const queryProvinceDictRequest = () => {
  return request({
    url: "/system/dict/data/type/uniops_province"
  });
};