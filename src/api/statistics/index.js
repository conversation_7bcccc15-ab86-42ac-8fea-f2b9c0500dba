import request from "@/utils/request";

//设备类型分类
export const queryTypeEchartsRequest = typeId => {
  return request({
    url: "/statistics/device/deviceTypeStatistics",
    params: typeId
  });
};

//设备性质分类
export const qureyNatureEchartsRequest = () => {
  return request({
    url: "/statistics/device/deviceNatureStatistics"
  });
};

//告警趋势统计
export const queryAlarmTrendEchartsRequest = dateType => {
  return request({
    url: "/statistics/device/incidentAlarmTendency",
    params: dateType
  });
};

//告警事件分类
export const queryAlarmEventTypeRequest = dateType => {
  return request({
    url: "/statistics/alarm/getAlarmClassification",
    params: dateType
  });
};

//告警趋势
export const queryAlarmTrendRequest = params => {
  return request({
    url: "/statistics/alarm/getAlarmTrend",
    params
  });
};

//告警区域
export const queryAlarmAreaRequest = dateType => {
  return request({
    url: "/statistics/alarm/getAreaAlarmRanking",
    params: dateType
  });
};

//工单数量趋势
export const getOrderNumStatistics = queryData => {
  return request({
    url: "/statistics/order/orderNumStatistics",
    params: queryData
  });
};

// 工单类型分布
export const getOrderTypeStatistics = queryData => {
  return request({
    url: "/statistics/order/orderTypeStatistics",
    params: queryData
  });
};

// 工单状态统计
export const getOrderStatusStatistics = queryData => {
  return request({
    url: "/statistics/order/orderStatusStatistics",
    params: queryData
  });
};

// 巡检记录趋势
export const getPollRecordStatistics = queryData => {
  return request({
    url: "/statistics/poll/pollRecordStatistics",
    params: queryData
  });
};

// 巡检模板设备数量分布
export const getPollTemplateDeviceRanking = queryData => {
  return request({
    url: "/statistics/poll/pollTemplateDeviceRanking",
    params: queryData
  });
};

// 设备类型巡检次数分布
export const getDeviceTypePollStatistics = queryData => {
  return request({
    url: "/statistics/poll/deviceTypePollStatistics",
    params: queryData
  });
};

// 巡检时间分布
export const getPollTimeStatistics = queryData => {
  return request({
    url: "/statistics/poll/pollTimeStatistics",
    params: queryData
  });
};

// 保养记录趋势
export const getUpkeepRecordStatistics = queryData => {
  return request({
    url: "/statistics/upkeep/upkeepRecordStatistics",
    params: queryData
  });
};

// 保养模板设备数量分布
export const getUpkeepTemplateDeviceRanking = queryData => {
  return request({
    url: "/statistics/upkeep/upkeepTemplateDeviceRanking",
    params: queryData
  });
};

// 设备类型保养次数分布
export const getDeviceTypeUpkeepStatistics = queryData => {
  return request({
    url: "/statistics/upkeep/deviceTypeUpkeepStatistics",
    params: queryData
  });
};

// 保养时间分布
export const getUpkeepTimeStatistics = queryData => {
  return request({
    url: "/statistics/upkeep/upkeepTimeStatistics",
    params: queryData
  });
};