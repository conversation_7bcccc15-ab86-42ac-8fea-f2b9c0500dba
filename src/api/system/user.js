import upload from '@/utils/upload';
import request from '@/utils/request';

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  };
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get',
  });
}

// 用户信息
export const getUserInfo = (userId) => {
  return request({
    url: `/system/user/${userId}`,
  });
};
// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return upload({
    url: '/system/user/profile/avatar',
    name: data.name,
    filePath: data.filePath,
  });
}

// 工作组树状结构
export function getDeptTree() {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
  });
}
// 获取门岗类型区域树
export function getGateGuardTree() {
  return request({
    url: '/area/getGateAreaTree',
    method: 'get',
  });
}

// 工作组成员通信信息
export const getUsersList = (query) => {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query,
  });
};

// 发起工单
export const getTodoList = () => {
  return request({
    url: `/flowable/task/todoList`,
  });
};

// 已办工单
export const getFinishedList = () => {
  return request({
    url: `/flowable/task/finishedList`,
  });
};

// 获取消息列表
export const getNoticeList = (data) => {
  return request({
    url: `/system/notice/list`,
    params: data,
  });
};

// 消息中心修改消息 未读-》已读
export function updateReadStatus(data) {
  return request({
    url: '/system/notice/updateRead',
    method: 'put',
    data: data,
  });
}

// 告警类详情
export const getAlarmInfo = (alarmId) => {
  return request({
    url: `/incidentAlarm/${alarmId}`,
  });
};

// 查询公告详细
export function getNoticeInfo(noticeId, query) {
  return request({
    url: '/notice/getInfo/' + noticeId,
    method: 'get',
    params: query,
  });
}

// 获取人脸图片
export function getFaceImage() {
  return request({
    url: '/transitAuth/getFaceImage',
    method: 'get',
  });
}
//设置人脸照片
export const setFaceImage = (data) => {
  return request({
    url: `/transitAuth/setFaceImage`,
    method: 'POST',
    data,
  });
};

// 获取消息通知列表

export const getNoticeListAll = (query) => {
  return request({
    url: '/system/notice/listAll',
    method: 'get',
    params: query,
  });
};