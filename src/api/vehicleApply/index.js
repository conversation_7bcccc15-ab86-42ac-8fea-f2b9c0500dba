import request from '@/utils/request';

//获取车辆路线站点（区域）
export function queryVehicleStationRequest() {
  return request({
    url: '/area/loadTree',
  });
}
//获取区域门岗
export function getGateAreaTree() {
  return request({
    url: '/area/getGateAreaTree',
    method: 'get',
    params: { areaType: 5 },
  });
}
//申请用车
export function applyVehicleRequest(data) {
  return request({
    url: '/dispatch/appointment/add',
    method: 'POST',
    data,
  });
}
