import request from '@/utils/request';
//获取作业类型列表
export const getWorkTypeList = (query) => {
  return request({
    url: `/dispatch/kind/list`,
    method: 'get',
    params: query,
  });
};
//获取作业人员列表
export const getWorkerList = (taskId) => {
  return request({
    url: `/dispatch/employee/currentGroupEmployee/${taskId}`,
    method: 'get',
  });
};

/**
 * @@param {作业任务}
 */
//获取作业任务列表
export const queryWorkTaskRequest = (path, query) => {
  return request({
    url: path + 'list',
    params: query,
  });
};

//获取任务详情
export const queryWorkDetailRequest = (path, id) => {
  return request({
    url: path + id,
  });
};

//任务转移
export const transferWorkRequest = (path, data) => {
  return request({
    url: `/dispatch/${path}/task/transfer`,
    method: 'POST',
    data,
  });
};

//任务分配
export const assignTasksRequest = (path, data) => {
  return request({
    url: `/dispatch/${path}/task/allocate`,
    method: 'POST',
    data,
  });
};

//取消任务
export const cancelTaskRequest = (path, data) => {
  return request({
    url: `/dispatch/${path}/task/cancel`,
    method: 'POST',
    data,
  });
};

/**
 * @@param {我的作业}
 */
//我的作业列表
// export const queryMyWorkListRequest = (path, query) => {
//     return request({
//         url: `/myTask/${path}/list`,
//         params: query
//     })
// }

//我的作业详情
// export const queryMyWorkDetailRequest = (path, taskId) => {
//     return request({
//         url: `/myTask/${path}/${taskId}`
//     })
// }

//开始作业
export const startMyWorkRequest = (path, data) => {
  return request({
    url: path + 'start',
    method: 'POST',
    data,
  });
};

/**
 * @@param {作业记录}
 */
//作业记录列表
export const queryWorkRecordListRequest = (path, query) => {
  return request({
    url: path + 'task',
    params: query,
  });
};
//作业记录详情
export const queryWorkRecordDetailRequest = (path, id) => {
  return request({
    url: path + 'taskDetail/' + id,
  });
};
//预约任务-申请用车记录
export const getApplyTaskRecordList = (query) => {
  return request({
    url: `/dispatch/record/apply`,
    method: 'get',
    params: query,
  });
};
//预约任务-结束任务
export const endTask = (data) => {
  return request({
    url: `/dispatch/appointment/task/endTask`,
    method: 'POST',
    data,
  });
};
//计划任务-结束任务
export const planEndTask= (data) => {
  return request({
    url: `/dispatch/plan/task/endTask`,
    method: 'POST',
    data,
  });
};
//预约任务-任务详情

export const getReservationTaskDetail = (taskId) => {
  return request({
    url: `/dispatch/appointment/taskDetail/${taskId}`,
    method: 'get',
  });
};
// 用车记录详情

export const getAppointmentTaskDetail = (taskId) => {
  return request({
    url: `/dispatch/appointment/task/${taskId}`,
    method: 'get',
  });
};
//作业计划-计划预览
export const workPlanePreview = (data) => {
  return request({
    url: `/dispatch/plan/preview`,
    method: 'get',
    data,
  });
};
// 预约任务接单
export const receiveOrder = (taskId) => {
  return request({
    url: `/myTask/appointment/receive/${taskId}`,
    method: 'POST',
  });
};
// 计划任务接单
export const receivePlanOrder = (taskId) => {
  return request({
    url: `/myTask/plan/receive/${taskId}`,
    method: 'POST',
  });
};
