import modules from './modules/index.js'
import Vue from 'vue'
import Router from 'uni-simple-router'
import store from '@/store/index.js'

Vue.use(Router)
//初始化
const router = new Router({
 APP: {
  holdTabbar: false //默认true
 },
 h5: {
  vueRouterDev: false, //完全使用vue-router开发 默认 false  
 },
 routes: [...modules] //路由表
});

//全局路由前置守卫
router.beforeEach((to, from, next) => {
 // 首先判断是否存在路由信息
 //不存在就先调用接口得到数据
   //具体内容可以参照上文的方案设计内容
})
// 全局路由后置守卫
router.afterEach((to, from) => {})
export default router;