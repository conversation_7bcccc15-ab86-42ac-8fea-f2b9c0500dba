<template>
    <view class="">
        <u-popup :show="popupShow" mode="top" :closeOnClickOverlay="true" @close="$emit('update:popupShow',false)"
            :customStyle="{padding:'20rpx'}">
            <slot name="filter"></slot>
            <view class="btn-container">
                <u-button text="重置" style="width: 20%;margin-right: 20rpx;" @click="$emit('resetHandle')" />
                <u-button type="primary" text="确定" style="width: 20%;" @click="$emit('confirm')" />
            </view>
        </u-popup>
    </view>
</template>

<script>
    export default {
        name: "Name",
        props: {
            popupShow: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {};
        },
        onLoad() {},
        onShow() {},
        methods: {}
    };
</script>

<style lang="scss" scoped>
	::v-deep .u-button {
		margin: unset
	}

	.btn-container {
		display: flex;
		justify-content: flex-end;
	}
</style>