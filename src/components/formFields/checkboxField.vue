<template>
  <zxz-uni-data-select
    v-model="modelValue"
    :localdata="radiolistCop"
    multiple
    collapse-tags
    :collapse-tags-num="2"
    :clear="false"
    class="custom-zxz-select"
  />
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      modelValue: [],
      radiolist: ['是', '否'],
      radiolistCop: []
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      let array = [];
      newValue.forEach(element => {
        array.push(this.radiolist[element]);
      });
      this.$emit(
        'update:modelValue',
        JSON.stringify(array),
        this.fieldProps.fieldCode
      );
    },
  },
  created() {
    if (this.fieldProps.selectOption) {
      let list = this.fieldProps.selectOption.split(',');
      this.radiolist = list;
      this.radiolistCop = list.map((item, index) => {
        let obj = {
          value: index,
          text: item
        }
        return obj
      });
    }
  },
};
</script>

<style lang="scss" scoped>
</style>
