<template>
  <zxz-uni-data-select
    v-model="modelValue"
    :localdata="radiolistCop"
    :clear="false"
  />
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      modelValue: '',
      radiolist: ['是', '否'],
      radiolistCop: []
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      this.$emit('update:modelValue', this.radiolist[newValue], this.fieldProps.fieldCode);
    },
  },
  created() {
    if (this.fieldProps.selectOption) {
      let list = this.fieldProps.selectOption.split(',');
      this.radiolist = list;
      this.radiolistCop = list.map((item, index) => {
        let obj = {
          value: index,
          text: item
        }
        return obj
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.u-radio {
  margin-right: 10px;
}
</style>
