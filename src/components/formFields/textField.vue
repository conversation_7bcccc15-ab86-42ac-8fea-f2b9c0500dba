<template>
   <u--input
      :placeholder="fieldProps.fieldRemark"
      maxlength="50"
      v-model="modelValue"
    >
  </u--input>
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {}
    },
  },
  data() {
		return {
      modelValue: ''
    }
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      this.$emit('update:modelValue', newValue, this.fieldProps.fieldCode);
    }
  },
};
</script>