<template>
	<view class="publicList">
		<u-list v-if="listData.length" @scrolltolower="scrolltolower">
			<u-list-item v-for="(task, index) in listData" :key="index" class="listCardItem">
				<uni-card
					@click="clickHandle(task)"
					margin="5px 0"
					:class="cardIcon ? 'listCard listCardIcon' : 'listCard'"
					:spacing="cardIcon ? '0 20px 0 13px' : '0 13px'"
					padding="0"
				>
					<template slot="title">
						<slot name="cardTitle"></slot>
					</template>

					<u-cell v-for="(item, i) in labelColumns" :key="i" class="listCardCell">
						<view slot="label" :class="item.iconSrc ? 'listCardCellBox listIconCardCellBox' : 'listCardCellBox'">
							<template v-if="!transform || !transform.includes(item.prop)">
								<view class="listCardCellRow">
									<view :class="item.iconSrc ? 'publicListIconBox listCardCellColLeft' : 'listCardCellColLeft'">
										<u--image v-if="item.iconSrc" class="publicListIcon" :src="globalConfig.iconImgPrefix + item.iconSrc"></u--image>
										<u-text class="listCardCellLabel" lineHeight="30rpx" :block="true" :text="`${item.label}：`" />
									</view>
									<view class="listCardCellColRight">
										<u-text lineHeight="30rpx" :block="true" :text="`${task[item.prop] || '暂无'}`" />
									</view>
								</view>
								<!-- <u-row class="listCardCellRow">
									<u-col :span="cardIcon ? 5 : 4" :class="item.iconSrc ? 'publicListIconBox listCardCellColLeft' : 'listCardCellColLeft'">
										<u--image v-if="item.iconSrc" class="publicListIcon" :src="globalConfig.iconImgPrefix + item.iconSrc"></u--image>
										<u-text lineHeight="30rpx" :block="true" :text="`${item.label}：`" />
									</u-col>
									<u-col :span="cardIcon ? 7 : 8" class="listCardCellColRight">
										<u-text lineHeight="30rpx" :block="true" :text="`${task[item.prop] || '暂无'}`" />
									</u-col>
								</u-row> -->
							</template>

							<slot v-else name="columnInfo" :row="task" :prop="item.prop"></slot>
						</view>
					</u-cell>

					<template slot="actions">
						<slot name="cardActions" :row="task"></slot>
					</template>
				</uni-card>
			</u-list-item>
		</u-list>

		<u-empty width="120" height="120" marginTop="20" v-else mode="list" icon="/static/images/iconImg/empty.png" />
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
export default {
	name: 'Name',
	props: {
		listData: {
			type: Array,
			default: () => []
		},
		labelColumns: {
			type: Array,
			default: () => []
		},
		transform: {
			type: [String, Array],
			default: ''
		},
		queryData: {
			type: Object,
			default: () => {}
		},
		totalPage: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			globalConfig: getApp().globalData.config,
			cardIcon: ''
		};
	},
	mounted() {},
	onShow() {},
	methods: {
		scrolltolower() {
			this.queryData.pageNum++;
			if (this.queryData.pageNum > this.totalPage) {
				this.$refs.uToast.show(this.$setToastMsg({
					message:'已加载全部',
					type:'success'
				}));
				return;
			}
			this.$emit('getList');
		},
		clickHandle(task) {
			this.$emit('cardItemClick', task);
		}
	},
	watch: {
		labelColumns: {
			handler(newVal, oldVal) {
				if (newVal && newVal.length > 0) {
					this.cardIcon = newVal.some((item) => {
						return item.iconSrc;
					});
				}
			},
			// deep: true
			immediate: true
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .u-cell__body {
	padding: unset;
}
</style>
