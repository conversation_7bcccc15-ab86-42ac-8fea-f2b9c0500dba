<template>
    <view class="">
        <u-popup :show="show" mode="right" :customStyle="{ padding: '0.9375rem' }" zIndex="990" oIndex="990">
            <view class="form-container">
                
                <u-form labelPosition="left" ref="uForm" :mode="formData" labelWidth="4.375rem">
                    <u-form-item v-for="(item, index) in formLabels" :key="index" :label="item.label" :borderBottom="true" :prop="item.prop">
                        <u-input v-if="!transform || !transform.includes(item.prop)" v-model="formData[item.prop]" placeholder="请输入内容" border="surround" :readonly="true" />
                        <slot v-else name="formItem" :data="formData[item.prop]" :prop="item.prop"></slot>
                    </u-form-item>
                </u-form>
                
                <slot name="footer"></slot>

                <u-button class="cancel_btn" type="primary" :plain="true" text="取消" @click="close" />
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    name: 'PublicPopup',
    props: {
        formLabels: {
            type: Array,
            default: () => []
        },
        transform: {
            type: [String, Array],
            default: ''
        },
        formData:{
            type:Object,
            default:() => {}
        }
    },
    data() {
        return {
            show: false
        };
    },

    mounted() {},
    onShow() {},
    methods: {
        close() {
            this.formLabels.forEach(item => {
                this.formData[item.prop] = ''
            })
            this.show = false;
        },
        open() {
            this.show = true;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-transition {
    width: 100vw;
}
.form-container {
    height: 100vh;
    overflow: auto;
    padding-bottom: 4.375rem;
}
.cancel_btn {
    position: fixed;
    bottom: 0;
    left: 0;
}

.u-swiper {
    width: 100%;
}
</style>
