<template>
  <view class="render-box">
    <u-icon
      name="close-circle"
      class="closeIcon"
      @tap="stopOverScan"
      size="45"
    ></u-icon>
    <view class="render" id="render"> </view>
    <view :class="this.isScaning ? 'box' : ''"></view>
  </view>
</template>
<script>
import { Html5Qrcode } from 'html5-qrcode';
export default {
  data() {
    return {
      html5QrCode: null,
      isScaning: false,
      sacnLine: null,
      isFinished: false,
    };
  },
  methods: {
    startScan() {
      try {
        Html5Qrcode.getCameras().then((devices) => {
          if (devices?.length) {
            this.html5QrCode = new Html5Qrcode('render');
            this.isFinished = false;

            this.openScanLine();
            this.html5QrCode.start(
              { facingMode: 'environment' },
              { fps: 25, qrbox: { width: 320, height: 280 } },
              (decodeText, decodeResult) => {
                this.isFinished = true;
                this.$emit('upScaning', JSON.parse(decodeText));
                this.html5QrCode && this.stopScan();
              },
              (err) => {}
            );
            this.requestFrame();
          }
        });
      } catch (error) {}
    },
    async requestFrame() {
      await new Promise((resolve) => setTimeout(resolve, 30000));

      if (!this.isFinished && this.html5QrCode) {
        this.$emit('upScaning', {
          status: 'error',
          msg: '扫码超时，请重新扫码',
        });
        this.stopScan();
      }
    },
    stopOverScan() {
      this.stopScan();
      this.$emit('stopScan');
    },
    stopScan() {
      this.sacnLine = null;
      this.isScaning = false;
      this.html5QrCode && this.html5QrCode.stop();
      this.html5QrCode = null;
    },
    openScanLine() {
      this.sacnLine = setTimeout(() => {
        this.isScaning = true;
      }, 1000);
    },
    scanBefore() {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        this.sacnLine = null;
        this.startScan();
      } else {
        this.$modal.msg('getUserMedia not supported on your browser!');
      }
    },
  },
};
</script>
<style scoped>
.render-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.closeIcon {
  position: absolute;
  right: 45rpx;
  top: 120rpx;
  z-index: 99;
}
.render {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.box {
  height: var(--scan-video-height);
  top: 50%;
  z-index: 1;
  left: 50%;
  width: 320px;
  transform: translate(-50%, -50%);
  position: absolute;
}
.box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
  animation: scans 5s infinite;
}
@keyframes scans {
  0% {
    margin-top: calc(0px - 60px);
    background: linear-gradient(#0000 0%, rgb(104, 185, 229) 100%);
  }
  49% {
    margin-top: var(--scan-video-height);
    background: linear-gradient(#0000 0%, rgb(104, 185, 229) 100%);
  }
  50% {
    margin-top: var(--scan-video-height);
    background: linear-gradient(rgb(104, 185, 229) 0%, #0000 100%);
  }
  100% {
    margin-top: 0px;
    background: linear-gradient(rgb(104, 185, 229) 0%, #0000 100%);
  }
}
</style>
