<template>
  <view v-if="shouldShowNavbar">
    <u-navbar
      height="44"
      :bgColor="bgColor"
      @leftClick="leftClick"
      :titleStyle="titleStyle"
      :title="title"
      :autoBack="autoBack"
      :border="true"
      :safeAreaInsetTop="true"
      :placeholder="true"
    >
      <view slot="left">
        <!-- <u-icon color="#fff" name="arrow-left" size="19" v-if="autoBack"></u-icon> -->
        <uni-icons
          color="#fff"
          type="left"
          size="19"
          v-if="autoBack"
        ></uni-icons>
        <u--text :text="leftText" v-if="autoBack"></u--text>
        <slot name="title-left" v-if="autoBack"></slot>
        <slot name="title-icon"></slot>
      </view>
      <view slot="right">
        <slot name="title-right"></slot>
        <view
          v-if="hasFilter"
          class="flex filter-box"
          style="color: #fff"
          @click="filterHandle"
        >
          <u--image
            class="top-filter"
            :src="globalConfig.iconImgPrefix + 'filter.png'"
          ></u--image>
          <span class="filter-text">筛选</span>
        </view>
      </view>
    </u-navbar>
  </view>
</template>

<script>
import indexBackgroundImage from '@/static/images/banner/banner.png';
export default {
  data() {
    return {
      globalConfig: getApp().globalData.config,
      titleStyle: {
        color: '#fff',
      },
      isWxBrowser: false,
      isMiniProgram: false,
      miniProgramEnv: false,
    };
  },
  props: {
    title: String,
    autoBack: {
      type: Boolean,
      default: true,
    },

    bgColor: {
      type: String,
      default: '#fff',
    },
    leftText: {
      type: String,
      default: '返回',
    },
    hasFilter: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    shouldShowNavbar() {
      // 如果是微信小程序环境，不显示导航栏
      if (this.isMiniProgram || this.miniProgramEnv) {
        return false;
      }
      // 如果是微信浏览器环境，不显示导航栏
      if (this.isWxBrowser) {
        return false;
      }
      // 其他环境显示导航栏
      return true;
    }
  },
  created() {
    // 判断是否是微信小程序环境 - 方式1
    // #ifdef MP-WEIXIN
    this.isMiniProgram = true;
    // #endif

    // 判断是否是微信小程序环境 - 方式2
    try {
      const systemInfo = uni.getSystemInfoSync();
      // 微信小程序环境会包含 miniProgram 字段
      if (systemInfo.environment === 'miniProgram' || systemInfo.platform === 'mp-weixin') {
        this.miniProgramEnv = true;
      }
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }

    // 判断是否是微信浏览器环境
    // #ifdef H5
    const ua = navigator.userAgent.toLowerCase();
    this.isWxBrowser = ua.indexOf('micromessenger') !== -1;
    // #endif
  },
  methods: {
    leftClick() {
      this.$emit('leftClick');
    },
    filterHandle() {
      this.$emit('filterHandle');
    },
  },
};
</script>

<style lang="scss" scoped>
.title-text {
  font-weight: 700;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.top-bar {
  padding: 7px 3px;
  margin: 0 !important;
}
.u-navbar__content__left {
  uni-view {
    display: flex;
  }
}
::v-deep .u-border-bottom {
  // border-bottom: 1px solid #ebeef5;
  border-bottom: none;
  // box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 0px 0px;
}
</style>
