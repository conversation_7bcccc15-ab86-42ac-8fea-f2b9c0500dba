// 应用全局配置
module.exports = {
  // baseUrl: 'https://vue.ruoyi.vip/prod-api',
  // baseUrl: 'http://devops.smartcloud.com:10082/ztf-zixun/cfcec-frontend/dev/prod-api',
  // baseUrl: 'http://**************:10090/c-park-cfcec-web/prod-api',
  baseUrl: `${
    process.env.NODE_ENV == 'development'
      ? 'http://devops.smartcloud.com:10082/ztf-zixun/jsyp-backend/dev'
      : '/ztf-zixun/jsyp-frontend/dev/prod-api'
  }`,
  // baseUrl:"http://**************:9010",
  // baseUrl:"http://**************:9006",
  // baseUrl: "http://**************:9006",
  // 应用信息
  appInfo: {
    // 应用名称
    name: '今世缘APP',
    // 应用版本
    version: '1.1.0',
    // 应用logo`
    logo: '/static/logo.png',
    // 官方网站
    site_url: '',
    // 政策协议
    agreements: [
      {
        title: '隐私政策',
        url: '/static/html/privacy.html',
      },
      {
        title: '用户服务协议',
        url: '/static/html/service.html',
      },
    ],
    gomapUrl:
      process.env.NODE_ENV == 'development'
        ? 'http://localhost:3333/#/map'
        : 'http://devops.smartcloud.com:10082/ztf-zixun/jsyp-frontend-gis/dev/#/map',
  },
  //图标图片文件夹前缀
  iconImgPrefix: '/static/images/iconImg/',
  iconImgWork: '/static/images/workIcon/',
};
