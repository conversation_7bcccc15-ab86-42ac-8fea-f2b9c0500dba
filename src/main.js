import Vue from 'vue';
import App from './App';
import store from './store'; // store
import plugins from './plugins'; // plugins

import './permission'; // permission
import { setToastMsg } from './utils/common.js';
import { router, RouterMount } from './router'; //路径换成自己的
import directive from '@/directive';
// 动态加载腾讯地图SDK
const loadTencentMap = () => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src =
      'https://map.qq.com/api/gljs?v=1.exp&key=LTOBZ-UG6C3-U7U3N-RBHGG-B5IAS-ESBM3';
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// 在Vue实例创建前加载地图SDK
loadTencentMap().catch((err) => {
  console.error('腾讯地图SDK加载失败:', err);
});
import VueCompositionApi from '@vue/composition-api';

Vue.use(VueCompositionApi);

Vue.use(directive);

Vue.use(plugins);
Vue.use(router);

/**
 * uView
 */
import uView from 'uview-ui';
Vue.use(uView);
import PicPreview from './plugins/picPreview'; //picPreview
import { compressImageFile } from './utils/compressImage.js';
Vue.use(PicPreview);
Vue.config.productionTip = false;
Vue.prototype.$setToastMsg = setToastMsg;
Vue.prototype.$store = store;
Vue.prototype.$compressImageFile = compressImageFile;
App.mpType = 'app';

const app = new Vue({
  ...App,
});
//app.$mount();
// #ifdef H5
RouterMount(app, router, '#app');
// #endif

// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif






