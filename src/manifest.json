{"name": "今世缘移动端", "appid": "__UNI__295A076", "description": "", "versionName": "0.0.1", "versionCode": 1, "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Barcode": {}, "Camera": {}, "Push": {}, "Geolocation": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"idfa": false, "dSYMs": false, "privacyDescription": {"NSLocationWhenInUseUsageDescription": "需要获取您的位置信息用于地图导航", "NSLocationAlwaysUsageDescription": "需要获取您的位置信息用于地图导航"}}, "sdkConfigs": {"ad": {}, "push": {"unipush": {}}, "geolocation": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wxccd7e2a0911b3397", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "今世缘", "router": {"mode": "history", "base": "/ztf-zixun/jsyp-frontend-h5/dev/"}, "unipush": {"enable": false}, "sdkConfigs": {"maps": {"qqmap": {"key": "LTOBZ-UG6C3-U7U3N-RBHGG-B5IAS-ESBM3"}}}}}