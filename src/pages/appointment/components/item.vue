<template>
  <uni-card
    :title="cardTitle"
    @click="handleDetail"
    :is-shadow="true"
    shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
    spacing="12px"
    margin="12px 12px 0"
  >
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-16 text_scrolling">
          <text>{{ itemData.visitorName }}</text>
        </view>
        <u-tag
          size="mini"
          shape="circle"
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
        ></u-tag>
      </view>
      <view class="mt4"> </view>
    </template>

    <uni-row slot="actions">
      <view class="flex mb5 align_center space_b font-12" style="color: #666">
        <span>{{ itemData.visitTime }}</span>
        <span>{{ itemData.leaveTime }}</span>
      </view>
    </uni-row>
  </uni-card>
</template>

<script>
const stausMap = {
  0: {
    text: '已发起',
    color: '#fff',
    bgColor: '#f9ae3d',
  },

  1: {
    text: '已完成',
    color: '#fff',
    bgColor: '#aa001e',
  },
  2: {
    text: '已失效',
    color: '#fff',
    bgColor: '#909193',
  },
};
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: '',
      type: String,
    },
  },
  data() {
    return {};
  },
  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail');
    },
    queryStatus(val) {
      return stausMap[val];
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}

.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
.item_name {
  color: #666;
}

.text_scrolling {
  white-space: nowrap; /* 保持文字在一行内显示 */
  overflow-x: auto; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 75%; /* 设置容器宽度 */
}
</style>
