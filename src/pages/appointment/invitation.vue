<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card
        shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <u--form
          labelPosition="left"
          labelWidth="85"
          :model="userInfo"
          :rules="rules"
          ref="uForm"
        >
          <u-form-item
            label="预约人类型"
            prop="objId"
            required
            borderBottom
            @click="selectObj"
          >
            <u--input
              v-model="objId"
              placeholder="请选择预约人类型"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
            <!-- <uni-data-select
              v-model="userInfo.objId"
              :localdata="typeRange"
              placeholder="请选择预约人类型"
              @change="typeChange"
              border="none"
            ></uni-data-select> -->
          </u-form-item>
          <u-form-item
            label="预约人姓名"
            prop="visitorName"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写预约人姓名"
              maxlength="50"
              v-model="userInfo.visitorName"
              border="none"
              :disabled="invitationId"
              disabledColor="#FFFFFF"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="预约人手机"
            prop="visitorPhone"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写预约人手机"
              maxlength="11"
              v-model="userInfo.visitorPhone"
              :disabled="invitationId"
              border="none"
              disabledColor="#FFFFFF"
            ></u--input>
          </u-form-item>
          <u-form-item label="被访人姓名" prop="inviterName" borderBottom>
            <u--input
              placeholder="请填写被访人姓名"
              maxlength="50"
              v-model="userInfo.inviterName"
              disabled
              border="none"
              disabledColor="#FFFFFF"
            ></u--input>
          </u-form-item>
          <u-form-item label="被访人手机" prop="inviterPhone" borderBottom>
            <u--input
              placeholder="请填写被访人手机"
              maxlength="11"
              v-model="userInfo.inviterPhone"
              disabled
              border="none"
              disabledColor="#FFFFFF"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="来访时间"
            prop="visitTime"
            borderBottom
            @click="setVistorTime"
          >
            <u--input
              v-model="userInfo.visitTime"
              placeholder="请选择来访时间"
              border="none"
              readonly
            ></u--input>
          </u-form-item>
          <u-form-item
            label="离厂时间"
            prop="leaveTime"
            borderBottom
            @click="setLeaveTime"
          >
            <u--input
              v-model="userInfo.leaveTime"
              placeholder="请选择离厂时间"
              border="none"
              readonly
            ></u--input>
          </u-form-item>
          <u-form-item
            label="来访事由"
            prop="visitReason"
            required
            borderBottom
            @click="addReason"
          >
            <u--input
              v-model="userInfo.visitReason"
              placeholder="请选择来访事由"
              border="none"
              readonly
            ></u--input>
          </u-form-item>
        </u--form>
        <uni-row slot="actions" class="flex" v-if="!invitationId">
          <uni-col :span="10">
            <u-button
              type="info"
              text="取消"
              @click="cancelInvitation"
            ></u-button>
          </uni-col>
          <uni-col :span="4"> </uni-col>
          <uni-col :span="10">
            <u-button
              type="primary"
              text="发起邀约"
              @click="sendInvitation"
            ></u-button>
          </uni-col>
        </uni-row>
      </uni-card>
    </view>
    <u-action-sheet
      :actions="typeRange"
      :show="showObjType"
      cancelText="取消"
      @select="typeChange"
      @close="showObjType = false"
    ></u-action-sheet>
    <u-action-sheet
      :actions="visitReasonRange"
      :show="showVisitReason"
      cancelText="取消"
      @select="visitReasonChange"
      @close="showVisitReason = false"
    ></u-action-sheet>
    <u-datetime-picker
      :show="showVisitTime"
      mode="datetime"
      :minDate="minDate"
      @close="showVisitTime = false"
      @cancel="showVisitTime = false"
      @confirm="visitPanelConfirm"
    ></u-datetime-picker>
    <u-datetime-picker
      :show="showLeaveTime"
      mode="datetime"
      :minDate="minDate"
      @close="showLeaveTime = false"
      @cancel="showLeaveTime = false"
      @confirm="leavePanelConfirm"
    ></u-datetime-picker>
    <u-loading-page :loading="loading" loadingText="请稍后" />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
import {
  getEnabledAppointmentObjectList,
  getApplyInvitation,
  applyVisitReason,
  getInvitationInfo,
} from '@/api/appointment/index.js';
export default {
  name: 'Invitation',
  props: {},
  data() {
    return {
      globalConfig: getApp().globalData.config,
      loading: false,
      autoBack: true,
      leftText: '',
      showVisitTime: false,
      showLeaveTime: false,
      minDate: Number(new Date()),
      userInfo: {
        visitorName: '',
        visitorPhone: '',
        inviterName: '',
        inviterPhone: '',
        objId: '',
        visitTime: '',
        leaveTime: '',
        visitReason: '',
      },
      rules: {
        visitorName: {
          type: 'string',
          required: true,
          max: 50,
          message: '请填写预约人姓名',
          trigger: ['blur', 'change'],
        },
        visitorPhone: [
          {
            type: 'number',
            max: 11,
            required: true,
            message: '请填写正确的手机号',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],

        objId: {
          type: 'string',
          required: true,
          message: '请选择访客类型',
          trigger: ['change'],
        },
        visitReason: {
          type: 'string',
          required: true,
          message: '请选择来访事由',
          trigger: ['change'],
        },
      },
      typeRange: [],
      objId: '',
      showObjType: false,
      visitReasonRange: [],
      showVisitReason: false,
      title: '',
    };
  },
  onLoad(option) {
    if (this.$store.state.user) {
      let user = this.$store.state.user;
      this.userInfo.inviterName = user.name;
      this.userInfo.inviterPhone = user.phoneNumber;
    }
    this.title = option.title;
    this.invitationId = option.invitationId;
    !this.invitationId && this.getTypeList();
    !this.invitationId && this.getVisitList();
    this.invitationId && this.GetInfo();
  },
  onShow() {},
  methods: {
    visitPanelConfirm(e) {
      this.userInfo.visitTime = formatDate(e.value, 'yyyy-MM-dd hh:mm');
      this.showVisitTime = false;
    },
    leavePanelConfirm(e) {
      this.userInfo.leaveTime = formatDate(e.value, 'yyyy-MM-dd hh:mm');
      this.showLeaveTime = false;
    },
    cancelInvitation() {
      uni.navigateBack();
    },
    //选择预约人类型
    selectObj() {
      if (this.invitationId) return;
      this.hideKeyboard();
      this.showObjType = true;
    },
    //设置来访时间
    setVistorTime() {
      if (this.invitationId) return;
      this.hideKeyboard();
      this.showVisitTime = true;
    },
    //设置来访离开时间
    setLeaveTime() {
      if (this.invitationId) return;
      this.hideKeyboard();
      this.showLeaveTime = true;
    },
    //添加来访事由
    addReason() {
      if (this.invitationId) return;
      this.hideKeyboard();
      this.showVisitReason = true;
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    typeChange(obj) {
      this.objId = obj.name;
      this.userInfo.objId = obj.value;
      this.showObjType = false;
    },
    visitReasonChange(obj) {
      this.userInfo.visitReason = obj.name;
      this.showVisitReason = false;
    },
    async getTypeList() {
      try {
        const res = await getEnabledAppointmentObjectList();
        let newRes = res?.data;
        this.typeRange = newRes.map((item) => ({
          name: item.objName,
          value: item.objId,
        }));
      } catch (error) {}
    },
    async getVisitList() {
      try {
        const res = await applyVisitReason();
        let newRes = res?.data;
        this.visitReasonRange = newRes.map((item) => ({
          name: item.dictLabel,
          value: item.dictCode,
        }));
      } catch (error) {}
    },
    //获取邀约信息
    async GetInfo() {
      try {
        const res = await getInvitationInfo(this.invitationId);
        this.userInfo = res.data;
        this.objId = this.userInfo.objName;
      } catch (error) {}
    },
    sendInvitation() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          if (res) {
            this.loading = true;
            try {
              const rep = await getApplyInvitation(this.userInfo);
              if (rep.code === 200) {
                // this.$modal.msgSuccess('邀约成功');
                this.$refs.uToast.show(
                  this.$setToastMsg({
                    message: '邀约成功',
                    type: 'success',
                  })
                );
                setTimeout(() => {
                  uni.navigateBack();
                }, 1000);
              } else {
                // this.$modal.msgError('邀约失败');
                this.$refs.uToast.show(
                  this.$setToastMsg({
                    message: '邀约失败',
                    position: 'top',
                    type: 'error',
                  })
                );
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.middle-container {
  padding-top: 0;
}
.uni-card {
  overflow: scroll;
  height: 80%;
  ::v-deep .uni-card__content {
    height: 90%;
  }
}
</style>
