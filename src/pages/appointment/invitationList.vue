<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="navBarTitle"
      :autoBack="autoBack"
      leftText=""
    >
    </top-navbar>
    <view class="bgCard middle-container">
      <view class="show-search">
        <view style="border-radius: 100px; margin: 0 24rpx" class="flex shadow">
          <u-search
            :showAction="true"
            clearabled
            v-model="keyword"
            actionText="搜索"
            :animation="true"
            bgColor="#fff"
            @search="searchHandle"
            @custom="searchHandle"
            @clear="searchHandle"
          />
        </view>
      </view>
      <card-list
        class="card-list"
        v-if="tableData.length"
        :cardList="tableData"
        @scrolltolower="scrolltolower"
      >
        <template #default="slotProps">
          <item
            :itemData="slotProps.itemData"
            @handleDetail="cardItemClick(slotProps.itemData)"
          />
        </template>
      </card-list>
      <u-empty
        width="120"
        height="120"
        marginTop="20"
        v-else
        mode="list"
        icon="/static/images/iconImg/empty.png"
      />
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import CardList from '@/pages/components/cardList.vue';
import Item from './components/item.vue';
import { tansParams } from '@/utils/permission';
import { getInvitationList } from '@/api/appointment';
export default {
  name: 'Name',
  components: {
    CardList,
    Item,
  },
  data() {
    return {
      tableData: [],
      keyword: '',
      globalConfig: getApp().globalData.config,
      autoBack: true,
      queryData: {
        keyWords: '',
        pageNum: 1,
        pageSize: 10,
      },
      navBarTitle: '',
      totalPage: 0,
    };
  },

  onLoad(option) {
    this.navBarTitle = option.title;
  },
  onShow() {
    this.searchHandle();
  },
  methods: {
    async getApplyList() {
      try {
        const res = await getInvitationList(this.queryData);
        let taskData = {
          list: [],
          total: 0,
        };
        taskData.list = res.rows;
        taskData.total = res.total;

        this.totalPage = Math.ceil(taskData.total / this.queryData.pageSize);

        if (taskData.list.length) this.tableData.push(...taskData.list);
      } catch (err) {
        console.log(err);
      }
    },

    searchHandle(val) {
      this.tableData = [];
      this.queryData.keyWords = this.keyword;
      this.queryData.pageNum = 1;
      this.getApplyList();
    },

    scrolltolower() {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.getApplyList();
    },
    cardItemClick(info) {
      let query = {
        invitationId: info?.invitationId,
        title: '邀约',
      };

      uni.navigateTo({
        url: `/pages/appointment/invitation?${tansParams(query)}`,
      });
      return;
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}

.filter-image {
  flex: 1;
  display: flex;
  align-items: flex-end;
}
.middle-container {
  padding-top: 0;
}
.show-search {
  padding: 24rpx 0 0;
}

.shadow {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
::v-deep .u-cell__body {
  padding: unset;
}

::v-deep .u-tag-wrapper {
  flex-direction: unset;
}
.todoListPage .bgCard .u-list {
  height: calc(100vh - 90px) !important;
  ::v-deep .u-list-item:last-child {
    margin-bottom: 20px;
  }
}
</style>
