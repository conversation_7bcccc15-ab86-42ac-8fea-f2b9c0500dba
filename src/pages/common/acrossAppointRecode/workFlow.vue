<template>
  <view>
    <top-navbar
      bgColor="#AA001E"
      title="流转记录"
      :autoBack="true"
      leftText=""
      :safeAreaInsetTop="true"
    />

    <uni-card shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)" margin="12px 12px 0">
      <view class="flex aligns_center space_c">
        <u-steps
          :current="approvalCurrent"
          :direction="direction"
          class="approvalStep"
          v-if="approvalInfo.length > 0"
        >
          <u-steps-item
            :title="item.approvalNode"
            v-for="(item, index) in approvalInfo"
            :key="index"
            v-if="approvalInfo.length > 0"
          >
            <template slot="desc">
              <view class="approvalDesc">
                <view>审批人姓名：</view>
                <view>{{ item.approvalUserName }}</view>
              </view>
              <view class="approvalDesc">
                <view>审批人手机号：</view>
                <view>{{ item.approvalUserPhone }}</view>
              </view>
              <view class="approvalDesc">
                <view>审批意见：</view>
                <view>{{ item.approvalOpinion }}</view>
              </view>
              <view class="approvalDesc">
                <view>审批时间：</view>
                <view>{{ item.approvalTime }}</view>
              </view>
            </template>
          </u-steps-item>
        </u-steps>
        <u-empty
          width="120"
          height="120"
          marginTop="20"
          v-else
          mode="list"
          icon="/static/images/iconImg/empty.png"
        />
      </view>
    </uni-card>
  </view>
</template>

<script>
import { workflowHistory } from '@/api/aboutTask/index';

export default {
  props: {
    direction: {
      default: 'column',
      type: [String],
    },
  },
  data() {
    return {
      approvalInfo: [],
      approvalCurrent: 0,
    };
  },
  methods: {
    getFlowStatus(status) {
      switch (status) {
        case '0':
          return '进行中';
        case '1':
          return '审批通过';
        case '2':
          return '审批拒绝';
        default:
          return '未知状态';
      }
    },
    async init(data) {
      try {
        let params = {
          requestId: data.requestId ? data.requestId : '',
        };
        this.approvalInfo = [];
        const res = await workflowHistory(params);
        this.approvalInfo = res.data;
        this.approvalCurrent = this.approvalInfo.length;
      } catch (error) {
        console.log(error);
      }
      // if(this.taskStatus !='2' ) {
      //   this.busCurrent = null
      // }
      // if(data.busStations&&data.busStations.length>0 && this.taskStatus=='2') {
      //   data.busStations.forEach((item,index)=>{
      //     if(item.arrivalTime) {
      //       item.timeTxt = '到达时间：'
      //       item.tiemInfo = item.arrivalTime
      //     } else if(item.expectedArrivalTime){
      //       item.timeTxt = '预计到达时间：'
      //       item.tiemInfo = item.expectedArrivalTime
      //       this.busCurrent = index
      //     }
      //   })
      // }

      // // const targetIndex = data.busStations.findIndex(item => item.status == 0)
      // // if(targetIndex > -1) this.busCurrent = targetIndex
      // this.busTaskInfo = data.busStations;
    },
  },
  onLoad(options) {
    this.init(options);
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}

.homeBody {
  padding: 36rpx;
}
.approvalStepBox {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 120rpx;
}
.approvalDesc {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  uni-view:nth-child(1) {
    color: #999;
  }
}
.descFirst {
  margin-top: 10rpx;
}
</style>
