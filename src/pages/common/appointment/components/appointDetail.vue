<template>
  <view class="visitStyle">
    <!-- <u--text v-if="itemData.isChecked==='-1'" class="center-text" type="error" text="审核拒绝"></u--text>
    <u--text v-else-if="itemData.isChecked==='0'" class="center-text" type="warning" text="未审核"></u--text>
    <u--text v-else-if="itemData.isChecked==='1'" class="center-text" type="success" text="审核通过"></u--text>
    <view v-if="itemData.passQrCode" class="flex justify-center">
      <u--image :showLoading="true" :src="`data:image/png;base64,${itemData.passQrCode}`" width="160px" height="160px"></u--image>
    </view>
    <u--text v-if="itemData.isChecked==='1'" class="center-text" color="#AA001E" :text="itemData.appointmentCode"></u--text> -->
    <ContentGroup :labelList="vistorLabelList" :itemInfo="itemData" />
    <view class="u_avatar avatar-shadow">
      <u-avatar
        :src="itemData.visitorPhotoUrl"
        size="87rpx"
        @click="openPicPreview(itemData.visitorPhotoUrl)"
      ></u-avatar>
    </view>
  </view>
</template>

<script>
import ContentGroup from '@/pages/components/contentGroup.vue';
import { vistorLabelList } from '../config.js';
export default {
  name: 'AppointDetail',
  components: {
    ContentGroup,
  },
  props: {
    itemData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      disabledColor: '#ffffff',
      vistorLabelList,
    };
  },
  onLoad() {},
  onShow() {},
  onUnload() {
    this.$closePreview();
  },
  methods: {
    /* 打开图片组件*/
    openPicPreview(url) {
      this.$openPreview(url).then();
    },
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
}
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
}
.center-text {
  justify-content: center !important;
}
.visitStyle {
  position: relative;
}
.u_avatar {
  position: absolute;
  right: 16rpx;
  top: 44rpx;
}
</style>
