<template>
  	<view>
      <u--form
          labelPosition="left"
          labelWidth="85"
          :model="result"
          :rules="rules"
          ref="uForm"
      >
        <u-form-item
            label="审核结果"
            prop="isChecked"
            required
        >
          <uni-data-select
            v-model="result.isChecked"
            :localdata="typeRange"
            placeholder="请选择审核类型"
          ></uni-data-select>
        </u-form-item>
        <u-form-item
            label="审核意见"
            prop="approvalRemark"
        >
          <u--input
              placeholder="请填写审核意见"
              maxlength="50"
              v-model="result.approvalRemark"
          ></u--input>
        </u-form-item>
      </u--form>
    </view>
</template>

<script>
	export default {
			name: "ApprovalContent",
      components: {
      },
			props: {
			},
			data() {
					return {
            result: {
              isChecked: '',
              approvalRemark: ''
            },
            typeRange: [
              { value: '1', text: "审核通过" },
              { value: '-1', text: "审核拒绝" },
            ],
            rules: {
              isChecked: {
                type: 'string',
                required: true,
                message: '请选择审核结果',
                trigger: ['blur', 'change']
              }
            }
					};
			},
			onLoad() {
			},
			onShow() {
			},
			methods: {
        validate(fn) {
          this.$refs.uForm.validate().then((res) => {
            if (res) {
              fn(this.result)
            }
          })
        }
			}
	};
</script>

<style lang="scss" scoped>
</style>