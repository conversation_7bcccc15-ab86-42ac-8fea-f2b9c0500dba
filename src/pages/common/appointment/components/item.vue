<template>
  <uni-card>
    <template v-slot:title>
      <u-cell :title="`${itemData.objName}预约`" :border="false">
        <text slot="value">
          <u-tag
            :text="`${isCheckedMap[itemData.isChecked].name}`"
            :type="isCheckedMap[itemData.isChecked].type"
            plain
            plainFill
          />
        </text>
      </u-cell>
      <view class="mt4">
        <u-line></u-line>
      </view>
    </template>
    <view>
      <uni-row class="flex align-center">
        <u-avatar
          :src="itemData.visitorPhotoUrl"
          bg-color="#AA001E"
          shape="circle"
          class="little-margin"
          @click="openPicPreview(itemData.visitorPhotoUrl)"
        ></u-avatar>
        <view style="width: 100%" class="right-box">
          <uni-row class="flex">
            <span class="little-margin">{{ itemData.visitorName }}</span>
            <u--text :text="`— ${itemData.visitorPhone}`"></u--text>
          </uni-row>
          <uni-row class="flex">
            <span class="little-margin">来访时间：</span>
            <u--text :text="itemData.visitTime" block></u--text>
          </uni-row>
          <uni-row class="flex">
            <span class="little-margin">来访事由：</span>
            <u--text :text="itemData.visitReason" block></u--text>
          </uni-row>
        </view>
      </uni-row>
      <!-- <uni-row>
        <u-tag :text="`${itemData.objName}预约`" type="error" plain></u-tag>
      </uni-row> -->
    </view>
    <uni-row slot="actions">
      <uni-col :span="1"></uni-col>
      <uni-col :span="5" class="flex">
        <!-- <u--text
          v-if="itemData.isChecked === '0'"
          type="warning"
          text="未审核"
        ></u--text>
        <u--text
          v-else-if="itemData.isChecked === '1'"
          type="success"
          text="审核通过"
        ></u--text>
        <u--text
          v-else-if="itemData.isChecked === '-1'"
          type="error"
          text="审核拒绝"
        ></u--text> -->
        <!-- <u--text
          type="error"
          :text="`${itemData.objName}预约`"
        ></u--text> -->
        <!-- <u-tag :text="`${itemData.objName}预约`" type="error" plain></u-tag> -->
      </uni-col>
      <uni-col :span="10" class="flex"></uni-col>
      <uni-col :span="4">
        <!-- <u-button :disabled="itemData.isChecked==='1'||itemData.isChecked==='-1'" type="primary" shape="circle" size="mini" text="同意"></u-button> -->
      </uni-col>
      <uni-col :span="4" class="flex">
        <u--text
          text="详情"
          suffixIcon="arrow-right"
          @click="handleDetail()"
        ></u--text>
        <!-- <u-icon name="arrow-right" color="#2979ff" size="14">详情</u-icon> -->
        <!-- <u-button
          type="primary"
          size="mini"
          text="详情"
          @click="handleDetail()"
        ></u-button> -->
      </uni-col>
    </uni-row>
  </uni-card>
</template>

<script>
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: String,
      type: '',
    },
  },
  data() {
    return {
      isCheckedMap: {
        '-3': {
          name: '未到达',
          type: 'warning',
        },
        '-2': {
          name: '已取消',
          type: 'warning',
        },
        '-1': {
          name: '审核拒绝',
          type: 'error',
        },
        0: {
          name: '未审核',
          type: 'warning',
        },
        1: {
          name: '审核中',
          type: 'warning',
        },
        2: {
          name: '审核通过',
          type: 'success',
        },
      },
    };
  },
  onLoad() {},
  onShow() {},
  onUnload() {
    this.$closePreview();
  },
  methods: {
    /* 打开图片组件*/
    openPicPreview(url) {
      this.$openPreview(url).then();
    },
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
  },
};
</script>

<style lang="scss" scoped>
.right-box {
  padding-left: 10px;
}
.uni-col {
  padding: 10px 0;
}
.uni-card {
  margin: 5px 0 !important;
  padding: 10px 10px !important;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
</style>
