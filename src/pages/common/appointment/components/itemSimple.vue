<template>
  <uni-card
    :is-shadow="true"
    spacing="12px"
    margin="12px 12px 0"
    shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
    @click.native="handleDetail"
  >
    <view>
      <uni-row class="flex align-center">
        <view class="avatar-margin avatar-shadow">
          <u-avatar
            :src="itemData.visitorPhotoUrl"
            bg-color="#AA001E"
            shape="circle"
            size="126rpx"
            @click.native.stop="openPicPreview(itemData.visitorPhotoUrl)"
          ></u-avatar>
        </view>
        <view style="width: 100%" class="right-box">
          <uni-row
            class="flex"
            justify="space-between"
            customStyle="margin-bottom: 10px"
          >
            <u--text size="15" :text="itemData.visitorName"></u--text>
            <u-tag
              size="mini"
              :text="queryStatus(itemData.isChecked).text"
              :color="queryStatus(itemData.isChecked).color"
              :bgColor="queryStatus(itemData.isChecked).bgColor"
              :borderColor="queryStatus(itemData.isChecked).color"
            ></u-tag>
          </uni-row>
          <uni-row class="flex" customStyle="margin-bottom: 10px">
            <span class="little-margin">联系电话</span>
            <u--text
              color="#333333"
              size="12"
              :text="itemData.visitorPhone"
            ></u--text>
          </uni-row>
          <uni-row class="flex" customStyle="margin-bottom: 10px">
            <span class="little-margin">来访事由</span>
            <u--text
              color="#333333"
              size="12"
              :text="itemData.visitReason"
              block
            ></u--text>
          </uni-row>
          <uni-row class="flex">
            <span class="little-margin">来访时间</span>
            <u--text color="#333333" :text="itemData.visitTime" block></u--text>
          </uni-row>
        </view>
      </uni-row>
    </view>
  </uni-card>
</template>

<script>
import { getAppointStausMap } from '@/pages/common/list_config';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: String,
      type: '',
    },
  },
  data() {
    return {
      isCheckedMap: {
        '-3': {
          name: '未到达',
          type: 'warning',
        },
        '-2': {
          name: '已取消',
          type: 'warning',
        },
        '-1': {
          name: '审核拒绝',
          type: 'error',
        },
        0: {
          name: '未审核',
          type: 'warning',
        },
        1: {
          name: '审核中',
          type: 'warning',
        },
        2: {
          name: '审核通过',
          type: 'success',
        },
      },
    };
  },
  onLoad() {},
  onShow() {},
  onUnload() {
    this.$closePreview();
  },
  methods: {
    queryStatus(val) {
      return getAppointStausMap(val);
    },
    /* 打开图片组件*/
    openPicPreview(url) {
      this.$openPreview(url).then();
    },
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
  },
};
</script>

<style lang="scss" scoped>
.right-box {
  padding-left: 10px;
}
.uni-col {
  padding: 10px 0;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  font-size: 12px;
  color: #333333;
  margin-right: 45rpx;
}
.avatar-margin {
  margin-right: 41rpx;
}
</style>
