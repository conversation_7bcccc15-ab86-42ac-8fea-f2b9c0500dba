<template>
  <view class="visitStyle">
    <u-row customStyle="margin-bottom: 10px">
      <u-tag
        v-if="queryStatus(itemData.isChecked).text"
        size="mini"
        :text="queryStatus(itemData.isChecked).text"
        :color="queryStatus(itemData.isChecked).color"
        :bgColor="queryStatus(itemData.isChecked).bgColor"
        :borderColor="queryStatus(itemData.isChecked).color"
      ></u-tag>
      <u-text class="tip-sty" size="12" text="审核通过后，请在限定时间内使用通行二维码" />
    </u-row>
    <u-row customStyle="margin-bottom: 10px">
      <u-col span="4" class="time-box">
        <u-row customStyle="margin: 5px">
          <u-text size="12" align="center" :text="getDate(itemData.visitTime)" />
        </u-row>
        <u-row customStyle="margin: 5px">
          <u-text size="12" align="center" :text="getTime(itemData.visitTime)" />
        </u-row>
      </u-col>
      <u-col span="1">
        <u-text size="12" align="center" text="至" />
      </u-col>
      <u-col span="4" class="time-box">
        <u-row customStyle="margin: 5px">
          <u-text size="12" align="center" :text="getDate(itemData.leaveTime)" />
        </u-row>
        <u-row customStyle="margin: 5px">
          <u-text size="12" align="center" :text="getTime(itemData.leaveTime)" />
        </u-row>
      </u-col>
      <u-col span="3"></u-col>
    </u-row>
  </view>
</template>

<script>
import { getAppointStausMap} from '@/pages/common/list_config';
export default {
  name: 'AppointDetail',
  components: {
  },
  props: {
    itemData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
    };
  },
  onLoad() {},
  onShow() {},
  methods: {
    /* 打开图片组件*/
    queryStatus(val) {
      return getAppointStausMap(val) || {bgColor: '', text: ''};
    },
    getDate(val) {
      if (!val) {
        return '';
      }
      let list = val?.split(' ');
      let date = list[0] || '';
      return date;
    },
    getTime(val) {
      if (!val) {
        return '';
      }
      let list = val?.split(' ');
      let time = list[1] || '';
      return time;
    }
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
}
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
}
.center-text {
  justify-content: center !important;
}
.visitStyle {
  margin: 20px 12px;
}
.tip-sty {
  margin-left: 18px !important;
}
.time-box {
    background: #F7F6F6;
    border: 1px solid #F7F6F6;
    border-radius: 10px;
}
</style>
