<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="详情"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard middle-container">
      <view>
        <preview-info :itemData="info"></preview-info>
      </view>
      <uni-card
        spacing="12px"
        margin="12px 12px 0"
        shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
        :is-shadow="true"
      >
        <uni-section
          slot="title"
          titleFontSize="15"
          title="访客信息"
          type="line"
        />
        <AppointDetail :itemData="info"></AppointDetail>
      </uni-card>
      <!-- <uni-card v-if="info.followVisitorInfo && info.followVisitorInfo.length" :is-shadow="true">
        <u--text size="18" text="随访人信息" />
        <FollowsTable
          :titleList="titleList"
          :tableList="info.followVisitorInfo"
        />
      </uni-card> -->
      <view v-if="info.followVisitorInfo && info.followVisitorInfo.length">
        <uni-card
          spacing="12px"
          margin="12px 12px 0"
          shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
          v-for="(item, index) in info.followVisitorInfo"
          :key="index"
        >
          <uni-section
            slot="title"
            titleFontSize="15"
            :title="`访客信息${index + 1}`"
            type="line"
          />
          <view class="visitStyle">
            <ContentGroup :labelList="followLabelList" :itemInfo="item" />
            <view class="u_avatar avatar-shadow">
              <u-avatar
                :src="item.photoUrl"
                size="87rpx"
                @click="openPicPreview(item.photoUrl)"
              ></u-avatar>
            </view>
          </view>
        </uni-card>
      </view>
      <!-- <uni-card v-if="info.followVisitorInfo && info.followVisitorInfo.length" :is-shadow="true">
        <u--text size="18" text="随访人信息" />
        <FollowsTable
          :titleList="titleList"
          :tableList="info.followVisitorInfo"
        />
      </uni-card> -->
      <!-- <uni-row
        class="flex"
        v-if="info.isChecked === '0' && detailType !== 'recode'"
      >
        <u-button type="primary" text="去审核" @click="toExamine"></u-button>
      </uni-row> -->
    </view>
    <u-modal
      :show="modalShow"
      showCancelButton
      @cancel="modalCancel"
      @confirm="modalConfirm"
    >
      <ApprovalContent ref="approvalcontent"></ApprovalContent>
    </u-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import ContentGroup from '@/pages/components/contentGroup.vue';
import PreviewInfo from './components/previewInfo.vue';
import AppointDetail from './components/appointDetail.vue';
import ApprovalContent from './components/approvalContent.vue';
import FollowsTable from '@/pages/components/cellsTable.vue';
import { titleList, followLabelList } from './config.js';
import {
  getAppointmentRecord,
  getAppointmentAapprove,
  testExtendLeaveApplyPass,
} from '@/api/appointment/index.js';
export default {
  name: 'Detail',
  components: {
    AppointDetail,
    ApprovalContent,
    FollowsTable,
    PreviewInfo,
    ContentGroup,
  },
  props: {},
  data() {
    return {
      detailType: '',
      autoBack: true,
      leftText: '',
      recordId: '',
      info: {},
      modalShow: false,
      titleList,
      followLabelList,
    };
  },
  onLoad(option) {
    this.recordId = JSON.parse(
      decodeURIComponent(option.query) || ''
    )?.appointmentId;
    this.detailType = JSON.parse(
      decodeURIComponent(option.query) || ''
    )?.detailType;
    this.getRecord();
  },
  onShow() {},
  onUnload() {
    this.$closePreview();
  },
  methods: {
    /* 打开图片组件*/
    openPicPreview(url) {
      this.$openPreview(url).then();
    },
    cancel() {
      uni.navigateBack();
    },
    async toExamine() {
      try {
        //await getAppointmentAapprove(this.recordId);
        // await testExtendLeaveApplyPass('1898984990366203905');
        uni.navigateBack();
      } catch (error) {
        uni.navigateBack();
      }
      // this.modalShow = true;
    },
    modalCancel() {
      this.modalShow = false;
    },
    modalConfirm() {
      this.$refs.approvalcontent.validate(async (result) => {
        try {
          const res = await getAppointmentAapprove(this.recordId, result);
          this.modalShow = false;
          if (res.code === 200) {
            // this.$modal.msgSuccess('审核成功');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '审核成功',
                type: 'success',
              })
            );
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          } else {
            // this.$modal.msgError('审核失败');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '审核失败',
                type: 'error',
                position: 'top',
              })
            );
          }
        } catch (error) {}
      });
    },
    async getRecord() {
      try {
        const res = await getAppointmentRecord(this.recordId);
        this.info = { ...res.data };
      } catch (error) {}
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.middle-container {
  padding-top: 0;
  overflow: hidden;
  overflow-y: scroll;
}
.visitStyle {
  position: relative;
}
.u_avatar {
  position: absolute;
  right: 16rpx;
  top: 44rpx;
}
::v-deep .uni-section-header {
  padding: 12px 0 !important;
  font-weight: 600;
}
</style>
