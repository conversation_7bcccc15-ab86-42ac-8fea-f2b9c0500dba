<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="预约列表"
      :autoBack="autoBack"
      :leftText="leftText"
    >
    </top-navbar>
    <view class="bgCard middle-container">
      <view class="tabothers">
        <u-tabs 
          :list="topTabList"
          :current="currentIndex"
          lineWidth="17"
          :activeStyle="{
            color: '#AA011C ',
          }"
          :inactiveStyle="{
            color: '#999999',
          }"
          itemStyle="height: 44px;"
          @change="changeTab">
        </u-tabs>
        <u--image
          width="15px"
          height="15px"
          mode="aspectFit"
          class="filter-image"
          :src="globalConfig.iconImgPrefix + 'filterimage.png'"
          @click="filterHandle"
        ></u--image>
      </view>
      <card-list
        class="card-list"
        v-if="tableData.length"
        :cardList="tableData"
        @scrolltolower="scrolltolower"
      >
        <template #default="slotProps">
          <item-simple
            :itemData="slotProps.itemData"
            @handleDetail="cardItemClick(slotProps.itemData)"
          />
        </template>
      </card-list>
      <u-empty
        width="120"
        height="120"
        marginTop="20"
        v-else
        mode="list"
        icon="/static/images/iconImg/empty.png"
      />
    </view>
    <filter-popup ref="filterRef" @filterSearch="filterSearch" />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import publicList from '@/components/publicList/index.vue';
import CardList from '@/pages/components/cardList.vue';
import FilterPopup from './components/filterPopup.vue';
import Item from './components/item.vue';
import ItemSimple from './components/itemSimple.vue';
import { getToken } from '@/utils/auth';
import { children } from '../../config.js';
import {
  getAppointmentRecordList,
  getAppointmentObjectList,
} from '@/api/appointment/index.js';
export default {
  name: 'Name',
  components: {
    publicList,
    CardList,
    Item,
    FilterPopup,
    ItemSimple
  },
  data() {
    return {
      topTabList: [
        { name: '全部', keyName: '', badge: { value: 0 } },
        { name: '未审核', keyName: 'unaudited', badge: { value: 0 } },
        { name: '审核中', keyName: 'passed', badge: { value: 0 } },
        { name: '生效中', keyName: 'refused', badge: { value: 0 } },
        { name: '已失效', keyName: 'expired', badge: { value: 0 } },
      ],
      tableData: [],
      currentIndex: 0,
      globalConfig: getApp().globalData.config,
      leftText: '',
      autoBack: true,
      tabList: children,
      queryData: {
        visitorName: '',
        visitorPhone: '',
        intervieweeName: '',
        intervieweePhone: '',
        startTime: '',
        endTime: '',
        status: '',
        pageNum: 1,
        pageSize: 10,
      },
      labelColumns: {},
      totalPage: 0,
      routeOption: {},
      currTabNumber: 0,
      objList: [],
    };
  },

  // computed: {
  // 	taskStatus(status) {
  // 		return (status) => {
  // 			switch (status) {
  // 				case '1':
  // 					return {
  // 						type: 'primary',
  // 						text: '进行中'
  // 					};
  // 				case '2':
  // 					return {
  // 						type: 'success',
  // 						text: '已完成'
  // 					};
  // 				case '3':
  // 					return {
  // 						type: 'warning',
  // 						text: '已失效'
  // 					};
  // 			}
  // 		};
  // 	},

  // 	currentTabIndex() {
  // 		let index;
  // 		if (this.routeOption.category) {
  // 			index = this.tabList.findIndex((item) => item.value === this.routeOption.category);
  // 		}
  // 		return index ? index : 0;
  // 	}
  // },
  onLoad(option) {
    this.getAppointmentObjList();
    if (option?.status === '0') {
      this.currentIndex = 1;
      this.queryData.status = '0';
    }
    uni.$once('update', () => {
      this.$refs.filterRef.close();
    });
  },
  onShow() {
    this.queryData.pageNum = 1;
    this.tableData = [];
    this.getAppointmentList();
  },
  methods: {
    // 参考uview导航栏的高度，用来设定吸顶时与顶部的距离,h5不需要操作
    navbarHeight() {
      let systemInfo = uni.getSystemInfoSync();
      /* (750 / systemInfo.windowWidth) */
      /* 在uview navBar组件中有一个默认高度，当这个默认高度加上状态栏高度后就是吸顶的位置，由于这两者相加是px，所以最后还需要转为rpx */
      let topHeight = 0;
      // #ifdef APP-PLUS
      topHeight = 44 + systemInfo.statusBarHeight;
      // #endif
      // #ifdef MP
      let height = systemInfo.platform == 'ios' ? 44 : 48;
      topHeight = height + systemInfo.statusBarHeight;
      // #endif
      /* 最后一步将px转为rpx （2.0版本不需要换算了）*/
      return topHeight;
    },
    closePopup(e) {
      if (e) {
        // this.queryData.category = this.$route.query.category ? this.$route.query.category : this.tabList[0].value;
        this.searchHandle();
      }
    },
    filterHandle() {
      this.$refs.filterRef.open();
    },
    async getAppointmentList() {
      try {
        const res = await getAppointmentRecordList(this.queryData);
        let taskData = {
          list: [],
          total: 0,
        };
        taskData.list = res.rows;
        taskData.total = res.total;
        this.currTabNumber = res.total;
        this.totalPage = Math.ceil(taskData.total / this.queryData.pageSize);

        if (taskData.list.length) this.tableData.push(...taskData.list);
        // this.getDotNumber();
      } catch (err) {
        console.log(err);
      }
    },
    getDotNumber() {
      this.topTabList.forEach((e, index) => {
        if (this.currentIndex === index) {
          e.badge.value = this.currTabNumber;
        }
      });
    },
    searchHandle() {
      this.tableData = [];
      this.queryData.pageNum = 1;
      this.getAppointmentList();
    },
    filterSearch(info) {
      this.queryData = { ...this.queryData, ...info };
      this.searchHandle();
    },
    clearHandle() {
      this.queryData.taskTitle = '';
      this.searchHandle();
    },
    scrolltolower() {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.getAppointmentList();
    },
    cardItemClick(info) {
      let query = {
        appointmentId: info?.appointmentId,
      };

      uni.navigateTo({
        url: `/pages/common/appointment/detail?query=${encodeURIComponent(
          JSON.stringify(query)
        )}`,
      });
      return;
      let id;
      if (this.routeOption.path === 'todoList') {
        id = task.taskId;
      } else {
        id = task.procInsId;
      }
      if (
        task.category == 'other_order' ||
        task.category == 'device_poll' ||
        task.category == 'device_upkeep'
      ) {
        let query = {
          token: getToken(),
          procInsId: task.procInsId,
          deployId: task.deployId,
          taskId: task.taskId,
          category: task.category,
          path: this.routeOption.path,
        };
        if (this.routeOption.path == 'todoList') {
          query.taskName = task.taskName;
          query.startUser = task.startUserName;
          query.procDefId = task.procDefId;
        }
        if (
          task.category == 'device_poll' ||
          task.category == 'device_upkeep'
        ) {
          if (this.routeOption.path == 'todoList') {
            query.taskDefKey = task.taskDefKey;
          }
          uni.navigateTo({
            url: `/pages/common/checkRecord/detail?query=${encodeURIComponent(
              JSON.stringify(query)
            )}`,
          });
        } else {
          uni.navigateTo({
            url: `/pages/common/myTaskList/detail?query=${encodeURIComponent(
              JSON.stringify(query)
            )}`,
          });
        }
      } else {
        let params = {
          procInsId: task.procInsId,
          executionId: task.executionId,
          deployId: task.deployId,
          taskId: id,
          taskName: task.taskName,
          startUser: task.startUserName,
          category: task.category,
          procDefId: task.procDefId,
          taskDefKey: task.taskDefKey,
          routeOption: this.routeOption,
        };

        uni.navigateTo({
          url: `/pages/common/myTaskList/detailPopup/index?query=${encodeURIComponent(
            JSON.stringify(params)
          )}`,
        });
      }
    },
    changeTab(e) {
      this.currentIndex = e.index;
      if (e.keyName === 'unaudited') {
        this.queryData.status = '0';
      } else if (e.keyName === 'passed') {
        this.queryData.status = '1';
      } else if (e.keyName === 'refused') {
        this.queryData.status = '2';
      } else if (e.keyName === 'expired') {
        this.queryData.status = '3';
      } else {
        this.queryData.status = '';
      }
      this.queryData.pageNum = 1;
      this.tableData = [];
      this.getAppointmentList();
    },
    async getAppointmentObjList() {
      try {
        const res = await getAppointmentObjectList();
        this.objList = res.data;
      } catch (error) {}
    },
    getTypeName(id) {
      let objName;
      this.objList.forEach((item) => {
        if (id === item.objId) {
          objName = item.objName;
        }
      });

      return objName || '';
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.tabothers {
  display: flex;
  align-items: center;
  background: '#ffff' !important;
  padding: 0 12px;
}
.filter-image {
    flex: 1;
    display: flex;
    align-items: flex-end;
  }
.middle-container {
  padding-top: 0;
}
::v-deep .u-cell__body {
  padding: unset;
}

::v-deep .u-tag-wrapper {
  flex-direction: unset;
}
.todoListPage .bgCard .u-list {
  height: calc(100vh - 90px) !important;
  ::v-deep .u-list-item:last-child {
    margin-bottom: 20px;
  }
}
</style>
