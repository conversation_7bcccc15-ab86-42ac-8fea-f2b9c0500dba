<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="邀约"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card>
        <u--form
          labelPosition="left"
          labelWidth="85"
          :model="userInfo"
          :rules="rules"
          ref="uForm"
        >
          <u-form-item
            label="预约人类型"
            prop="objId"
            required
            borderBottom
            @click="(showObjType = true), hideKeyboard()"
          >
            <u--input
              v-model="objId"
              placeholder="请选择预约人类型"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
            <!-- <uni-data-select
                v-model="userInfo.objId"
                :localdata="typeRange"
                placeholder="请选择预约人类型"
                @change="typeChange"
                border="none"
              ></uni-data-select> -->
          </u-form-item>
          <u-form-item
            label="预约人姓名"
            prop="visitorName"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写预约人姓名"
              maxlength="20"
              v-model="userInfo.visitorName"
              border="none"
              :showWordLimit="true"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="预约人手机"
            prop="visitorPhone"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写预约人手机"
              maxlength="11"
              v-model="userInfo.visitorPhone"
              :showWordLimit="true"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="被访人姓名"
            prop="intervieweeName"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写被访人姓名"
              maxlength="20"
              v-model="userInfo.intervieweeName"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="被访人手机"
            prop="intervieweePhone"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写被访人手机"
              maxlength="11"
              v-model="userInfo.intervieweePhone"
              border="none"
            ></u--input>
          </u-form-item>
          <!-- <u-form-item
                label="来访时间"
                prop="visitTime"
                required
            >
              <u--input
                  v-model="userInfo.visitTime"
                  border="none"
              ></u--input>
            </u-form-item>
            <u-form-item
                label="离厂时间"
                prop="leaveTime"
                required
            >
              <u--input
                  v-model="userInfo.leaveTime"
                  border="none"
              ></u--input>
            </u-form-item> -->
        </u--form>
        <uni-row slot="actions" class="flex">
          <uni-col :span="10">
            <u-button
              type="info"
              text="取消"
              @click="cancelInvitation"
            ></u-button>
          </uni-col>
          <uni-col :span="4"> </uni-col>
          <uni-col :span="10">
            <u-button
              type="primary"
              text="发起邀约"
              @click="sendInvitation"
            ></u-button>
          </uni-col>
        </uni-row>
      </uni-card>
    </view>
    <u-action-sheet
      :actions="typeRange"
      :show="showObjType"
      cancelText="取消"
      @select="typeChange"
      @close="showObjType = false"
    ></u-action-sheet>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import {
  getAppointmentObjectList,
  getApplyInvitation,
} from '@/api/appointment/index.js';
export default {
  name: 'Invitation',
  props: {},
  data() {
    return {
      autoBack: true,
      leftText: '',
      userInfo: {
        visitorName: '',
        visitorPhone: '',
        intervieweeName: '',
        intervieweePhone: '',
        objId: '',
        // visitTime: '',
        // leaveTime: ''
      },
      rules: {
        visitorName: {
          type: 'string',
          required: true,
          max: 50,
          message: '请填写预约人姓名',
          trigger: ['blur', 'change'],
        },
        visitorPhone: [
          {
            type: 'number',
            max: 11,
            required: true,
            message: '请填写正确的手机号',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],
        intervieweeName: {
          type: 'string',
          required: true,
          max: 50,
          message: '请填写被访人姓名',
          trigger: ['blur', 'change'],
        },
        intervieweePhone: [
          {
            type: 'number',
            max: 11,
            required: true,
            message: '请填写正确的手机号',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],
        visitTime: {
          type: 'string',
          required: true,
          message: '请选择来访时间',
          trigger: ['blur', 'change'],
        },
        leaveTime: {
          type: 'string',
          required: true,
          message: '请选择离厂时间',
          trigger: ['blur', 'change'],
        },
        objId: {
          type: 'string',
          required: true,
          message: '请选择访客类型',
          trigger: ['blur', 'change'],
        },
      },
      typeRange: [],
      objId: '',
      showObjType: false,
    };
  },
  onLoad() {
    this.getTypeList();
  },
  onShow() {},
  methods: {
    cancelInvitation() {
      uni.navigateBack();
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    typeChange(obj) {
      this.objId = obj.name;
      this.userInfo.objId = obj.value;
      this.showObjType = false;
    },
    async getTypeList() {
      try {
        const res = await getAppointmentObjectList();
        let newRes = res?.data;
        this.typeRange = newRes.map((item) => ({
          name: item.objName,
          value: item.objId,
        }));
      } catch (error) {}
    },
    sendInvitation() {
      this.$refs.uForm.validate().then(async (res) => {
        if (res) {
          const rep = await getApplyInvitation(this.userInfo);
          if (rep.code === 200) {
            // this.$modal.msgSuccess('邀约成功');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '邀约成功',
                type: 'success',
              })
            );
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          } else {
            // this.$modal.msgError('邀约失败');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '邀约失败',
                position: 'top',
                type: 'error',
              })
            );
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
}
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
  overflow: scroll;
  height: 80%;
  ::v-deep .uni-card__content {
    height: 90%;
  }
}
</style>
