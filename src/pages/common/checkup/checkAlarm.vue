<template>
  <uni-card>
    <u--text size="18" text="告警信息" />
    <u-gap height="12"></u-gap>
    <uni-list class="curStyle">
      <uni-list-item
        v-for="(item, index) in alarmList"
        :key="index"
        :note="`${item.alarmContent}`"
      >
        <template v-slot:footer>
          <u-tag
            slot="value"
            :text="queryStatus(item.disposeStatus).text"
            size="mini"
            :color="queryStatus(item.disposeStatus).color"
            :bgColor="queryStatus(item.disposeStatus).bgColor"
            :borderColor="queryStatus(item.disposeStatus).color"
          ></u-tag>
        </template>
      </uni-list-item>
    </uni-list>
  </uni-card>
</template>
<script>
import { getStatus } from './config';
export default {
  data() {
    return {
      alarmList: [],
    };
  },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    tableData(val) {
      this.alarmList = val || [];
    },
  },
  methods: {
    queryStatus(val) {
      return getStatus(val);
    },
  },
};
</script>
<style>
.curStyle {
  max-height: 500rpx;
  overflow-y: auto;
}
</style>
