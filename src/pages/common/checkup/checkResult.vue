<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="topTitle"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card class="flex justify-center align-center">
        <ResultTip :status="status" :text="tipText" />
        <view class="mt16" v-show="showIngore">
          <u-button type="primary" text="忽略" @click="jump" />
        </view>
      </uni-card>
    </view>
  </view>
</template>

<script>
import ResultTip from '@/pages/components/resultTip.vue';
export default {
  name: 'CheckResult',
  components: {
    ResultTip,
  },
  props: {},
  data() {
    return {
      autoBack: true,
      leftText: '',
      topTitle: '提示',
      status: '',
      tipText: '',
      showIngore: false,
      jumpurl: '',
      passId: '',
      passCode: '',
    };
  },
  onLoad(option) {
    if (option.query) {
      let query = JSON.parse(decodeURIComponent(option.query));
      console.log(query);
      this.status = query.status;
      this.tipText = query.msg;
      this.showIngore = query.errorsIgnored;
      this.jumpurl = query.url;
      this.passId = query.passId;
      this.passCode = query.passCode;
    }
  },
  onShow() {},
  methods: {
    jump() {
      const params = {
        passId: this.passId,
        passCode: this.passCode,
      };

      const url = `/pages/common/checkup/index?info=${encodeURIComponent(
        JSON.stringify(params)
      )}`;

      uni.redirectTo({
        url: url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
  height: 50%;
}
// .middle-container {
//     padding-top: 0;
// }
</style>
