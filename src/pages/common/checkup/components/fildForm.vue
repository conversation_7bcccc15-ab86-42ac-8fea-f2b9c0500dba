<template>
  <view>
    <u--form :model="uFormModel" labelWidth="auto" :labelStyle="labelStyle">
      <u-form-item
        v-for="item in formItems"
        :key="item.fieldId"
        :label="item.fieldName"
        :prop="item.fieldCode"
      >
        <u--text
          v-if="item.fieldType != 'upload'"
          :text="reSetValue(item.fieldValue)"
        />
        <u-album
          v-else
          :urls="reSetUpValue(item.fieldValue)"
          singleSize="70"
        ></u-album>
      </u-form-item>
    </u--form>
  </view>
</template>

<script>
export default {
  name: 'FildForm',
  components: {},
  props: {
    formItems: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      uFormModel: {},
      uFormRules: {},
      labelStyle: {
        color: '#909399',
      },
    };
  },
  onLoad() {},
  methods: {
    reSetValue(val) {
      return this.isJSONString(val) ? JSON.parse(val).join(',') : val;
    },
    reSetUpValue(val) {
      return JSON.parse(val);
    },
    isJSONString(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
