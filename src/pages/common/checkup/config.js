export const vistor<PERSON>abelList = [
  {
    label: '访客姓名',
    value: 'visitorName',
  },
  {
    label: '访客手机',
    value: 'visitorPhone',
  },
  {
    label: '访客公司',
    value: 'visitorCompany',
  },
  {
    label: '车牌号',
    value: 'carNo',
  },
  {
    label: '随访人数',
    value: 'followNumber',
  },
  {
    label: '来访事由',
    value: 'visitReason',
  },
  {
    label: '预约对象',
    value: 'objectName',
  },
  {
    label: '通行码',
    value: 'passCode',
  },

  {
    label: '来访时间',
    value: 'visitTime',
  },
  {
    label: '离场时间',
    value: 'leaveTime',
  },
  {
    label: '备注',
    value: 'remark',
  },
];
export const intervieweeLabelList = [
  {
    label: '被访问人姓名',
    value: 'intervieweeName',
  },

  {
    label: '被访问人手机',
    value: 'intervieweePhone',
  },
  {
    label: '被访问人部门',
    value: 'intervieweeDept',
  },
];
export const deviceList = [
  {
    label: '设备名称',
    value: 'deviceName',
  },
  {
    label: '设备编码',
    value: 'deviceCode',
  },
];

const stausMap = {
  0: {
    text: '未处理',
    status: '0',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
  1: {
    text: '已处理',
    status: '1',
    color: '#fff',
    bgColor: '#aa001e',
  },
  2: {
    text: '处理中',
    status: '2',
    color: '#fff',

    bgColor: '#ffcd5d',
  },
};

export const stepList = {
  0: [
    {
      text: '预约信息',
      value: 0,
    },
    {
      text: '入园核验',
      value: 1,
    },
    // {
    //   text: '绑定设备',
    //   value: 2,
    // },
    {
      text: '确认入园',
      value: 3,
    },
  ],
  1: [
    {
      text: '预约信息',
      value: 0,
    },
    {
      text: '出园核验',
      value: 1,
    },

    {
      text: '确认出园',
      value: 2,
    },
  ],
};
export const getStepsList = (type, needCheck) => {
  const data = JSON.parse(JSON.stringify(stepList[type]));

  if (!needCheck) {
    data.splice(1, 1);
  }
  return data;
};
export const getStatus = (key) => {
  return stausMap[key];
};
