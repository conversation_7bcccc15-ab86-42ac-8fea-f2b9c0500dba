<template>
  <view class="checkPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="topTitle"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="page_body middle-container">
      <view class="mt16">
        <u-steps
          :current="currentIndex"
          activeIcon="checkmark"
          inactiveIcon="arrow-right"
        >
          <u-steps-item
            :title="item.text"
            v-for="item in showSteps"
            :key="item.value"
          ></u-steps-item>
        </u-steps>
      </view>
      <ShowVistor
        :itemInfo="appointmentInfo"
        v-show="isShowVistor"
      ></ShowVistor>

      <ShowCheckForm
        :checkFields="checkFields"
        :passCheckHistory="passCheckHistory"
        ref="checkForm"
        v-show="isChecked"
        :isShowHistroy="currentIndex != showSteps.length - 1"
      ></ShowCheckForm>
      <!--  v-show="isShowDevice" 暂时隐藏通行证绑定 -->
      <uni-card v-show="false">
        <view class="flex" v-show="currentIndex != showSteps.length - 1">
          <u--text size="18" text="绑定通行证" />
          <u-icon
            name="scan"
            size="48"
            color="#AA001E"
            @click="scanCode"
          ></u-icon>
        </view>
        <view
          v-show="
            Object.keys(deviceInfo).length &&
            currentIndex == showSteps.length - 1
          "
        >
          <u--text size="18" text="设备信息" />
          <u-gap height="12"></u-gap>
        </view>
        <ContentGroup
          v-show="Object.keys(deviceInfo).length"
          :labelList="deviceList"
          :itemInfo="deviceInfo"
        ></ContentGroup>
        <u-gap height="12" v-show="Object.keys(deviceInfo).length"></u-gap>
        <view class="flex" style="justify-content: flex-end">
          <u-tag
            color="#fff"
            bgColor="#aa001e"
            borderColor="#fff"
            text="取消绑定"
            @click="cancelAbout"
            v-show="
              Object.keys(deviceInfo).length &&
              currentIndex == showSteps.length - 2
            "
          ></u-tag>
        </view>
      </uni-card>

      <CheckAlarm
        v-show="alarmList.length && currentIndex == showSteps.length - 1"
        :tableData="alarmList"
      ></CheckAlarm>
    </view>

    <uni-row class="flex space_b setOper">
      <uni-col :span="6">
        <u-button
          :type="currentIndex == 0 ? 'info' : 'primary'"
          :text="currentIndex == 0 ? '取消' : '上一步'"
          @click="cancelNext"
        ></u-button>
      </uni-col>
      <uni-col :span="3"></uni-col>
      <uni-col :span="6">
        <u-button
          type="error"
          text="核验不通过"
          @click="cancelCheckIn"
          v-show="checkFields.length && currentIndex == 1"
        ></u-button>
      </uni-col>
      <uni-col :span="3"></uni-col>
      <uni-col :span="6">
        <u-button
          type="primary"
          :text="submitText"
          @click="toNext"
          v-show="commonStatus"
        ></u-button>
      </uni-col>
    </uni-row>
    <qr-code
      v-show="isScaning"
      ref="qrScan"
      @upScaning="upScaning"
      @stopScan="stopScan"
    ></qr-code>
    <u-loading-page :loading="loading" loadingText="请稍后" />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import formFieldLoader from '@/utils/formFieldLoader';
import ShowVistor from './showVistor.vue';
import ShowCheckForm from './showCheckForm.vue';
import ContentGroup from '@/pages/components/contentGroup.vue';
import CheckAlarm from './checkAlarm.vue';
import { getStepsList, deviceList } from './config.js';
import { getPassCheckInfo, commitCheckInfo } from '@/api/checkup/index.js';
import QrCode from '@/components/qr/code';
export default {
  name: 'CheckUp',
  components: {
    ShowVistor,
    ShowCheckForm,
    CheckAlarm,
    ContentGroup,
    QrCode,
  },
  props: {},
  data() {
    return {
      loading: false,
      autoBack: true,
      leftText: '',
      currentIndex: 0,
      topTitle: '核验',
      deviceList,
      appointmentInfo: {},
      checkType: 0,
      checkFields: [],
      uFormModel: {},
      uFormRules: {},
      showVistor: true,
      commonStatus: true,
      deviceInfo: {},
      showSteps: [],
      alarmList: [],
      passCheckHistory: [],
      appointResult: {},
      isScaning: false,

      checkInfoBoolean: false,
    };
  },

  computed: {
    isShowVistor() {
      return (
        this.currentIndex == 0 || this.currentIndex == this.showSteps.length - 1
      );
    },
    isChecked() {
      return (
        (this.checkFields &&
          this.checkFields.length &&
          this.currentIndex == 1) ||
        (this.checkInfoBoolean &&
          this.currentIndex == this.showSteps.length - 1)
      );
    },
    isShowDevice() {
      const len = this.showSteps.length;
      return (
        this.checkType == 0 &&
        ((len - 1 == this.currentIndex && !!this.appointResult.deviceId) ||
          len - 2 == this.currentIndex)
      );
    },
    submitText() {
      return this.currentIndex == this.showSteps.length - 1
        ? this.checkType == 0
          ? '确认入园'
          : '确认出园'
        : '下一步';
    },
  },
  onLoad(option) {
    if (!option.info) {
      uni.redirectTo({
        url: `/pages/gate/index`,
      });
      return;
    }
    this.appointmentInfo = JSON.parse(decodeURIComponent(option.info));
    this.appointResult.passId = this.appointmentInfo.passId;
    this.getCheckInfo();
  },
  onShow() {},
  methods: {
    fieldComponent(type) {
      // 根据传入的表单类型动态加载组件
      return formFieldLoader.getFieldComponent(type);
    },
    cancelNext() {
      // 根据步骤和出入园状态判定
      //currentIndex, this.checkType;
      if (this.currentIndex == 0) {
        uni.navigateBack(); //取消返回来时页
      }

      this.currentIndex--;
    },
    toNext() {
      // 根据步骤和出入园状态,以及是否有核验项来判定
      if (this.currentIndex == this.showSteps.length - 1) {
        this.appointResult.checkResult = 0;
        this.toCommit(); // 入园或者出园操作
      }

      //出入园校验

      if (this.isChecked) {
        this.$refs.checkForm.validateForm((val) => {
          this.appointResult.checkInfos = val.reduce((pre, next) => {
            pre.push({
              fieldCode: next.fieldCode,
              fieldValue: next.fieldValue,
            });
            return pre;
          }, []);

          this.checkInfoBoolean = val.some((item) => item.fieldValue);

          this.currentIndex++;
        });
        return;
      }

      this.currentIndex++;
    },

    async cancelCheckIn() {
      this.$refs.checkForm.validateForm((val) => {
        this.appointResult.checkResult = 1;
        this.appointResult.checkInfos = val.reduce((pre, next) => {
          pre.push({
            fieldCode: next.fieldCode,
            fieldValue: next.fieldValue,
          });
          return pre;
        }, []);
        this.checkFail();
      });
    },
    async checkFail() {
      this.loading = true;
      try {
        const res = await commitCheckInfo(this.appointResult);
        this.loading = false;
        uni.navigateBack();
      } catch (error) {
        this.loading = false;
      }
    },
    scanCode() {
      this.isScaning = true;
      this.$refs.qrScan.scanBefore();
    },
    stopScan() {
      this.isScaning = false;
    },
    /*扫码结果*/
    upScaning(val) {
      this.isScaning = false;
      if (val.status) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: val.msg,
            type: 'error',
          })
        );
      } else {
        this.appointResult.deviceId = val.deviceId;
        this.deviceInfo = val;
      }
    },
    /*取消设备绑定*/
    cancelAbout() {
      this.$modal.confirm('确定取消已绑定设备吗？').then(() => {
        this.appointResult.deviceId = '';
        this.deviceInfo = {};
      });
    },
    async toCommit() {
      this.loading = true;
      try {
        const res = await commitCheckInfo(this.appointResult);
        this.loading = false;
        let status;
        if (res.code === 200) {
          this.$refs.uToast.show(
            this.$setToastMsg({
              message: this.checkType == 0 ? '入园审核成功' : '出园审核成功',
              type: 'success',
            })
          );
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/gate/index`,
            });
          }, 1000);
        } else {
          status = 'error';
          let query = {
            status,
            msg: res.msg,
          };
          uni.redirectTo({
            url: `/pages/common/checkup/checkResult?query=${encodeURIComponent(
              JSON.stringify(query)
            )}`,
          });
        }
      } catch (error) {
        this.loading = false;
        let query = {
          status: 'error',
          msg: error.msg || '',
        };
        uni.redirectTo({
          url: `/pages/common/checkup/checkResult?query=${encodeURIComponent(
            JSON.stringify(query)
          )}`,
        });
      }
    },
    async getCheckInfo() {
      try {
        this.loading = true;
        let params = {
          passId: this.appointmentInfo.passId,
          passCode: this.appointmentInfo.passCode,
        };

        const res = await getPassCheckInfo(params);
        this.loading = false;
        if (res.data) {
          const data = res.data;
          this.appointmentInfo = data;

          this.checkFields = data.checkFields || [];

          this.checkType = data.arriveStatus == 1 ? 1 : 0;
          this.showSteps = getStepsList(
            this.checkType,
            !!this.checkFields.length
          );
          this.passCheckHistory = data.passCheckHistory || [];
          this.alarmList =
            data.alarmList ||
            [
              // { disposeStatus: 0, alarmContent: '违规停车' },
              // { disposeStatus: 1, alarmContent: '违规车' },
            ];
        }
      } catch (error) {
        this.loading = false;
        if (error == 500) {
          let query = {
            status: 'error',
            msg: res.msg,
          };
          uni.redirectTo({
            url: `/pages/common/checkup/checkResult?query=${encodeURIComponent(
              JSON.stringify(query)
            )}`,
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
  overflow: hidden;
  overflow-y: scroll;
}

.setOper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16rpx;
}
.page_body {
  height: calc(100vh - 86rpx);
  padding-bottom: 80rpx;
}
.checkPage {
  height: 100vh;
  background-color: #f7f7f7;
}
</style>
