<template>
  <uni-card>
    <view class="flex spacr_b">
      <u--text size="24" text="核验内容" />
      <u-tag
        color="#fff"
        bgColor="#aa001e"
        borderColor="#fff"
        text="历史核验"
        size="large"
        @click="openPop"
        v-show="showHistroy"
      ></u-tag>
    </view>
    <u-gap height="12"></u-gap>
    <DynamicForm
      ref="dynamicForm"
      :formItems="fields"
      v-on="$listeners"
      v-show="showHistroy"
    ></DynamicForm>
    <FildForm :formItems="filedResult" v-show="!showHistroy"></FildForm>
    <u-popup :show="showPop" @close="showPop = false" :round="10" mode="bottom">
      <view class="recodeStyle">
        <uni-card v-for="item in historyCheckRecode" :key="item.infoId">
          <view class="flex">
            <span style="color: #303133; font-size: 20px" class="mr8"
              >{{ item.checkType == 0 ? '入' : '出' }}园核验</span
            >
            <u--text :text="item.checkTime" size="16" color="#999" />
          </view>
          <u-gap height="6"></u-gap>
          <u-line></u-line>
          <u-gap height="8"></u-gap>
          <FildForm
            :formItems="item.checkInfoList.filter((val) => !!val.fieldValue)"
          ></FildForm>
          <view
            class="flex align_center footorStyle"
            style="justify-content: flex-end"
          >
            <span>审核人:</span>
            <span>{{ item.checkUserName }}</span>
          </view>
        </uni-card>
      </view>
    </u-popup>
  </uni-card>
</template>
<script>
import DynamicForm from '@/pages/components/dynamicForm.vue';
import FildForm from './components/fildForm.vue';
export default {
  components: {
    DynamicForm,
    FildForm,
  },
  data() {
    return {
      fields: [],
      showPop: false,
      historyCheckRecode: [],
      filedResult: [],
      showHistroy: true,
    };
  },
  props: {
    checkFields: {
      type: Array,
      default: () => [],
    },
    passCheckHistory: {
      type: Array,
      default: () => [],
    },
    isShowHistroy: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    checkFields(val) {
      this.fields = val;
      if (val && val.length) {
        this.$nextTick(() => {
          this.$refs.dynamicForm.getFormInfo();
          this.$refs.dynamicForm.getFormRules();
        });
      }
    },
    passCheckHistory(val) {
      this.historyCheckRecode = val;
    },
    isShowHistroy(val) {
      this.showHistroy = val;
    },
  },
  methods: {
    openPop() {
      if (!this.historyCheckRecode.length) {
        this.$modal.msg('暂无历史核验记录');
        return;
      }
      this.showPop = true;
    },
    validateForm(fn) {
      this.$refs.dynamicForm.validateForm((val) => {
        this.filedResult = val.filter((item) => !!item.fieldValue);

        fn(val);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.recodeStyle {
  max-height: 60vh;
  overflow-y: auto;
  padding: 24rpx;

  .footorStyle {
    font-size: 18px;
  }
}
.uni-card {
  overflow: visible;
}

/* 增大表单相关文字大小 */
::v-deep .u-form-item__body__left__content__label {
  font-size: 18px !important;
}

::v-deep .u-input__content__field-wrapper__field {
  font-size: 18px !important;
}

::v-deep .u-radio__text {
  font-size: 18px !important;
}

::v-deep .u-checkbox__text {
  font-size: 18px !important;
}

::v-deep .u-button__text {
  font-size: 18px !important;
}
</style>
