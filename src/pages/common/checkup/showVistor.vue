<template>
  <view>
    <uni-card>
      <view class="visitStyle">
        <u--text size="24" text="访客信息" />
        <u-gap height="12"></u-gap>
        <!-- <uni-section title="访客信息"></uni-section> -->
        <ContentGroup
          :labelList="vistorLabelList"
          :itemInfo="appointmentInfo"
        />
        <u-avatar
          :src="appointmentInfo.visitorPhoto"
          size="60"
          class="u_avatar"
          @click="openPicPreview"
        ></u-avatar>
      </view>
    </uni-card>
    <uni-card>
      <u--text size="24" text="被访问人信息" />
      <u-gap height="12"></u-gap>
      <ContentGroup
        :labelList="intervieweeLabelList"
        :itemInfo="appointmentInfo"
      />
    </uni-card>
  </view>
</template>
<script>
import ContentGroup from '@/pages/components/contentGroup.vue';
import { vistor<PERSON>abel<PERSON>ist, interviewee<PERSON>abe<PERSON><PERSON>ist } from './config.js';
export default {
  components: {
    ContentGroup,
  },
  data() {
    return {
      vistor<PERSON><PERSON><PERSON><PERSON>ist,
      interviewee<PERSON><PERSON>l<PERSON>ist,
      appointmentInfo: {},
    };
  },
  props: {
    itemInfo: {
      type: [Object, String],
      default: () => {},
    },
  },
  watch: {
    itemInfo(val) {
      this.appointmentInfo = val;
    },
  },
  methods: {
    /* 打开图片组件*/
    openPicPreview() {
      this.$openPreview(this.appointmentInfo.visitorPhoto);
    },
  },
};
</script>
<style lang="scss">
.visitStyle {
  position: relative;
}
.u_avatar {
  position: absolute;
  right: 16rpx;
  top: 88rpx;
}
</style>
