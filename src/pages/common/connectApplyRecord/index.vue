<template>
    <view class="todoListPage topBgBox">
      <top-navbar
        bgColor="#AA001E"
        :title="navBarTitle"
        :autoBack="autoBack"
        leftText=""
      >
      </top-navbar>
      <view class="bgCard middle-container">
        <view class="tabothers">
          <u-tabs
            :list="topTabList"
            :current="currentIndex"
            lineWidth="17"
            :activeStyle="{
              color: '#AA011C ',
            }"
            :inactiveStyle="{
              color: '#999999',
            }"
            itemStyle="height: 44px;"
            @change="changeTab"
          >
          </u-tabs>
        </view>
        <card-list
          class="card-list"
          v-if="tableData.length"
          :cardList="tableData"
          @scrolltolower="scrolltolower"
        >
          <template #default="slotProps">
            <item
              :itemData="slotProps.itemData"
              @handleDetail="cardItemClick(slotProps.itemData)"
            />
          </template>
        </card-list>
        <u-empty
          width="120"
          height="120"
          marginTop="20"
          v-else
          mode="list"
          icon="/static/images/iconImg/empty.png"
        />
      </view>
  
      <u-toast ref="uToast"></u-toast>
    </view>
  </template>
  
  <script>
  import CardList from '@/pages/components/cardList.vue';
  import Item from './components/item.vue';
  import { tansParams } from '@/utils/permission';
  import { applyList } from '@/api/connectApplyRecord';
  export default {
    name: 'Name',
    components: {
      CardList,
      Item,
    },
    data() {
      return {
        topTabList: [
          { name: '全部', keyName: '' },
          { name: '未审核', keyName: '0' },
          { name: '审核中', keyName: '1' },
          { name: '审批通过', keyName: '2' },
          { name: '审批拒绝', keyName: '-1' },
        ],
        tableData: [],
        currentIndex: 0,
        globalConfig: getApp().globalData.config,
        autoBack: true,
        queryData: {
          status: '',
          pageNum: 1,
          pageSize: 10,
        },
        navBarTitle: '',
        totalPage: 0,
      };
    },
  
    onLoad(option) {
      this.navBarTitle = option.title;
    },
    onShow() {
      this.searchHandle();
    },
    methods: {
      async getApplyList() {
        try {
          const res = await applyList(this.queryData);
          let taskData = {
            list: [],
            total: 0,
          };
          taskData.list = res.rows;
          taskData.total = res.total;
  
          this.totalPage = Math.ceil(taskData.total / this.queryData.pageSize);
  
          if (taskData.list.length) this.tableData.push(...taskData.list);
        } catch (err) {
          console.log(err);
        }
      },
  
      searchHandle() {
        this.tableData = [];
        this.queryData.pageNum = 1;
        this.getApplyList();
      },
  
      scrolltolower() {
        this.queryData.pageNum++;
        if (this.queryData.pageNum > this.totalPage) {
          this.$refs.uToast.show(
            this.$setToastMsg({
              message: '已加载全部',
              type: 'success',
            })
          );
          return;
        }
        this.getApplyList();
      },
      cardItemClick(info) {
        let query = {
          applyId: info?.applyId,
          title: '详情',
          info: info
        };
        uni.navigateTo({
          url: '/pages/common/useCar/useBusCar?applyId=' + query.applyId + '&title=' + query.title + '&info=' + encodeURIComponent(JSON.stringify(query.info))
        });
        return;
      },
      changeTab(e) {
        this.queryData.status = e.keyName;
        this.currentIndex = e.index;
        this.searchHandle();
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  uni-page-body {
    background-image: url('@/static/images/bg.png');
    background-repeat: repeat;
    background-size: contain;
  }
  .tabothers {
    display: flex;
    align-items: center;
    background: '#ffff' !important;
    padding: 0 12px;
  }
  .filter-image {
    flex: 1;
    display: flex;
    align-items: flex-end;
  }
  .middle-container {
    padding-top: 0;
  }
  ::v-deep .u-cell__body {
    padding: unset;
  }
  
  ::v-deep .u-tag-wrapper {
    flex-direction: unset;
  }
  .todoListPage .bgCard .u-list {
    height: calc(100vh - 90px) !important;
    ::v-deep .u-list-item:last-child {
      margin-bottom: 20px;
    }
  }
  </style>
  