<template>
  <view>
    <u-modal
      :show="show"
      mode="center"
      @close="close"
      round="8"
      showCancelButton
      @confirm="confirm"
      @cancel="close"
      confirmColor="#aa001e"
    >
      <view class="popup_content">
        <uni-section :title="cancelTitle" type="line">
          <u--textarea
            :placeholder="inputPlaceholder"
            border="surround"
            v-model="cancelReason"
            maxlength="100"
            count
          />
        </uni-section>

        <!-- <view class="footer_button">
          <u-button
            style="width: 180rpx"
            plain
            hairline
            type="primary"
            text="确定"
            @click="confirm"
          ></u-button>
        </view> -->
      </view>
    </u-modal>
  </view>
</template>

<script>
export default {
  name: 'TaskCancelPopup',
  props: {
    taskId: {
      type: String,
    },
    pathType: {
      type: String,
    },
    pathEnum: {
      type: Object,
    },
    cancelTask: {
      type: Function,
      required: true,
    },
    cancelTitle: {
      type: String,
      default: '取消原因',
    },
    inputPlaceholder: {
      type: String,
      default: '请输入取消原因',
    },
    confirmMessage: {
      // 新增 confirmMessage prop
      type: String,
      default: '请填写取消原因', // 设置默认值
    },
  },
  data() {
    return {
      show: false,
      cancelReason: '',
      loading: false,
    };
  },
  methods: {
    close() {
      this.show = false;
      this.cancelReason = '';
      this.$emit('close');
    },
    open() {
      this.show = true;
    },
    async confirm() {
      if (!this.cancelReason) return uni.$u.toast(this.confirmMessage); // 使用 confirmMessage
      this.loading = true;
      try {
        await this.cancelTask(this.cancelReason);
        uni.$u.toast('操作成功');
        this.$emit('success');
        this.close();
      } catch (e) {
        console.error(e);
        uni.$u.toast('操作失败');
        this.$emit('error', e);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style>
.popup_content {
  width: 600rpx;
  padding: 40rpx;
}

.footer_button {
  margin-top: 40rpx;
  text-align: end;
}
</style>
