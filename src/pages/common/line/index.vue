<template>
  <view class="toListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="true"
      leftText=""
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card>
        <uni-row>
          <view
            class="flex mb5 align_center"
            v-for="(item, index) in queryItems(formData.isOnline)"
            :key="index"
          >
            <span class="mr4">{{ item.name }}:</span>
            <span v-if="item.name === '当前状态'">{{
              formData.statusName
            }}</span>
            <span v-else>{{ formData.lastTime }}</span>
          </view>
        </uni-row>
        <uni-row class="flex">
          <uni-col :span="10">
            <u-button type="info" size="small" @click="reBack">返回</u-button>
          </uni-col>
          <uni-col :span="4"> </uni-col>
          <uni-col :span="10">
            <u-button size="small" type="primary" @click="switchStatus">{{
              formData.isOnline === '1' ? '离线' : '上线'
            }}</u-button>
          </uni-col>
        </uni-row>
      </uni-card>
      <view>
        <uni-section type="line" title="在线/离线记录" class="pb1 mb5">
          <uni-card>
            <u-list @scrolltolower="loadMore">
              <u-list-item v-for="(item, index) in clockRecord" :key="index">
                <view class="flex space_b align_center setTitle">
                  <view class="font-16 text_scrolling">
                    <text>{{ item.userName }}</text>
                  </view>
                  <u-tag
                    :text="`${item.isOnline == 1 ? '在线' : '离线'}`"
                    size="mini"
                    :type="`${item.isOnline == 1 ? 'success' : 'warning'}`"
                  ></u-tag>
                </view>

                <view class="flex align_center content_note">
                  <view>{{ item.deptName }}</view>
                  <view>{{ item.operateTime }}</view>
                </view>
                <view class="mt4">
                  <u-line></u-line>
                </view>
              </u-list-item>
            </u-list>
            <!-- <uni-load-more
              :status="status"
              :content-text="contentText"
              @clickLoadMore="loadMore"
            ></uni-load-more> -->
          </uni-card>
        </uni-section>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>
    <u-loading-page :loading="loading" loadingText="请稍后" />
  </view>
</template>

<script>
import {
  onlineStatusGetStatus,
  onlineStatusEditStatus,
  getLineList,
} from '@/api/line/index';
export default {
  data() {
    return {
      loading: false,
      clockRecord: [],
      title: '',
      formItem: [
        {
          prop: 'statusName',
          isOnline: '0,1',
          name: '当前状态',
        },
        {
          prop: 'offlineTime',
          isOnline: '1',
          name: '上次离线时间',
        },
        {
          prop: 'onlineTime',
          isOnline: '0',
          name: '上次上线时间',
        },
      ],
      formData: {
        isOnline: '0',
        statusName: '离线',
        lastTime: '',
      },
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      status: 'more',
      contentText: {
        contentdown: '加载更多',
        contentrefresh: '加载中',
        contentnomore: '没有更多',
      },
    };
  },
  onLoad(option) {
    this.title = option?.title || '在线/离线';
    this.init();
  },
  /*下拉刷新*/
  onPullDownRefresh() {
    this.clockRecord = [];
    this.queryForm.pageNum = 1;
    this.getList();
  },
  methods: {
    init() {
      this.getStatus();
      this.getList();
    },
    loadMore() {
      if (this.queryForm.pageNum * this.queryForm.pageSize >= this.total) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.queryForm.pageNum++;
      this.getList();
    },
    async getList() {
      try {
        const res = await getLineList(this.queryForm);
        if (res.rows?.length)
          this.clockRecord = this.clockRecord.concat(res.rows);

        this.total = res.total || 0;
      } catch {}
    },
    async getStatus() {
      const res = await onlineStatusGetStatus();
      this.formData = { ...res?.data };
      this.formData.statusName =
        this.formData.isOnline === '0' ? '离线' : '在线';
    },
    async switchStatus() {
      this.loading = true;
      if (this.formData.isOnline === '1') {
        this.formData.isOnline = '0';
      } else {
        this.formData.isOnline = '1';
      }
      const res = await onlineStatusEditStatus(this.formData.isOnline);
      if (res.code == 200) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: `${res.msg}`,
            type: 'success',
          })
        );
      } else {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: `${res.msg}`,
            type: 'error',
          })
        );
      }
      this.loading = false;
      this.getStatus();
    },
    queryItems(val) {
      return this.formItem.filter((item) => item.isOnline.includes(val));
    },
    reBack() {
      this.$tab.reLaunch('/pages/index');
    },
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding: 0;
}
.setTitle {
  height: 60rpx;
}
.content_note {
  margin-top: 3px;
  color: #999;
  font-size: 12px;
  overflow: hidden;
}
.content_note view:nth-child(1) {
  margin-right: 16rpx;
  flex-grow: 1;
}
.toListPage {
  .u-list {
    /*#ifdef H5*/
    height: calc(100vh - 508rpx) !important;
    /*#endif*/
    /*#ifdef APP-PLUS*/
    height: calc(100vh - 508rpx) !important;
    /*#endif*/
    .uni-scroll-view-content {
      .listCardItem {
        &:first-child {
          .listCard {
            margin-top: 0 !important;
          }
        }
      }
    }
  }
}
</style>
