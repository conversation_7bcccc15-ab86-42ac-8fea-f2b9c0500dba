export const myTaskColumns = [
  {
    label: '工单标题',
    prop: 'taskTitle',
    iconSrc: '',
  },
  {
    label: '流程名称',
    prop: 'procDefName',
  },
  {
    label: '处理环节',
    prop: 'taskName',
  },
  {
    label: '处理人',
    prop: 'assigneeName',
  },
  {
    label: '接收时间',
    prop: 'createTime',
  },
  {
    label: '要求完成时间',
    prop: 'requireFinishTime',
  },
  {
    label: '工单状态',
    prop: 'webFlowStatus',
  },
];
export const todoColumns = [
  {
    label: '工单标题',
    prop: 'taskTitle',
  },
  {
    label: '流程名称',
    prop: 'procDefName',
  },
  {
    label: '处理环节',
    prop: 'taskName',
  },
  {
    label: '发起人',
    prop: 'startUserName',
  },
  {
    label: '接收时间',
    prop: 'createTime',
  },
  {
    label: '要求完成时间',
    prop: 'requireFinishTime',
  },
];
export const finishedColumns = [
  {
    label: '工单标题',
    prop: 'taskTitle',
  },
  {
    label: '流程名称',
    prop: 'procDefName',
  },
  {
    label: '处理环节',
    prop: 'taskName',
  },
  {
    label: '处理人',
    prop: 'assigneeName',
  },
  {
    label: '发起人',
    prop: 'startUserName',
  },
  {
    label: '接收时间',
    prop: 'createTime',
  },
  {
    label: '审批时间',
    prop: 'finishTime',
  },
  {
    label: '要求完成时间',
    prop: 'requireFinishTime',
  },
];

export const repairColumns = [
  {
    label: '工单标题',
    prop: 'taskTitle',
  },
  {
    label: '处理环节',
    prop: 'taskName',
  },
  {
    label: '处理人',
    prop: 'assigneeName',
  },
  {
    label: '上报时间',
    prop: 'createTime',
  },
  {
    label: '工单状态',
    prop: 'finishTime',
  },
];

export const checkPlanColumns = [
  {
    label: '计划名称',
    prop: 'planName',
  },
  {
    label: '计划编号',
    prop: 'planCode',
  },
  {
    label: '计划开始时间',
    prop: 'startTime',
  },
  {
    label: '计划结束时间',
    prop: 'endTime',
  },
  {
    label: '工单状态',
    prop: 'state',
  },
];

export const checkRecordColumns = [
  {
    label: '工单标题',
    prop: 'taskTitle',
  },
  {
    label: '处理环节',
    prop: 'taskName',
  },
  {
    label: '处理人',
    prop: 'assigneeName',
  },
  {
    label: '任务开始时间',
    prop: 'createTime',
  },
  {
    label: '要求完成时间',
    prop: 'requireFinishTime',
  },
  {
    label: '工单状态',
    prop: 'finishTime',
  },
];

export const taskTypeColumns = [
  {
    label: '工单名称',
    prop: 'name',
  },
  {
    label: '工单类型',
    prop: 'type',
  },
];

export const expertListColumns = [
  {
    label: '姓名',
    prop: 'expertName',
  },
  {
    label: '专业',
    prop: 'major',
  },
  {
    label: '电话',
    prop: 'phoneNumber',
  },
  {
    label: '工作单位',
    prop: 'firm',
  },
];

export const expertFormLabels = [
  {
    label: '专家姓名',
    prop: 'expertName',
  },
  {
    label: '性别',
    prop: 'gender',
  },
  {
    label: '专家类型',
    prop: 'expertType',
  },
  {
    label: '学历',
    prop: 'expertEducation',
  },
  {
    label: '技术职称',
    prop: 'technicalTitle',
  },
  {
    label: '专业',
    prop: 'major',
  },
  {
    label: '照片',
    prop: 'ossInfo',
  },
  {
    label: '手机号码',
    prop: 'phoneNumber',
  },
  {
    label: '办公电话',
    prop: 'telNumber',
  },
  {
    label: '省份',
    prop: 'province',
  },
  {
    label: '常用邮箱',
    prop: 'email',
  },
  {
    label: '详细地址',
    prop: 'address',
  },
];

export const docFormLabels = [
  {
    label: '标题',
    prop: 'title',
  },
  {
    label: '预案类型',
    prop: 'typeName',
  },
  {
    label: '描述',
    prop: 'description',
  },
  {
    label: '上传时间',
    prop: 'createTime',
  },
];

export const emergencyPlanColumns = [
  {
    label: '预案名称',
    prop: 'planName',
  },
  {
    label: '应急预案类型',
    prop: 'typeName',
  },
  {
    label: '应急等级',
    prop: 'emergencyLevel',
  },
  {
    label: '启动次数',
    prop: 'startNum',
  },
  {
    label: '更新日期',
    prop: 'updateTime',
  },
];

export const emergencyFormLabels = [
  {
    label: '预案名称',
    prop: 'planName',
  },
  {
    label: '应急预案类型',
    prop: 'typeName',
  },
  {
    label: '应急等级',
    prop: 'emergencyLevel',
  },
  {
    label: '省批流程',
    prop: 'flowKey',
  },
  {
    label: '联系人',
    prop: 'updateTime',
  },
  {
    label: '联系电话',
    prop: 'updateTime',
  },
  {
    label: '应急设备',
    prop: 'device',
  },
];

export const emergencyRecordColumns = [
  {
    label: '预案名称',
    prop: 'planName',
  },
  {
    label: '流程编码',
    prop: 'procInsId',
  },
  {
    label: '状态',
    prop: 'isFinished',
  },
  {
    label: '发起时间',
    prop: 'createTime',
  },
  {
    label: '结束时间',
    prop: 'finishTime',
  },
  {
    label: '执行进度',
    prop: 'taskName',
  },
];

export const deviceColumns = [
  {
    prop: 'deviceCode',
    label: '设备编码',
  },
  {
    prop: 'deviceName',
    label: '设备名称',
  },
  {
    prop: 'typeName',
    label: '设备类型',
  },
  {
    prop: 'areaFullName',
    label: '所属区域',
  },
  {
    prop: 'deviceStatus',
    label: '设备状态',
  },
  {
    prop: 'isSmart',
    label: '设备属性',
  },
  {
    prop: 'deviceFactory',
    label: '厂家',
  },
  {
    prop: 'safePerson',
    label: '安全责任人',
  },
  {
    prop: 'updateTime',
    label: '最后更新时间',
  },
];

export const runningRecordColumns = [
  {
    prop: 'deviceCode',
    label: '设备编码',
  },
  {
    prop: 'deviceName',
    label: '设备名称',
  },
  {
    prop: 'areaFullName',
    label: '所属区域',
  },
  {
    prop: 'deviceStatus',
    label: '设备状态',
  },
  {
    prop: 'updateTime',
    label: '最后更新时间',
  },
];

export const fileLabels = [
  {
    label: '文件名',
    prop: 'originalName',
  },
  {
    label: '文件类型',
    prop: 'fileSuffix',
  },
  {
    label: '创建人',
    prop: 'createBy',
  },
  {
    label: '上传时间',
    prop: 'createTime',
  },
  {
    label: '服务商',
    prop: 'service',
  },
];
export const topAllTabList = [
  {
    name: '未分配',
    keyName: 0,
  },
  {
    name: '未开始',
    keyName: 1,
  },
  {
    name: '进行中',
    keyName: 2,
  },
  {
    name: '已结束',
    keyName: 3,
  },
  {
    name: '已取消',
    keyName: 4,
  },
  {
    name: '已失效',
    keyName: 5,
  },
];

export const topAllSelectList = [
  {
    text: '未分配',
    value: 0,
    checked: false,
  },
  {
    text: '未开始',
    value: 1,
    checked: false,
  },
  {
    text: '进行中',
    value: 2,
    checked: false,
  },
  {
    text: '已结束',
    value: 3,
    checked: false,
  },
  {
    text: '已取消',
    value: 4,
    checked: false,
  },
  {
    text: '已失效',
    value: 5,
    checked: false,
  },
];
export const userCarList = [
  {
    text: '绑定',
    value: '1',
    checked: false,
  },
  {
    text: '解绑',
    value: '0',
    checked: false,
  },
];
export const workTabList = [
  {
    name: '生产作业',
    keyName: 1,
  },
  {
    name: '接驳作业',
    keyName: 2,
  },
  {
    name: '普通作业',
    keyName: 3,
  },
];

export const topPassTabList = [
  {
    keyName: '0',
    name: '未到达',
  },
  {
    keyName: '1',
    name: '已到达',
  },
  {
    keyName: '2',
    name: '已离厂',
  },
];
const formItems = [
  {
    prop: 'startTime',
    status: [0, 1, 2, 4, 5, 6],
    name: '任务执行时间',
  },
  {
    prop: 'endTime',
    status: [0, 1, 2, 4, 5, 6],
    name: '任务结束时间',
  },

  {
    prop: 'actuallyStartTime',
    status: [3],
    name: '任务执行时间',
  },
  {
    prop: 'actuallyEndTime',
    status: [3],
    name: '任务结束时间',
  },
  // {
  //   prop: 'appointmentUserName',
  //   status: [0],
  //   name: '申请人',
  // },
  // {
  //   prop: 'carType',
  //   status: [0],
  //   name: '用车类型',
  // },
  // {
  //   prop: 'usingTime',
  //   status: [0],
  //   name: '用车时间',
  // },
];
const formAppointItems = [
  {
    prop: 'kindName',

    name: '任务类型',
  },
  {
    prop: 'appointmentUserName',

    name: '申请人',
  },
  {
    prop: 'usingTime',

    name: '用车时间',
  },
];
const useCarRecordItems = [
  {
    prop: 'appointmentTypeName',

    name: '预约类型',
  },
  {
    prop: 'usingTime',

    name: '用车时间',
  },
  {
    prop: 'startPointName',

    name: '起点',
  },
  {
    prop: 'endPointName',

    name: '终点',
  },
];
const stausMap = {
  0: {
    text: '未分配',
    status: '0',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
  1: {
    text: '未开始',
    status: '1',
    color: '#fff',
    bgColor: '#E56B19',
  },
  2: {
    text: '进行中',
    status: '2',
    color: '#fff',
    bgColor: '#aa001e',
  },
  3: {
    text: '已结束',

    status: '3',
    color: '#fff',
    bgColor: '#909193',
  },
  4: {
    text: '已取消',
    status: '4',
    color: '#fff',
    bgColor: '#909193',
  },
  5: {
    text: '已失效',
    status: '5',
    color: '#fff',
    bgColor: '#909193',
  },
  // 6: {

  // 	text: '已转移',
  // 	status: '6',
  // 	color: '#fff',
  // 	bgColor:'#909193',
  // },
};
const appointStausMap = {
  "-3": {
    text: '未到达',
    status: '-3',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
  "-2": {
    text: '已取消',
    status: '-2',
    color: '#fff',
    bgColor: '#909193',
  },
  "-1": {
    text: '审核拒绝',
    status: '-1',
    color: '#fff',
    bgColor: '#909193',
  },
  "0": {
    text: '未审核',
    status: '3',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
  "1": {
    // text: '已失效',
    text: '审核中',
    status: '4',
    color: '#fff',
    bgColor: '#aa001e',
  },
  "2": {
    text: '审核通过',
    status: '5',
    color: '#fff',
    bgColor: '#aa001e',
  },
};
const UserCarStatusMap = {
  0: {
    text: '解绑',
    status: '0',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
  1: {
    text: '绑定',
    status: '1',
    color: '#fff',
    bgColor: '#f9ae3d',
  },
};
const UserCarFormItem = [
  {
    prop: 'dispatchCarId',
    status: [0, 1],
    name: '作业车辆id',
  },
  {
    prop: 'bindTime',
    status: [0, 1],
    name: '绑定时间',
  },
  {
    prop: 'unbindTime',
    status: [0, 1],
    name: '解绑时间',
  },
];
const parkBusFormItem = [
  {
    prop: 'departureTimes',
    status: [],
    name: '计划发车时间',
  },
  {
    prop: 'startPoint',
    status: [],
    name: '起点',
  },
  {
    prop: 'endPoint',
    status: [],
    name: '终点',
  },
  {
    prop: 'nextPoint',
    status: [],
    name: '下一站',
  },
  {
    prop: 'arrivalNextStationTime',
    status: [],
    name: '预计到达下一站时间',
  },
];
export const getStatus = (key) => {
  return stausMap[key];
};
export const getAppointStausMap = (key) => {
  return appointStausMap[key];
};
export const getUserCarStatus = (key) => {
  return UserCarStatusMap[key];
};
export const getUserCarItems = (key) => {
  return UserCarFormItem.filter((item) => item.status.includes(Number(key)));
};
export const getItems = (key, key2) => {
  if (key2 == 'second') {
    return [];
  } else if (key2 == 'useCarRecord') {
    return useCarRecordItems;
  } else if (key2 == 'parkBus') {
    return parkBusFormItem;
  } else {
    return formItems.filter((item) => item.status.includes(Number(key)));
  }
};
