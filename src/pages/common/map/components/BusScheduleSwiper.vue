<template>
  <view style="margin:0 20px">
    <view class="flex justify-center items-center">
      <view
        style="
          background-color: #aa011c;
          color: #fff;
          padding: 4px 20px;
          border-radius: 20px;
          font-weight: 700;
          font-size: 12px;
        "
      >
        园区公交发车时刻表 (工作日)
      </view>
      
    </view>
    <swiper
      class="swiper"
      :indicator-dots="false"
      :autoplay="false"
     
    >
      <swiper-item
        v-for="(route, index) in sourcesData"
        :key="index"
        :indicator-dots="false"
      >
        <view
          class="flex justify-between items-center"
          style="
            padding: 10px 40px 10px 40px;
            color: #aa011c;
            font-size: 12px;
            font-weight: 700;
          "
        >
          <view style="width: 50%; text-align: center">
            {{ route.startPoint }}
          </view>
          <view style="width: 50%; text-align: center">
            {{ route.endPoint }}
          </view>
        </view>
        <view class="flex justify-between" style="height: 100%;">
          <view
            style="
              width: 49%;
              height: 90%;
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              justify-content: center;
              align-items: center;
              background:#f7f6f6;
              border-radius: 20px;
            "
          >
          
             <view
              v-for="(item, timeIndex) in route.departureTime[0]"
              :key="timeIndex"
               :style="getTimeItemStyle(timeIndex, route.departureTime[0].length)"
            >
              {{ item.time}}
            </view> 
          </view>
         <view
            style="
              width: 49%;
              height: 90%;
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              justify-content: center;
              align-items: center;
              background:#f7f6f6;
              border-radius: 20px;
            "
          >
            <view
              v-for="(item, timeIndex) in route.departureTime[1]"
              :key="timeIndex"
              :style="getTimeItemStyle(timeIndex, route.departureTime[1].length)"
            >
              {{ item.time }}
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: "BusScheduleSwiper",
  props: {
    sourcesData: {
      type: Array,
      default: () => [
       
      ],
    },
  },
  computed: {
    columnCount() {
      return 8; // 每行显示8列，因为width: calc(12.5% - 10px)中12.5% = 100% / 8
    }
  },
  methods: {
    getTimeItemStyle(index, length) {
      const row = Math.floor(index / this.columnCount);
      const isInLastRow = Math.floor(index / this.columnCount) === Math.floor((length - 1) / this.columnCount);
      return {
        backgroundColor: '#f5f5f5',
        borderRadius: '6px',
        padding: '10px 0',
        minWidth: '40px',
        textAlign: 'center',
        fontSize: '10px',
        width: `calc(${100 / this.columnCount}% - 10px)`,
        height: '20px',
        marginBottom: '10px',
        marginLeft: isInLastRow ? '0' : 'auto',
        color: row % 2 === 0 ? '#333' : '#AA011C'
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.swiper {
  width: 100%;
  height:280px;
}
</style>
