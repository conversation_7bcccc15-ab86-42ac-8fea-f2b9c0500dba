<template>
  <!-- 国家秘密标识示例（根据实际需求添加） -->
  <!-- <view class="secret-mark">机密★10年</view> -->

  <!-- 动态列布局容器 -->
  <view class="container">
    <!-- 循环渲染多列 -->
    <view 
      v-for="(column, colIndex) in splitData" 
      :key="colIndex" 
      class="column"
    >
      <!-- 每列中的内容项 -->
      <view 
        v-for="item in column" 
        :key="item.id" 
        class="item"
        @click="handleItemClick(item)"
      >
        <image 
          :src="item.image" 
          mode="widthFix" 
          class="item-image"
        />
        <text class="item-title">{{ item.title }}</text>
        <text class="item-desc">{{ item.description }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 模拟后端返回的数据
      dataList: [
        { id: 1, title: '项目1', description: '描述内容...', image: '/static/1.jpg' },
        { id: 2, title: '项目2', description: '描述内容...', image: '/static/2.jpg' },
        // ...更多数据项（可测试不同数据量效果）
      ]
    }
  },
  computed: {
    // 动态计算列数（根据数据量自动调整）
    columnCount() {
      const len = this.dataList.length
      if (len <= 4) return 2      // 小数据量双列
      if (len <= 8) return 3      // 中等数据量三列
      return 4                    // 大数据量四列
    },

    // 将数据拆分为多列二维数组
    splitData() {
      const chunkSize = Math.ceil(this.dataList.length / this.columnCount)
      return Array.from({ length: this.columnCount }, (_, i) =>
        this.dataList.slice(i * chunkSize, (i + 1) * chunkSize)
      )
    }
  },
  methods: {
    // 点击事件处理
    handleItemClick(item) {
      uni.showToast({
        title: `选中项目：${item.title}`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 容器基础样式 */
.container {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f5f5f5;
}

/* 单列样式 */
.column {
  flex: 1;
  margin: 0 10rpx;
  min-width: 0; /* 解决flex布局内容溢出问题 */
}

/* 内容项样式 */
.item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);

  &-image {
    width: 100%;
    border-radius: 8rpx;
  }

  &-title {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin: 15rpx 0 10rpx;
    font-weight: bold;
  }

  &-desc {
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
  }
}

/* 响应式适配 */
@media (max-width: 480px) {
  .container {
    flex-wrap: wrap;
  }
  
  .column {
    flex: 0 0 100%;
    margin: 0;
    
    .item {
      margin-bottom: 15rpx;
    }
  }
}

/* 国家秘密标识样式示例 */
.secret-mark {
  position: fixed;
  top: 20rpx;
  left: 20rpx;
  color: red;
  font-size: 24rpx;
  z-index: 999;
}
</style>
