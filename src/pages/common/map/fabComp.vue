<template>
    <div class="fab-container">
      <button class="fab" @click="onClick">
        <slot></slot> 
      </button>
    </div>
  </template>
   
  <script>
  export default {
    name: 'FabButton',
    methods: {
      onClick() {
        this.$emit('click'); // 触发点击事件
      }
    }
  }
  </script>
   
  <style scoped>
  .fab-container {
    position: fixed;
    top: 88px;
    left: 20px;
  }
  .fab {
    background-color: #fff; /* 按钮颜色 */
    border: none;
    border-radius:20%;
    color: #333;
    cursor: pointer;
    outline: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 阴影效果 */;
    width: 60px;
    height: 60px;
    font-size: 10px;
  }
  </style>