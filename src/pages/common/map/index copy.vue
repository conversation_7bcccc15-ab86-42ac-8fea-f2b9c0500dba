<template>
  <view style="overflow: hidden">
    <top-navbar
      bgColor="#AA001E"
      title="园区公交"
      :autoBack="autoBack"
      leftText=""
      :safeAreaInsetTop="true"
    >
    </top-navbar>
    <MapWapper
      :allPointData="allPointData"
      :realTimeRoute="realTimeRoute"
      :center="centerData"
      :allBusCarLocations="allBusCarLocations"
    />
    <limeFloatingPanel
      :isExpanded="isExpanded"
      style="z-index: 9999; overflow-y: auto"
    >
      <view class="flex justify-between items-center" style="margin: 0 20px">
        <view>
          <u--text
            :lines="1"
            size="22px"
            bold
            color="#333"
            :text="busName"
          ></u--text>
        </view>
        <view
          class="flex justify-between items-center"
          style="flex-direction: row; align-items: center"
        >
          <view
            class="flex justify-center items-center"
            style="
              background: #aa011c;
              color: #fff;
              border-radius: 10px;
              height: 24px;
              line-height: 24px;
              padding: 0 10px;
              font-size: 12px;
            "
            @click="handleSchedule"
          >
            <image
              style="width: 12px; height: 14px; margin: 6px 4px"
              src="/static/images/bus/时刻表.png"
              mode="scaleToFill"
            />
            出发时刻表
          </view>
          <view
            class="flex justify-center items-center"
            style="
              background: #aa011c;
              color: #fff;
              border-radius: 10px;
              height: 24px;
              line-height: 24px;
              padding: 0 10px;
              font-size: 12px;
              margin-left: 10px;
            "
            @click="handleReversing"
            v-if="routeList && routeList.length > 1"
          >
            <image
              style="width: 12px; height: 14px; margin: 6px 4px"
              src="/static/images/bus/切换方向.png"
              mode="scaleToFill"
            />
          </view>
        </view>
      </view>
      <view style="margin: 0 20px; color: #999999; font-size: 12px">
        {{ departureTime || "暂无" }}
      </view>
      <StepProgress ref="stepProgress" :steps="pointList"  :currentPoint="currentPoint"  />
      <view class="flex justify-between items-center" style="margin: 10px 16px">
        <view
          style="
            width: 46%;
            background-color: #f7f6f6;
            height: 80px;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <view>
            {{ tips }}
          </view>
          <view style="display: flex; align-items: center">
            <image
              style="width: 12px; height: 14px; margin: 0 4px"
              src="/static/images/bus/提醒.png"
              mode="scaleToFill"
            />
            {{ currentPoint || "暂无" }}
          </view>
        </view>
        <view
          style="
            width: 46%;
            background-color: #f7f6f6;
            height: 80px;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <view> 下一班发车时间 </view>
          <view>
            {{ nextDeparture || "暂无" }}
          </view>
        </view>
      </view>
      <!-- <BusScheduleSwiper :sourcesData="formartDate" /> -->
      <view style="margin: 0 20px">
        <view class="flex justify-center items-center">
          <view
            style="
              background-color: #aa011c;
              color: #fff;
              padding: 4px 20px;
              border-radius: 20px;
              font-weight: 700;
              font-size: 12px;
            "
          >
            园区公交发车时刻表 (工作日)
          </view>
        </view>
        <u-list
          @scrolltolower="scrolltolower"
          :height="240"
          style="margin: 10px 0; text-align: center"
        >
          <u-list-item
            v-for="(item, index) in tableData"
            :key="index"
            v-if="tableData && tableData.length > 0"
          >
            <view
              :style="{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '0 10px',
                height:
                  item.startPoint.length > 9 || item.endPoint.length > 9
                    ? '60px'
                    : '30px',
                lineHeight:
                  item.startPoint.length > 9 || item.endPoint.length > 9
                    ? '30px'
                    : '30px',
                alignItems: 'center',
              }"
            >
              <text style="width: 20%; text-align: left">{{ item.time }}</text>
              <text
                style="width: 40%; text-align: center; word-break: break-all"
                >{{ item.startPoint }}</text
              >
              <text
                style="width: 40%; text-align: right; word-break: break-all"
                >{{ item.endPoint }}</text
              >
            </view>
          </u-list-item>
        </u-list>
      </view>
    </limeFloatingPanel>
  </view>
</template>

<script>
import StepProgress from "./progress.vue";
import { queryWorkLinesRequest, getBusInfo } from "@/api/parkBus/index";
import limeFloatingPanel from "./limeFloatingPanel.vue";
import MapWapper from "./mapWapper.vue";
import BusScheduleSwiper from "./components/BusScheduleSwiper.vue";

export default {
  name: "map",
  components: {
    StepProgress,
    limeFloatingPanel,
    MapWapper,
    BusScheduleSwiper,

  },
  data() {
    return {
      leftText: "",
      panelData: {},
      markerLng: 119.19181483946939,
      markerLat: 34.01346557127052,
      latitude: 34.01346557127052,
      longitude: 119.19181483946939,
      scale: 40,
      controls: [],
      markers: [
        {
          id: 1,
          latitude: 34.01346557127052,
          longitude: 119.19181483946939,
          title: "默认位置",
          iconPath: "/static/pin.png",
          width: 32,
          height: 32,
        },
      ],
      itemData: {
        status: 1,
      },
      autoBack: true,
      title: "uni-fab",
      directionStr: "垂直",
      horizontal: "left",
      vertical: "bottom",
      direction: "horizontal",
      pattern: {
        color: "#333333",
        backgroundColor: "#fff",
        selectedColor: "#007AFF",
        buttonColor: "#fff",
        iconColor: "#007AFF",
      },
      is_color_type: false,

      pickerShow: false,
      pickerColumns: [],
      startTime: "08:00",
      endTime: "18:00",
      busName: "发车时间",
      forwardDirection: false,
      sourcesData: [],
      currentStep: 0,
      polyline: [],
      pointList: [],
      tableData: [],
      tips: "当前站点",
      suggest: "建议乘坐下一站",
      isExpanded: false,
      nextDeparture: "",
      allPointData: [],
      formartDate: [],
      realTimeRoute: [],
      currentPoint: "",
      centerData: {},
      hasReloaded: false, // 防止重复刷新
      routeList: [],
      currentLineData: {},
      departureTime: "",
      pollingTimer: null, // 轮询定时器
      pollingInterval: 10000, // 轮询间隔，默认10秒
      isPolling: false, // 是否正在轮询
      allBusCarLocations: [],
      userSelectedRouteId: null,
    };
  },
  onLoad() {
    this.init();
  },
  onUnload() {
    // 页面卸载时清除轮询
    this.stopPolling();
  },
  methods: {
    init() {
      this.getBusRouteInfo();
    },
    // 开始轮询
    startPolling(routeId) {
      if (this.isPolling) return;

      this.isPolling = true;
      this.pollingTimer = setInterval(() => {
        if (routeId) {
          this.getBusInfoLing(routeId);
        } else if (this.currentLineData && this.currentLineData.routeId) {
          this.getBusInfoLing(this.currentLineData.routeId);
        }
      }, this.pollingInterval);
    },
    // 停止轮询
    stopPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
        this.isPolling = false;
      }
    },
    handleBackClick() {
      // 退出页面前停止轮询
      this.stopPolling();

      // #ifdef H5
      localStorage.removeItem("mapPageReloaded");

      // 使用reLaunch强制跳转到首页或其他页面
      uni.reLaunch({
        url: "/pages/index",
      });
      return;
      // #endif

      // 非H5环境
      uni.navigateBack({
        delta: 1,
        fail: function () {
          uni.switchTab({
            url: "/pages/index/index",
          });
        },
      });
    },

    handleSchedule() {
      this.isExpanded = !this.isExpanded;
    },
    //换向
    handleReversing() {
         console.log("-切换线路-")
      this.forwardDirection = !this.forwardDirection;
      let currentIndex = this.currentIndex || 0;

      if (this.routeList && this.routeList.length > 0) {
        // 强制更新一些关键数据，即使是第一次点击
        const newIndex = (currentIndex + 1) % this.routeList.length;
        this.currentIndex = newIndex;
        this.currentLineData = { ...this.routeList[newIndex] }; // 使用展开运算符创建新对象
        this.userSelectedRouteId = this.currentLineData.routeId; // 记录用户选择的线路ID

        if (this.currentLineData.pointList) {
          // 先更新当前点位名称，确保在提取数据前已更新
          this.currentPoint = this.currentLineData.currentPoint.gcj02.pointName;

          // 强制创建新数组，触发视图更新
          this.pointList = [
            ...this.extractGCJ02Data(this.currentLineData.pointList),
          ];
          this.realTimeRoute = this.currentLineData.routePlan;
          // 确认提取后的数据是否正确设置了status

          this.allPointData = [
            ...this.pointList.map((item) => ({
              lng: item.lng,
              lat: item.lat,
              name: item.pointName,
            })),
          ];

          // 修改其他可观察数据
          this.nextDeparture =
            this.currentLineData.nextDeparture || "路线" + newIndex;
          this.departureTime = this.currentLineData.departureTime;
        }

        // 强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate();

          // 在视图更新后，使用新的 currentPoint 居中显示
          if (this.$refs.stepProgress) {
            console.log('切换线路后居中显示新的当前点:', this.currentPoint);
            this.$refs.stepProgress.centerStep(this.currentPoint);
          }
        });
      }
    },
    //
    scrolltolower() {
      console.log("-滚动-");
    },
    commutationWayChange() {
      this.forwardDirection = !this.forwardDirection;
    },

    // 示例：让当前步骤居中显示
    centerCurrentStep() {
      if (this.$refs.stepProgress) {
        this.$refs.stepProgress.centerCurrentStep();
      }
    },

    // 示例：让指定步骤居中显示
    centerSpecificStep(stepName) {
      if (this.$refs.stepProgress) {
        this.$refs.stepProgress.centerStep(stepName);
      }
    },

    // 示例：滚动到指定位置
    scrollToPosition(position) {
      if (this.$refs.stepProgress) {
        this.$refs.stepProgress.scrollToPosition(position);
      }
    },
    async getBusRouteInfo() {
      try {
        const res = await queryWorkLinesRequest();
        res.data.forEach((item) => {
          if (item) this.sourcesData.push(item);
        });

        this.sourcesData.forEach((item) => {
          item.id = item.routeId;
          item.name = item.routeName;
        });
        this.pickerColumns = this.sourcesData;

        this.pickerConfirm(this.pickerColumns[0]);
      } catch (error) {}
    },
    pickerConfirm(e) {
      this.pickerShow = false;
      this.getBusInfoLing(e.routeId);

      // 选择路线后开始轮询
      this.stopPolling(); // 先停止之前的轮询
      this.startPolling(e.routeId);
    },
    async getBusInfoLing(val) {
      try {
        const res = await getBusInfo(val);
        if (res.code === 200) {
          this.tableData = res.data.timeTable || [];
          if (res.data.routeList && res.data.routeList.length > 0) {
            this.routeList = res.data.routeList;
            
            // 如果用户已经选择了线路，保持用户的选择
            if (this.userSelectedRouteId) {
              const selectedRoute = this.routeList.find(route => route.routeId === this.userSelectedRouteId);
              if (selectedRoute) {
                this.currentLineData = selectedRoute;
                this.currentPoint = selectedRoute.currentPoint.gcj02.pointName;
                this.departureTime = selectedRoute.departureTime;
                this.nextDeparture = selectedRoute.nextDeparture;
                this.pointList = this.extractGCJ02Data(selectedRoute.pointList);
                this.allPointData = this.pointList.map((item) => ({
                  lng: item.lng,
                  lat: item.lat,
                  name: item.pointName,
                }));
                this.realTimeRoute = selectedRoute.routePlan;
              }
            } else {
              // 如果没有用户选择，使用第一条线路
              this.currentPoint = res.data.routeList[0].currentPoint.wgs84.pointName;
              this.departureTime = res.data.routeList[0].departureTime;
              this.nextDeparture = res.data.routeList[0].nextDeparture;
              this.pointList = this.extractGCJ02Data(res.data.routeList[0].pointList);
              this.allPointData = this.pointList.map((item) => ({
                lng: item.lng,
                lat: item.lat,
                name: item.pointName,
              }));
              this.realTimeRoute = res.data.routeList[0].routePlan;
              this.currentLineData = res.data.routeList[0];
            }
          } else {
            this.routeList = [];
            this.currentPoint = "";
            this.departureTime = "";
            this.nextDeparture = "";
            this.pointList = [];
            this.allPointData = [];
            this.realTimeRoute = [];
            this.currentLineData = {};
          }
          this.centerData = { lat: 34.00776409604684, lng: 119.1852291724374 };
          this.allBusCarLocations = res.data.allBusCarLocations;
        } else {
          console.error("获取公交信息失败:", res.msg || "未知错误");
        }
      } catch (error) {
        console.error("获取公交信息异常:", error);
      }

      // 数据加载完成后强制刷新
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    transformRoutes(routes) {
      return routes.map((route) => {
        const departureTimeArray = [];
        // 处理起点出发时间
        const startTimes = route.departureTime[route.startPoint] || [];
        departureTimeArray.push(startTimes.map((time) => ({ time })));
        // 处理终点出发时间
        const endTimes = route.departureTime[route.endPoint] || [];
        departureTimeArray.push(endTimes.map((time) => ({ time })));

        return {
          routeId: route.routeId,
          routeName: route.routeName,
          startPoint: route.startPoint,
          endPoint: route.endPoint,
          departureTime: departureTimeArray,
        };  
      });
    },
    pickerCancel() {
      this.pickerShow = false;
    },
    handleFabClick() {
      console.log("FAB clicked!"); // 处理点击事件
      this.pickerShow = true;
    },
    handleMarkerTap(e) {
      console.log("Marker tapped!", e); // 处理标记点点击事件
    },
    handlePolylineTap(e) {
      console.log("Polyline tapped!", e); // 处理折线点击事件
    },
    handleMapTap(e) {
      console.log("--e--", e);
      try {
        if (e && e.target) {
          const { latitude, longitude } = e.target;
          console.log("点击地图坐标:", latitude, longitude);

          // 更新地图中心点
          this.latitude = latitude;
          this.longitude = longitude;

          // 添加新的标记点
          this.markers.push({
            id: this.markers.length + 1,
            latitude,
            longitude,
            title: `标记点${this.markers.length + 1}`,
            iconPath: "/static/pin.png",
            width: 32,
            height: 32,
          });
        } else {
          console.warn("未获取到点击坐标信息");
        }
      } catch (error) {
        console.error("处理地图点击事件出错:", error);
      }
    },
    trigger(e) {
      console.log(e);
      this.content[e.index].active = !e.item.active;
      uni.showModal({
        title: "提示",
        content: `您${this.content[e.index].active ? "选中了" : "取消了"}${
          e.item.text
        }`,
        success: function (res) {
          if (res.confirm) {
            console.log("用户点击确定");
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    fabClick() {
      uni.showToast({
        title: "点击了悬浮按钮",
        icon: "none",
      });
    },
    switchBtn(hor, ver) {
      if (hor === 0) {
        this.direction = this.direction === "horizontal" ? "垂直" : "水平";
        this.directionStr = this.direction === "horizontal" ? "垂直" : "水平";
      } else {
        this.horizontal = hor;
        this.vertical = ver;
      }
      this.$forceUpdate();
    },

    handleScaleChange(e) {
      // console.log("缩放比例:", e.detail.scale);
      // this.scale = e.detail.scale;
    },
    // 在methods中添加获取地图实例的方法
    getMapContext() {
      const mapContext = this.$refs.mapRef;
      return mapContext;
    },
    /**
     * 提取数据中的gcj02坐标数据并平铺结构
     * @param {Array} originalArray 原始坐标数据数组
     * @returns {Array} 仅包含平铺gcj02数据的新数组
     */
    extractGCJ02Data(originalArray) {
      return originalArray.map((item) => {
        const pointName = item.gcj02.pointName;
        const isCurrentPoint =
          String(pointName).trim() === String(this.currentPoint).trim();

        // console.log(
        //   `比较: "${pointName}" 与 "${this.currentPoint}" 结果: ${isCurrentPoint}`
        // );

        return {
          pointName: pointName,
          lng: item.gcj02.lng,
          lat: item.gcj02.lat,
          status: isCurrentPoint ? "1" : item.gcj02.status || "0",
        };
      });
    },
    convertFuctureToPlArray(data) {
      // 处理 dat 对象格式
      if (data && !Array.isArray(data) && data.departureTime) {
        // 从 dat 对象中提取数据
        const result = [];

        // 遍历 departureTime 对象的所有键（站点名称）
        Object.keys(data.departureTime).forEach((stationName) => {
          // 获取该站点的发车时间数组
          const times = data.departureTime[stationName];

          if (Array.isArray(times) && times.length > 0) {
            // 创建符合要求的对象格式
            result.push({
              startPoint: data.startPoint || "",
              endPoint: data.endPoint || "",
              departureTime: times.map((time) => ({ time: time || "" })),
            });
          }
        });

        return result;
      }

      // 原有的数组处理逻辑
      if (!data || !Array.isArray(data)) {
        console.warn("时刻表数据格式无效");
        return [];
      }

      return data
        .map((item) => {
          if (!item || typeof item !== "object") {
            console.warn("时刻表项数据格式无效");
            return null;
          }

          // 增强健壮性处理
          let departureTimeValues = [];

          if (item.departureTime) {
            const values = Object.values(item.departureTime);
            if (values.length > 0 && Array.isArray(values[0])) {
              departureTimeValues = values[0];
            }
          }

          // 确保即使没有时间数据也返回一个有效对象
          return {
            startPoint: item.startPoint || "",
            endPoint: item.endPoint || "",
            departureTime: Array.isArray(departureTimeValues)
              ? departureTimeValues.map((time) => ({ time: time || "" }))
              : [], // 如果不是数组，返回空数组
          };
        })
        .filter(Boolean); // 过滤掉 null 值
    },
  },
};
</script>
<style lang="scss"></style>
