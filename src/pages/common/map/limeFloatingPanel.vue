<template>
  <l-floating-panel v-model:height="height" :anchors="anchors" ref="floatingPanel" :style="panelStyle">
    <slot></slot>
  </l-floating-panel>
</template>
 
<script>
import { ref, onMounted, watch, computed } from '@vue/composition-api';
import lFloatingPanel from '@/uni_modules/lime-floating-panel/components/l-floating-panel/l-floating-panel.vue';
export default {
  components: {
    lFloatingPanel
  },
  props: {
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const {windowHeight,windowWidth} = uni.getSystemInfoSync()
    const anchors = [
            340,
            Math.round(0.8 * windowHeight),
    ];
    const height = ref(anchors[0]);
    const floatingPanel = ref(null);
    
    // 添加样式计算属性
    const panelStyle = computed(() => {
      return {
        width: '100%',
        maxWidth: windowWidth + 'px'
      }
    });

    watch(() => props.isExpanded, (newVal) => {
      if (floatingPanel.value) {
        floatingPanel.value.toAnchor(newVal ? 1 : 0);
      }
    });
    
    // 添加延迟初始化逻辑
    onMounted(() => {
      // 使用延迟确保组件完全渲染后再设置宽度
      setTimeout(() => {
        // 强制更新组件宽度
        if (floatingPanel.value) {
          const panelEl = floatingPanel.value.$el;
          if (panelEl) {
            const panelArea = panelEl.querySelector('.l-floating-panel-area');
            if (panelArea) {
              panelArea.style.width = '100%';
              panelArea.style.maxWidth = windowWidth + 'px';
            }
          }
        }
      }, 100);
    });

    return { anchors, height, floatingPanel, panelStyle };
  }
}
</script>

<style>
/* 添加全局样式修复 */
.l-floating-panel-area {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}
</style>