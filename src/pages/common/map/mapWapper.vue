<template>
  <div>
    <div id="txMapContainer"></div>
    <mapcontrol
      @change-map-type="changeBaseMap"
      @set-center="setMapCenter"
      @set-limit-bounds="setLimitBounds"
      @remove-limit-bounds="removeLimitBounds"
    />
  </div>
</template>
<script>
import mapcontrol from "./mapcontrol.vue";
export default {
  name: "mapWapper",
  components: {
    mapcontrol,
  },
  props: {
    allPointData: {
      type: Array,
      default: () => [],
    },
    realTimeRoute: {
      type: Array,
      default: () => [],
    },
    center: {
      type: Object,
      default: () => ({
        lat: 34.00776409604684,
        lng: 119.1852291724374,
      }),
    },
    allBusCarLocations: {
      type: Array,
      default: () => [],
    },
  },
  // watch
  watch: {
    allPointData: {
      handler(newVal) {
        // 在这里处理 allPointData 的变化

        if (
          !newVal ||
          !Array.isArray(newVal) ||
          !this.marker ||
          typeof this.marker.setGeometries !== "function"
        )
          return;

        if (newVal.length < 1) return; // 确保至少有一个点
        // 创建新的几何体数组
        const geometries = [];
        // 如果有数据点，使用第一个点作为起点
        geometries.push({
          id: "start-marker",
          styleId: "start", // 使用起点样式
          position: new TMap.LatLng(newVal[0].lat, newVal[0].lng), // 使用第一个点作为起点
          properties: {
            title: "起点",
          },
        });
        // 如果有多个点，使用最后一个点作为终点
        if (newVal.length > 1) {
          geometries.push({
            id: "end-marker",
            styleId: "end", // 使用终点样式
            position: new TMap.LatLng(
              newVal[newVal.length - 1].lat,
              newVal[newVal.length - 1].lng
            ), // 使用最后一个点作为终点
            properties: {
              title: "终点",
            },
          });
        }

        // 添加来自 allPointData 的标记点
        newVal.forEach((point, index) => {
          geometries.push({
            id: `point-${index}`,
            styleId: "small", // 使用small样式
            position: new TMap.LatLng(point.lat, point.lng), // 使用数据中的经纬度
            properties: {
              title: point.name || point.content, // 使用数据中的名称
            },
            // content: point.name || point.content, // 标注点文本
          });
        });

        // 更新标记
        this.marker.setGeometries(geometries);
      },
      immediate: true, // 组件创建时立即执行一次
      deep: true, // 深度监听对象内部变化
    },
    realTimeRoute: {
      handler(newVal) {
        if (
          !newVal ||
          !Array.isArray(newVal) ||
          newVal.length < 2 ||
          !this.polylineLayer
        )
          return;
        if (newVal.length < 2) return;
        // 更新 paths 数组
        this.paths = newVal.map(
          (point) => new TMap.LatLng(point.lat, point.lng)
        );

        // 创建新的 rainbowPaths 数组
        const rainbowPaths = [];
        for (let i = 0; i < this.paths.length - 1; i++) {
          rainbowPaths.push({
            path: [this.paths[i], this.paths[i + 1]],
            color: "rgba(0, 180, 0, 1)",
          });
        }

        // 更新 polylineLayer
        this.polylineLayer.setGeometries([
          {
            styleId: "style_blue",
            rainbowPaths: rainbowPaths,
          },
        ]);
      },
      immediate: true,
      deep: true,
    },
    allBusCarLocations: {
      handler(newVal) {
        if (newVal && Array.isArray(newVal) && newVal.length > 0) {
          this.updateRealTimePositions(newVal);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      txMap: null,
      txMapCenter: null,
      paths: [],
      polylineLayer: null,
      clickPosition: "", // 添加新的数据属性用于存储点击位置
      marker: null,
      isLimitBoundsEnabled: true, //
      realTimePositions: [], // 添加实时位置数据数组
      markerStyles: {}, // 添加这一行，用于保存样式
      infoWindows: [],
      updateTimer: null, // 添加这一行，用于存储定时器引用
      infoWindowTimers: [], // 添加这一行，用于存储信息窗口相关的定时器
    };
  },
  mounted() {
    // 等待TMap加载完成
    if (window.TMap) {
      this.init();
    } else {
      const checkTMap = () => {
        if (window.TMap) {
          this.init();
        } else {
          setTimeout(checkTMap, 100);
        }
      };
      checkTMap();
    }
  },
  methods: {
    init() {
      this.txMapCenter = new TMap.LatLng(34.00776409604684, 119.1852291724374);
      if (
        this.realTimeRoute &&
        Array.isArray(this.realTimeRoute) &&
        this.realTimeRoute.length >= 2
      ) {
        this.paths = this.realTimeRoute.map(
          (point) => new TMap.LatLng(point.lat, point.lng)
        );
      } else {
        // 使用默认的 paths

        this.paths = [
          {
            lng: 119.189109,
            lat: 34.003248,
          },
          {
            lng: 119.191047,
            lat: 34.002901,
          },
          {
            lng: 119.191458,
            lat: 34.00447,
          },
        ];
      }
      //定义map变量，调用 TMap.Map() 构造函数创建地图
      this.txMap = new TMap.Map(document.getElementById("txMapContainer"), {
        center: this.txMapCenter, //设置地图中心点坐标
        zoom: 13, //设置地图缩放级别
        pitch: 10, //设置俯仰角
        rotation: 20, //设置地图旋转角度
        baseMap: {
          // 设置卫星地图
          type: "vector",
        },
      });
      this.txMap.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM);
      this.txMap.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE);
      this.txMap.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
      this.polylineLayer = new TMap.MultiPolyline({
        map: this.txMap,
        styles: {
          style_blue: new TMap.PolylineStyle({
            color: "#3777FF",
            width: 10,
            borderWidth: 0,
            showArrow: true,
            arrowOptions: {
              space: 70,
            },
            lineCap: "round",
          }),
        },
        geometries: [
          {
            styleId: "style_blue",
            rainbowPaths:
              this.paths.length >= 2 ? this.createRainbowPaths(this.paths) : [],
          },
        ],
      });
      this.txMap.on("click", (evt) => {
        const lat = evt.latLng.getLat().toFixed(6);
        const lng = evt.latLng.getLng().toFixed(6);
        this.clickPosition = `${lat},${lng}`;
      });

      //初始化marker
      this.marker = new TMap.MultiMarker({
        id: "marker-layer",
        map: this.txMap,
        styles: {
          // 定义起点样式https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-start.png
          start: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: "https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-start.png",
          }),
          // 定义终点样式 https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-end.png
          end: new TMap.MarkerStyle({
            width: 25,
            height: 35,
            anchor: { x: 16, y: 32 },
            src: "https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-end.png",
          }),

          small: new TMap.MarkerStyle({
            // 点标注的相关样式
            width: 20, // 宽度
            height: 20, // 高度
            anchor: { x: 17, y: 23 }, // 标注点图片的锚点位置
            src: "data:image/png;base64,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", // 标注点图片url或base64地址
            color: "#333", // 标注点文本颜色
            size: 16, // 标注点文本文字大小
            direction: "bottom", // 标注点文本文字相对于标注点图片的方位
            offset: { x: 0, y: 8 }, // 标注点文本文字基于direction方位的偏移属性
            strokeColor: "#fff", // 标注点文本描边颜色
            strokeWidth: 2, // 标注点文本描边宽度
          }),
          // 添加实时位置的样式
          realtime: new TMap.MarkerStyle({
            width: 32,
            height: 32,
            anchor: { x: 16, y: 16 },
            src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png",
          }),
        },
        geometries: [],
      });
      // 添加marker点击事件监听
      this.marker.on("click", (evt) => {
        const { latLng, properties, id, content } = evt;
        const lat = latLng.getLat().toFixed(6);
        const lng = latLng.getLng().toFixed(6);
        const name =
          properties && properties.title ? properties.title : content || "";

        // 将点击信息发送给父组件
        this.$emit("marker-click", {
          lat,
          lng,
          name,
          id,
          position: `${lat},${lng}`,
        });
      });
      // 设置默认的地图移动边界限制
      if (this.isLimitBoundsEnabled) {
        this.setLimitBounds(
          { lat: 33.98981271513883, lng: 119.16046651161389 }, // 西南角坐标
          { lat: 34.036056860444155, lng: 119.21377142670633 } // 东北角坐标
        );
      }

      // 定时更新示例
      this.updateInterval = setInterval(() => {
        // 更新实时位置标记
        this.updateRealTimePositions(this.allBusCarLocations);
      }, 5000); // 每5秒更新一次
    },
    // 添加一个辅助方法来创建 rainbowPaths
    createRainbowPaths(paths) {
      if (!paths || paths.length < 2) return [];

      const rainbowPaths = [];
      for (let i = 0; i < paths.length - 1; i++) {
        rainbowPaths.push({
          path: [paths[i], paths[i + 1]],
          color: "rgba(0, 180, 0, 1)",
        });
      }
      return rainbowPaths;
    },
    // 添加切换地图类型的方法
    changeBaseMap(type) {
      if (!this.txMap) return;

      this.txMap.setBaseMap({
        type: type, // 'satellite'为卫星图，'vector'为矢量图
      });
    },
    /**
     * 设置地图中心点
     * @param {Object} center - 包含 lat 和 lng 属性的对象
     * @param {Number} zoom - 可选，地图缩放级别
     */
    setMapCenter(center, zoom) {
      if (!this.txMap) return;

      if (
        center &&
        typeof center === "object" &&
        "lat" in center &&
        "lng" in center
      ) {
        const newCenter = new TMap.LatLng(center.lat, center.lng);

        // 设置地图中心点
        this.txMap.setCenter(newCenter);

        // 如果提供了缩放级别，也一并设置
        if (zoom !== undefined && typeof zoom === "number") {
          this.txMap.setZoom(zoom);
        }

        // 更新内部存储的中心点
        this.txMapCenter = newCenter;
      }
    },

    /**
     * 设置地图移动边界限制
     * @param {Object} sw - 西南角坐标，包含 lat 和 lng 属性
     * @param {Object} ne - 东北角坐标，包含 lat 和 lng 属性
     */
    setLimitBounds(sw, ne) {
      if (!this.txMap) return false;

      if (
        sw &&
        ne &&
        "lat" in sw &&
        "lng" in sw &&
        "lat" in ne &&
        "lng" in ne
      ) {
        // 创建西南角和东北角的坐标点
        const swPoint = new TMap.LatLng(sw.lat, sw.lng);
        const nePoint = new TMap.LatLng(ne.lat, ne.lng);

        // 创建边界对象
        const boundary = new TMap.LatLngBounds(swPoint, nePoint);

        // 使用 fitBounds 方法将地图视图调整到指定边界
        this.txMap.setBoundary(boundary);

        // 注意：腾讯地图API没有直接限制地图移动范围的方法
        // 我们可以监听地图移动事件，如果超出边界则重置
        this.txMap.on("dragend", () => {
          const center = this.txMap.getCenter();
          if (!boundary.contains(center)) {
            // 如果中心点超出边界，将地图重新调整到边界内
            this.txMap.setBoundary(boundary);
          }
        });

        return true;
      } else {
        console.error(
          "设置地图边界限制失败：参数格式不正确，需要包含 lat 和 lng 属性的 sw 和 ne 对象"
        );
        return false;
      }
    },

    /**
     * 移除地图移动边界限制
     */
    removeLimitBounds() {
      if (!this.txMap) return false;

      // 移除地图的移动边界限制
      this.txMap.setLimitBounds(null);

      return true;
    },
    /**
     * 更新实时位置标记
     * @param {Array} positions - 包含实时位置的数组，每个位置应有 lat 和 lng 属性
     */
    updateRealTimePositions(positions) {
      if (
        !this.txMap ||
        !this.marker ||
        !positions ||
        !Array.isArray(positions)
      )
        return;

      if (
        !this.txMap ||
        !this.marker ||
        !positions ||
        !Array.isArray(positions)
      )
        return;

      // 清除之前的信息窗口
      if (this.infoWindows && this.infoWindows.length) {
        this.infoWindows.forEach((infoWindow) => {
          if (infoWindow) {
            try {
              infoWindow.close();
            } catch (e) {
              console.error("关闭信息窗口时出错:", e);
            }
          }
        });
        this.infoWindows = [];
      } else {
        this.infoWindows = [];
      }
      // 保存实时位置数据
      this.realTimePositions = positions;
      // 获取当前所有几何体
      const currentGeometries = this.marker.getGeometries() || [];

      // 过滤掉之前的实时位置标记，保留其他标记（如起点、终点等）
      const filteredGeometries = currentGeometries.filter(
        (geo) => !geo.id || !geo.id.startsWith("realtime-position-")
      );

      // 获取当前的样式集合
      const currentStyles = this.marker.getStyles() || {};

      // 为每个车辆创建单独的样式
      const newStyles = {};
      positions.forEach((pos, index) => {
        // 将方向值用360减去，实现方向转换
        const direction =
          pos.direction !== undefined ? 360 - Number(pos.direction) : 0;
        const styleId = `realtime-${index}`;

        newStyles[styleId] = new TMap.MarkerStyle({
          width: 32,
          height: 32,
          anchor: { x: 16, y: 16 },
          src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/car.png",
          faceTo: "map",
          rotate: direction, // 在样式中直接设置旋转角度
        });
      });

      // 合并样式，保留原有样式
      const mergedStyles = { ...currentStyles, ...newStyles };

      // 更新样式
      this.marker.setStyles(mergedStyles);

      // 添加新的实时位置标记
      const realtimeGeometries = positions.map((pos, index) => {
        // 检查车辆是否停车超过1分钟
        let parkingMinutes = 0;
        let showInfoWindow = false;

        if (pos.gpsTime) {
          try {
            const gpsTime = new Date(pos.gpsTime).getTime();
            const currentTime = new Date().getTime();
            const timeDiff = currentTime - gpsTime; // 时间差（毫秒）

            if (timeDiff > 60000) {
              // 如果时间差大于1分钟（60000毫秒）
              // 计算停车时间的月、天、小时、分钟
              const minutes = Math.floor(timeDiff / 60000) % 60;
              const hours = Math.floor(timeDiff / (60000 * 60)) % 24;
              const days = Math.floor(timeDiff / (60000 * 60 * 24)) % 30;
              const months = Math.floor(timeDiff / (60000 * 60 * 24 * 30));

              // 构建停车时间字符串
              let parkingTimeStr = "";
              if (months > 0) {
                parkingTimeStr += `${months}个月`;
              }
              if (days > 0) {
                parkingTimeStr += `${days}天`;
              }
              if (hours > 0) {
                parkingTimeStr += `${hours}小时`;
              }
              if (minutes > 0 || parkingTimeStr === "") {
                parkingTimeStr += `${minutes}分钟`;
              }

              parkingMinutes = Math.floor(timeDiff / 60000); // 保留总分钟数用于其他计算
              showInfoWindow = true;

              // 将停车时间字符串保存到位置对象中
              pos.parkingTimeStr = parkingTimeStr;
            }
          } catch (e) {
            console.error("计算停车时间时出错:", e);
          }
        }

        // 将停车信息保存到标记的属性中
        return {
          id: `realtime-position-${index}`,
          styleId: `realtime-${index}`, // 使用对应的样式ID
          position: new TMap.LatLng(pos.lat, pos.lng),
          properties: {
            title: pos.carNo || `实时位置 ${index + 1}`,
            parkingMinutes: parkingMinutes,
            showInfoWindow: showInfoWindow,
            gpsTime: pos.gpsTime,
            updateTime: pos.updateTime,
            carNo: pos.carNo,
          },
        };
      });

      // 更新标记，合并保留的标记和新的实时位置标记
      this.marker.setGeometries([...filteredGeometries, ...realtimeGeometries]);
      // 清除之前的定时器
      if (this.updateTimer) {
        clearTimeout(this.updateTimer);
        this.updateTimer = null;
      }
      // 清除所有信息窗口相关的定时器
      if (this.infoWindowTimers && this.infoWindowTimers.length) {
        this.infoWindowTimers.forEach((timer) => {
          clearTimeout(timer);
        });
        this.infoWindowTimers = [];
      }
      // 延迟创建信息窗口，确保标记已经更新完成
      this.updateTimer = setTimeout(() => {
        try {
          // 只为停车超过1分钟的车辆创建信息窗口
          positions.forEach((pos, index) => {
            if (!pos.gpsTime || !pos.parkingTimeStr) return;

            try {
              // 创建信息窗口内容
              const infoContent = `
                                 <div style="padding:2px; max-width: 200px;">
                    <h4 >${pos.carNo || `车辆 ${index + 1}`}</h4>
                    <p style="margin: 5px 0; color: red; font-weight: bold;">已停车 ${
                      pos.parkingTimeStr
                    }</p>
                  </div>

              `;

              // 先创建信息窗口对象但不立即添加到地图
              const infoWindow = new TMap.InfoWindow({
                content: infoContent,
                offset: { x: 0, y: -32 },
                zIndex: 100,
                position: new TMap.LatLng(pos.lat, pos.lng),
              });

              // 先将信息窗口添加到数组
              this.infoWindows.push(infoWindow);

              // 然后再添加到地图并打开
              const timer = setTimeout(() => {
                try {
                  infoWindow.setMap(this.txMap);
                  infoWindow.open();
                } catch (e) {
                  console.error("打开信息窗口时出错:", e);
                }
              }, 100 * index); // 错开每个窗口的创建时间

              // 保存定时器引用
              this.infoWindowTimers.push(timer);
            } catch (e) {
              console.error("创建单个信息窗口时出错:", e);
            }
          });
        } catch (e) {
          console.error("创建信息窗口时出错:", e);
        }
      }, 500); // 延迟500毫秒，确保标记已经更新完成
    },
  },
  beforeDestroy() {
    if (this.txMap) {
      this.txMap.destroy();
    }
    // 清理信息窗口
    if (this.infoWindows && this.infoWindows.length) {
      this.infoWindows.forEach((infoWindow) => {
        try {
          if (infoWindow) {
            infoWindow.close();
          }
        } catch (e) {
          console.error("清理信息窗口时出错:", e);
        }
      });
      this.infoWindows = [];
    }

    // 清理定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }

    // 清理信息窗口相关的定时器
    if (this.infoWindowTimers && this.infoWindowTimers.length) {
      this.infoWindowTimers.forEach((timer) => {
        clearTimeout(timer);
      });
      this.infoWindowTimers = [];
    }

    // 清理更新间隔定时器
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    // 清理地图实例
    if (this.txMap) {
      this.txMap.destroy();
    }
  },
};
</script>
<style lang="scss" scoped>
#txMapContainer {
  position: relative;
  width: 100%;
  height: 66vh;
}
</style>
