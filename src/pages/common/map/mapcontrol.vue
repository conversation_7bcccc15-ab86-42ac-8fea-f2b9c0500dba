<template>
  <div class="map-buttons">
    <view class="map-button" @click="toggleMapType" v-show="hasChangeMapType">
      {{ isVector ? '地图' : '卫星' }}
    </view>
    <view class="map-button" @click="setCenterView" v-show="hasSetCenter">
      原点
    </view>
    <!-- <view class="map-button" @click="toggleLimitBounds">
      {{ isLimited ? "解除限制" : "限制边界" }}
    </view>  -->
  </div>
</template>
<script>
export default {
  name: 'mapcontrol',
  emits: ['change-map-type'],
  props: {
    hasChangeMapType: {
      type: Boolean,
      default: true,
    },
    hasSetCenter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isVector: false,
      isLimited: true,
    };
  },
  methods: {
    toggleMapType() {
      this.isVector = !this.isVector;
      // 发送事件到父组件
      this.$emit('change-map-type', this.isVector ? 'satellite' : 'vector');
    },
    setCenterView() {
      // 发送事件到父组件，让父组件设置地图中心
      this.$emit(
        'set-center',
        {
          lat: 34.00776409604684,
          lng: 119.1852291724374,
        },
        13.9
      );
    },
    setMapBounds() {
      // 发送事件到父组件，让父组件设置地图边界
      this.$emit(
        'set-bounds',
        { lat: 34.00776409604684, lng: 119.1852291724374 }, // 西南角坐标
        { lat: 34.01776409604684, lng: 119.1892291724374 } // 东北角坐标
      );
    },
    toggleLimitBounds() {
      this.isLimited = !this.isLimited;

      if (this.isLimited) {
        // 设置地图边界限制
        this.$emit(
          'set-limit-bounds',
          { lat: 34.00276409604684, lng: 119.1802291724374 }, // 西南角坐标
          { lat: 34.01276409604684, lng: 119.1902291724374 } // 东北角坐标
        );
      } else {
        // 移除地图边界限制
        this.$emit('remove-limit-bounds');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.map-buttons {
  position: absolute;
  top: 54px;
  right: 10px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.map-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  padding: 8px 12px;
  margin-bottom: 5px;
  border-radius: 4px;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-align: center;
  min-width: 60px;
  cursor: pointer; /* 添加鼠标指针样式 */
  user-select: none; /* 防止文本被选中 */
}

.map-button:active {
  background-color: #aa011c;
  color: white;
}
</style>
