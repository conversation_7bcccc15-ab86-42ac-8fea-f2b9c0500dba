<template>
  <view class="canvas-progress-bar" :style="{ height: componentHeight + 'px' }">
    <scroll-view
      class="scroll-container"
      scroll-x="true"
      :show-scrollbar="false"
      :enable-flex="true"
      :scroll-left="scrollLeft"
    >
      <view
        class="canvas-wrapper"
        :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
      >
        <!-- 主步骤条 canvas -->
        <canvas
          canvas-id="progressCanvas"
          ref="progressCanvas"
          :width="canvasWidth"
          :height="canvasHeight"
          :style="{
            width: canvasWidth + 'px',
            height: canvasHeight + 'px',
            display: 'block',
            position: 'absolute',
            zIndex: 1,
          }"
        ></canvas>
        <view>
          <u--image
            class="bus-wapper"
            :src="globalConfig.iconImgPrefix + 'busImage.png'"
            :style="busImageStyle"
          />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: "CanvasProgressBar",
  props: {
    steps: {
      type: Array, // 步骤数组
      default: () => [],
    },
    currentPoint: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      canvasWidth: 110, // 初始宽度，会根据步骤数量动态调整
      canvasHeight: 80, // 会根据文字高度动态调整
      minStepWidth:80, // 每个步骤的最小宽度
      stepBarHeight: 70, // 步骤条本身的高度（圆点+线条区域+60x40巴士图标空间）
      textLineHeight: 16, // 文字行高
      componentHeight: 80, // 组件总高度
      scrollLeft: 0, // 滚动位置
      globalConfig: getApp().globalData.config,
      busImageStyle: {
        display: "none",
        position: "absolute",
        left: "0",
        top: "0",
        width: "32px",
        height: "32px",
        zIndex: 3,
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        this.drawProgressBar();
      }, 100);
    });
  },
  watch: {
    steps() {
      this.$nextTick(() => {
        this.drawProgressBar();
      });
    },
    currentPoint() {
      this.$nextTick(() => {
        this.calculateScrollPosition();
      });
    },
  },
  methods: {
    drawProgressBar() {
      // 如果没有步骤数据，直接返回
      if (!this.steps || this.steps.length === 0) {
        return;
      }

      // 计算组件高度和canvas尺寸
      this.calculateDimensions();

      // 等待下一帧再绘制，确保canvas尺寸已更新
      this.$nextTick(() => {
        setTimeout(() => {
          // 统一使用uni.createCanvasContext API
          const ctx = uni.createCanvasContext("progressCanvas", this);
          ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

          this.drawContent(ctx);
        }, 50);
      });
    },

    // 绘制内容的具体逻辑
    drawContent(ctx) {
      const barY = 50; // 线条和圆点的Y位置，为上方留出更多空间
      const padding = 60; // 左右边距
      const stepCount = this.steps.length;
      const totalLength = this.canvasWidth - 2 * padding;
      const stepGap = stepCount > 1 ? totalLength / (stepCount - 1) : 0;

      // 如果有多个步骤，画连接线
      if (stepCount > 1) {
        ctx.lineWidth = 2; // 调小连接线宽度
        ctx.lineCap = "round";

        for (let i = 0; i < stepCount - 1; i++) {
          const startX = padding + i * stepGap;
          const endX = padding + (i + 1) * stepGap;

          // 根据当前步骤位置确定线段颜色
          const lineColor = this.getLineColor(i);
          ctx.strokeStyle = lineColor;

          // 画连接线段，直接连接到圆点边缘
          const circleRadius = 12; // 与步骤点的实际半径保持一致
          const lineStartX = startX + circleRadius;
          const lineEndX = endX - circleRadius;

          ctx.beginPath();
          ctx.moveTo(lineStartX, barY);
          ctx.lineTo(lineEndX, barY);
          ctx.stroke();
        }
      }

      // 画步骤点和文字
      this.steps.forEach((step, index) => {
        const x =
          stepCount === 1 ? this.canvasWidth / 2 : padding + index * stepGap;
        // console.log(`Drawing step ${index} at x=${x}, stepGap=${stepGap}`);
        this.drawStepPoint(ctx, x, barY, step.pointName, index);
      });

      // 在uni-app中，需要调用draw()方法来实际绘制到canvas上
      ctx.draw();

      // 绘制公交车到独立的canvas上
      this.drawBusCanvas();

      // 绘制完成后计算滚动位置
      this.calculateScrollPosition();
    },

    // 计算组件尺寸
    calculateDimensions() {
      const stepCount = this.steps.length;
      if (stepCount === 0) return;

      // 计算canvas宽度
      const screenWidth = uni.getSystemInfoSync().windowWidth;

      if (stepCount <= 7) {
        // 7个及以下步骤：适应屏幕宽度，不滚动
        this.canvasWidth = screenWidth;
        console.log(`步骤数量 ${stepCount} <= 7，使用屏幕宽度: ${screenWidth}px`);
      } else {
        // 超过7个步骤：使用最小宽度，允许滚动
        const minWidth = stepCount * this.minStepWidth;
        this.canvasWidth = minWidth;
        console.log(`步骤数量 ${stepCount} > 7，使用计算宽度: ${minWidth}px`);
      }

      // 调试信息
      // console.log("Canvas dimensions:", {
      //   stepCount,
      //   minWidth,
      //   screenWidth,
      //   finalWidth: this.canvasWidth,
      // });

      // 计算最长文字的字符数
      let maxTextLength = 0;
      this.steps.forEach((step) => {
        const textLength = (step.pointName || "").length;
        if (textLength > maxTextLength) {
          maxTextLength = textLength;
        }
      });

      // 计算canvas和组件高度
      // 步骤条高度 + 文字高度（字符数 * 行高）+ 上下边距
      const textHeight = maxTextLength * this.textLineHeight;
      this.canvasHeight = this.stepBarHeight + textHeight + 10; // 10px为上下边距
      this.componentHeight = this.canvasHeight;
    },

    // 计算滚动位置
    calculateScrollPosition() {
      if (!this.currentPoint || !this.steps || this.steps.length === 0) {
        this.scrollLeft = 0;
        return;
      }

      // 找到当前步骤的索引
      const currentIndex = this.steps.findIndex(
        (step) => step.pointName === this.currentPoint
      );
      if (currentIndex === -1) {
        this.scrollLeft = 0;
        return;
      }

      const stepCount = this.steps.length;

      // 如果步骤数量 <= 7，不需要滚动
      if (stepCount <= 7) {
        this.updateScrollPosition(0);
        console.log(`步骤数量 ${stepCount} <= 7，不滚动`);
        return;
      }

      const isStartPoint = currentIndex === 0;
      const isEndPoint = currentIndex === stepCount - 1;

      // 获取屏幕宽度
      const screenWidth = uni.getSystemInfoSync().windowWidth;

      // 计算滚动的最大值
      const maxScrollLeft = Math.max(0, this.canvasWidth - screenWidth);

      // 如果是起点，滚动到最左边
      if (isStartPoint) {
        this.updateScrollPosition(0);
        return;
      }

      // 如果是终点，滚动到最右边（但不超过最大值）
      if (isEndPoint) {
        this.updateScrollPosition(maxScrollLeft);
        return;
      }

      // 计算当前步骤的X位置
      const padding = 60;
      const totalLength = this.canvasWidth - 2 * padding;
      const stepGap = stepCount > 1 ? totalLength / (stepCount - 1) : 0;
      const currentStepX = padding + currentIndex * stepGap;

      // 计算滚动位置，让当前步骤居中
      const targetScrollLeft = currentStepX - screenWidth / 2;

      // 确保滚动位置在有效范围内
      const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));
      this.updateScrollPosition(finalScrollLeft);

      // console.log("滚动位置计算:", {
      //   currentIndex,
      //   currentStepX,
      //   screenWidth,
      //   targetScrollLeft,
      //   maxScrollLeft,
      //   finalScrollLeft: this.scrollLeft,
      // });
    },
    centerStep(stepName) {
      if (!stepName || !this.steps || this.steps.length === 0) {
        console.warn('无效的步骤名称或步骤数据');
        return;
      }

      // 找到指定步骤的索引
      const stepIndex = this.steps.findIndex(step => step.pointName === stepName);
      if (stepIndex === -1) {
        console.warn('未找到指定的步骤:', stepName);
        return;
      }

      // 直接计算指定步骤的滚动位置，不修改 currentPoint prop
      const stepCount = this.steps.length;

      // 如果步骤数量 <= 7，不需要滚动
      if (stepCount <= 7) {
        this.updateScrollPosition(0);
        console.log(`centerStep: 步骤数量 ${stepCount} <= 7，不滚动`);
        return;
      }

      const isStartPoint = stepIndex === 0;
      const isEndPoint = stepIndex === stepCount - 1;

      // 获取屏幕宽度
      const screenWidth = uni.getSystemInfoSync().windowWidth;

      // 计算滚动的最大值
      const maxScrollLeft = Math.max(0, this.canvasWidth - screenWidth);

      // 如果是起点，滚动到最左边
      if (isStartPoint) {
        this.updateScrollPosition(0);
        console.log('手动触发步骤居中 - 起点:', stepName, 'scrollLeft:', 0);
        return;
      }

      // 如果是终点，滚动到最右边（但不超过最大值）
      if (isEndPoint) {
        this.updateScrollPosition(maxScrollLeft);
        console.log('手动触发步骤居中 - 终点:', stepName, 'scrollLeft:', maxScrollLeft);
        return;
      }

      // 计算指定步骤的X位置
      const padding = 60;
      const totalLength = this.canvasWidth - 2 * padding;
      const stepGap = stepCount > 1 ? totalLength / (stepCount - 1) : 0;
      const stepX = padding + stepIndex * stepGap;

      // 计算滚动位置，让指定步骤居中
      const targetScrollLeft = stepX - screenWidth / 2;

      // 确保滚动位置在有效范围内
      const newScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));

      // 强制更新滚动位置
      this.updateScrollPosition(newScrollLeft);

      console.log('手动触发步骤居中:', stepName, {
        stepIndex,
        stepX,
        targetScrollLeft,
        finalScrollLeft: this.scrollLeft,
        screenWidth,
        maxScrollLeft
      });
    },

    // 强制更新滚动位置
    updateScrollPosition(newScrollLeft) {
      // 先设置为一个不同的值，然后再设置目标值，强制触发更新
      this.scrollLeft = newScrollLeft === 0 ? 1 : 0;

      this.$nextTick(() => {
        this.scrollLeft = newScrollLeft;
        console.log('强制更新滚动位置:', newScrollLeft);
      });
    },

    // 绘制公交车canvas
    drawBusCanvas() {
      const currentIndex = this.getCurrentStepIndex();

      // 如果没有当前步骤或者是最后一个步骤，隐藏公交车图片
      if (currentIndex === -1 || currentIndex >= this.steps.length - 1) {
        this.busImageStyle.display = "none";
        return;
      }

      // 计算连接线的位置（与 drawContent 保持一致）
      const barY = 50; // 与 drawContent 中的 barY 保持一致
      const padding = 60; // 与 drawContent 中的 padding 保持一致
      const stepCount = this.steps.length;
      const totalLength = this.canvasWidth - 2 * padding;
      const stepGap = stepCount > 1 ? totalLength / (stepCount - 1) : 0;

      // 计算当前步骤连接线的位置
      const startX = padding + currentIndex * stepGap;
      const endX = padding + (currentIndex + 1) * stepGap;
      const circleRadius = 12; // 与 drawContent 中的 circleRadius 保持一致
      const lineStartX = startX + circleRadius;
      const lineEndX = endX - circleRadius;

      // 计算公交车位置（实际连接线中间上方）
      const busX = (lineStartX + lineEndX) / 2;
      const busY = barY - 20;

      // 更新公交车图片的位置和样式
      this.updateBusImagePosition(busX, busY);
    },

    // 更新公交车图片位置
    updateBusImagePosition(x, y) {
      const busWidth = 24;
      const busHeight = 24;

      // 计算图片的左上角位置（居中显示）
      const left = x - busWidth / 2;
      const top = y - busHeight / 2;

      // 更新样式
      this.busImageStyle = {
        display: "block",
        position: "absolute",
        left: left + "px",
        top: top + "px",
        width: busWidth + "px",
        height: busHeight + "px",
        zIndex: 3,
      };

      // console.log("更新公交车图片位置:", {
      //   x,
      //   y,
      //   left,
      //   top,
      //   busWidth,
      //   busHeight,
      //   scrollLeft: this.scrollLeft,
      //   canvasWidth: this.canvasWidth,
      // });
    },
    // 获取当前步骤的索引
    getCurrentStepIndex() {
      if (!this.currentPoint || !this.steps) return -1;
      return this.steps.findIndex(
        (step) => step.pointName === this.currentPoint
      );
    },

    // 获取步骤点颜色
    getStepColor(stepIndex) {
      const currentIndex = this.getCurrentStepIndex();

      if (currentIndex === -1) {
        // 没有设置当前步骤，默认为绿色
        return "#26a383";
      }

      if (stepIndex === currentIndex) {
        // 当前步骤为红色
        return "#aa011c";
      } else if (stepIndex < currentIndex) {
        // 当前步骤之前的为灰色
        return "#999999";
      } else {
        // 当前步骤之后的为绿色
        return "#26a383";
      }
    },

    // 获取连接线颜色
    getLineColor(lineIndex) {
      const currentIndex = this.getCurrentStepIndex();

      if (currentIndex === -1) {
        // 没有设置当前步骤，默认为绿色
        return "#26a383";
      }

      // 连接线连接的是 lineIndex 和 lineIndex+1 两个步骤
      // 如果连接线的起点在当前步骤之前，则为灰色
      if (lineIndex < currentIndex) {
        return "#999999";
      } else {
        // 否则为绿色
        return "#26a383";
      }
    },

    // 绘制带方向的连接线
    drawDirectionalLine(ctx, startX, endX, y, startStep, endStep) {
      const arrowSize = 8; // 箭头大小

      // 根据步骤是否为当前步骤确定圆点半径
      const startRadius = startStep.pointName === this.currentPoint ? 16 : 12;
      const endRadius = endStep.pointName === this.currentPoint ? 16 : 12;

      const lineStartX = startX + startRadius; // 线条从前一个圆点边缘开始
      const lineEndX = endX - endRadius - 8; // 线条结束位置，为箭头和圆点留出空间

      // 画主线条
      ctx.strokeStyle = "#26a69a";
      ctx.lineWidth = 1; // 调小连接线宽度
      ctx.lineCap = "round";
      ctx.beginPath();
      ctx.moveTo(lineStartX, y);
      ctx.lineTo(lineEndX, y);
      ctx.stroke();

      // 画箭头
      this.drawArrowHead(ctx, lineEndX, endX - endRadius, y, arrowSize);
    },

    // 绘制箭头头部
    drawArrowHead(ctx, startX, endX, y, size) {
      ctx.save();
      ctx.fillStyle = "#26a383";
      ctx.strokeStyle = "#26a383";
      ctx.lineWidth = 2;

      // 计算箭头的三个点
      const arrowLength = endX - startX;
      const arrowTipX = endX - 2; // 箭头尖端稍微往内一点，避免与圆点重叠

      ctx.beginPath();
      // 箭头尖端
      ctx.moveTo(arrowTipX, y);
      // 箭头上边
      ctx.lineTo(arrowTipX - arrowLength, y - size / 2);
      // 箭头下边
      ctx.lineTo(arrowTipX - arrowLength, y + size / 2);
      // 回到尖端
      ctx.closePath();

      ctx.fill();
      ctx.stroke();
      ctx.restore();
    },

    // 绘制步骤点和文字
    drawStepPoint(ctx, x, y, pointName, index) {
      const stepCount = this.steps.length;
      const isStartPoint = index === 0;
      const isEndPoint = index === stepCount - 1;
      const isCurrentPoint = pointName === this.currentPoint; // 判断是否为当前步骤
      // 根据当前步骤位置确定圆点颜色
      const circleRadius = 12; // 保持原来的圆点大小
      const circleColor = this.getStepColor(index);

      // 画圆点
      ctx.save();
      ctx.beginPath();
      ctx.arc(x, y, circleRadius, 0, 2 * Math.PI);
      ctx.fillStyle = circleColor;
      ctx.fill();
      ctx.strokeStyle = "#fff";
      ctx.lineWidth = 2;
      ctx.stroke();

      // 在起点和终点的圆点中间显示"始"和"终"
      if (isStartPoint || isEndPoint) {
        ctx.fillStyle = "#fff";
        ctx.font = "bold 12px Arial";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        const centerText = isStartPoint ? "始" : "终";
        ctx.fillText(centerText, x, y);
      } else {
        // 普通步骤点显示白色方向箭头 ">"
        ctx.fillStyle = "#fff";
        ctx.font = "bold 12px Arial";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText("站", x, y);
      }

      // 画纵向文字，从圆点下方开始
      const textY = y + circleRadius + 8;
      this.drawVerticalText(
        ctx,
        pointName || `步骤${index + 1}`,
        x,
        textY,
        isCurrentPoint
      );
      ctx.restore();
    },

    // 绘制纵向文字
    drawVerticalText(ctx, text, x, startY, isCurrent = false) {
      // 当前步骤的文字样式：变黑变大
      ctx.fillStyle = isCurrent ? "#000" : "#666"; // 当前步骤文字变黑，普通步骤为灰色
      ctx.font = isCurrent ? "bold 14px Arial" : "14px Arial"; // 当前步骤文字变大变粗
      ctx.textAlign = "center";
      ctx.textBaseline = "top";

      // 将文字按字符分割，纵向绘制
      const chars = text.split("");

      chars.forEach((char, index) => {
        const y = startY + index * this.textLineHeight;
        ctx.fillText(char, x, y);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.canvas-progress-bar {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.scroll-container {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

.canvas-wrapper {
  display: inline-block;
  vertical-align: top;
  position: relative;
  /* 为绝对定位的canvas提供定位上下文 */
}

.scroll-container :deep(.uni-scroll-view) {
  height: 100%;
}
.bus-wapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  z-index: 3;

}
::v-deep .u-image{
	width: 100% !important;
  height: 100% !important;
  top: 6px;
}
</style>
