<template>
  <view class="step-container" :class="{ horizontal: direction === 'horizontal', vertical: direction === 'vertical' }" v-if="steps && steps.length > 0">
    <view v-for="(step, index) in steps" :key="index" class="step-item" >
      <!-- 节点内容 -->
      <view class="step-content" @click="handleNodeClick(step, index)">
        <!-- 状态为1时显示的图片 -->
        <image v-if="step.status === '1'" class="status-image" src="/static/images/bus/公交车.png" mode="aspectFit" />
        <!-- 自定义节点内容 -->
        <slot name="node" :step="step" :index="index">
          <view class="step-node-wrapper">
            <view class="step-node" :class="{ active: step.status === '1' }">
              <text :class="{ 'active-text': step.status === '1' }">{{ step.pointName }}</text>
            </view>
            <!-- 连接线 -->
            <view v-if="index < steps.length - 1" class="step-connector">
              <slot name="connector" :step="step" :nextStep="steps[index + 1]" :index="index">
                <image class="connector-image"
                  :src="step.status === '1' ? '/static/images/bus/箭头_切换右.png' : '/static/images/bus/箭头_切换右拷贝2.png'"
                  mode="aspectFit" />
              </slot>
            </view>
          </view>
        </slot>
      </view>
    </view>

  </view>
  <view v-else class="empty-state">
      暂无站点数据
    </view>
</template>

<script>
export default {
  name: 'StepComp',
  props: {
    steps: {
      type: Array,
      default: () => []
    },
    direction: {
      type: String,
      default: 'horizontal',
      validator: value => ['horizontal', 'vertical'].includes(value)
    },

  },
  mounted() {

  },
  watch: {
    steps: {
      handler(newVal) {
        // console.log('Steps data changed:', newVal);
      },
      deep: true
    }
  },
  methods: {
    handleNodeClick(step, index) {
      // 触发父组件事件，传递当前节点数据和索引
      this.$emit('node-click', { step, index });
    }
  }
}
</script>

<style scoped>
.step-container {
  display: flex;
  margin: 0 20px;
  font-family: Arial, sans-serif;
  flex-wrap: wrap;
  width: 92%;
  border-size: border-box;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 20px;
  height: 24%;
}

/* 水平布局 */
.step-container.horizontal {
  flex-direction: row;
  justify-content: center;
}

/* 垂直布局 */
.step-container.vertical {
  flex-direction: column;
  align-items: center;
}

.step-item {
  position: relative;
  display: flex;
  align-items: center;
  width: 22%;
}

.step-item:nth-last-child(-n+4) {
  margin-bottom: 0;
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  width: 100%;
}

.step-node-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.step-node {
  min-width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  padding: 0 4px;
  color: #999999;
}

.step-node.active {}

.active-text {
  color: #aa011c;
  font-size: 12px;
  white-space: nowrap;
  margin-top:20px;
}

.step-connector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 2px;
}

.connector-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.status-image {
  position: absolute;
  top: -19px;
  left: 26%;
  transform: translateX(-50%);
  width: 24px;
  height: 24px;
  z-index: 1;
}
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}
</style>
