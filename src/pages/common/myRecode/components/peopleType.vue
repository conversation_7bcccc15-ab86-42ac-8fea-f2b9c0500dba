<template>
	<view class="charts-box">
		<uni-section title="本月入园人员类型占比" style="padding:10px">
			<uni-card>
				<qiun-data-charts
					v-if="chartData&&chartData.series"
					type="pie"
					:animation="true"
					:opts="opts"
					:chartData="chartData" />
				<u-empty
					width="120"
					height="120"
					marginTop="20"
					v-else text="暂无数据"
					icon="/static/images/iconImg/empty.png"
					/>
			</uni-card>
		</uni-section>
	</view>

</template>

<script>
	export default {
		name: 'LeavePark',
		props: {
			chartData: {
				type: Object,
				default: () => {}
			},
		},
		data() {
			return {
				//您可以通过修改 config-ucharts.js 文件中下标为 ['area'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。

				opts: {
					rotate: false,
					rotateLock: false,
					color: ["#aa001e","#40dbad","#ffcd5d","#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					dataLabel: true,
					enableScroll: false,
					legend: {
						show: true,
						position: "bottom",
						lineHeight: 25,
						selectedMode: false
					},
					title: {
						name: "",
						fontSize: 15,
						color: "#333"
					},
					subtitle: {
						name: "",
						fontSize: 15,
						color: "#7cb5ec"
					},
					extra: {
						ring: {
							ringWidth: 30,
							activeOpacity: 0.5,
							activeRadius: 10,
							offsetAngle: 0,
							labelWidth: 15,
							border: true,
							borderWidth: 3,
							borderColor: "#FFFFFF"
						}
					},
				}
			}
		},
		methods: {
			 
		},
		onLoad() {
			// this.getServerData();
		},
		watch: {
			chartData(val) {

				// this.setOpts(val.series)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.charts-box {
		padding-bottom: 10px;
	}
</style>