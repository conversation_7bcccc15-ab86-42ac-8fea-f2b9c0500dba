<template>
	<view class="todoListPage topBgBox">
		<top-navbar bgColor="#AA001E" :title="title" :autoBack="true" leftText=""></top-navbar>
		 
		<view class="bgCard middle-container">
			<view class="setOver">
			<EnterPark :chartData="enterData"></EnterPark>
           	<LeavePark :chartData="leaveData"></LeavePark>
			<PeopleType :chartData="typeData"></PeopleType>
			</view>
		</view>

		 
	</view>
</template>

<script>
	import EnterPark from "./components/enterPark.vue"
	import LeavePark from "./components/leavePark.vue"
	import PeopleType from "./components/peopleType.vue"
	import { getThisMonthIn, getThisMonthOut, getPersonnelRatio } from "@/api/myRecode/index";
	export default {
        components:{
			EnterPark,LeavePark,PeopleType
		},
		data() {
			return {
				title:  '',
				enterData: {
					categories: [],
					series: []
				},
				leaveData: {},
				typeData: {}
			}
		},
		onLoad(option) {
			this.title = option.title;
			this.getEnterParkInfo()
			this.getLeaveParkInfo()
			this.getPeopleTypeInfo()
		},
		methods: {
			async getEnterParkInfo() {
				const res = await getThisMonthIn()
				if (res.data && res.data.length>0) {
					const axisX = res.data.map(item => item.date)
					const axisY = res.data.map(item => item.count)
					this.enterData = {
						categories: axisX,
						series: [{
							name: "入园",
							data: axisY,
						}]
					} 
				}
			},
			async getLeaveParkInfo() {
				const res = await getThisMonthOut()
				if (res.data && res.data.length>0) {
					const axisX = res.data.map(item => item.date)
					const axisY = res.data.map(item => item.count)
					this.leaveData = {
						categories: axisX,
						series: [{
							name: "出园",
							data: axisY,
						}]
					} 
				}
			},
			async getPeopleTypeInfo() {
				const res = await getPersonnelRatio()
				if (res.data && res.data.length>0) {
					this.typeData = {
						series: [{
							data: res.data.map(item => ({
								name: item.obj_name,
								value: parseFloat(item.percentage)
							}))
					    }]
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.middle-container {
		padding-top: 10px;
	}
	.setOver{
		height: calc(100vh - 200rpx);
		overflow-y: auto;
	}
</style>