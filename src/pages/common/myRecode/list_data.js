const axisX=[]
for(let i=1;i<30;i++){
	axisX.push(`11-${i}`)
}
const axisY=[]
const axisYl=[]
for(let i=1;i<30;i++){
	axisYl.push(Math.floor(Math.random() * 101))
	axisY.push(Math.floor(Math.random() * 101))
}
 
export const allData = {
	enterData: {
		categories: axisX,
		series: [{
			name: "入园",
			data: axisY,
		}]
	},
	leaveData: {
		categories: axisX,
		series: [{
			name: "出园" ,
			data: axisYl ,
		}]
	},
	
	typeData: {
		series: [{
			 
			data: [{
				name:'面试者',value:200,
			} ,{
				name:'游客',value:1000,
			} ,{
				name:'经销商',value:2400,
			} ,{
				name:'供应商',value:5200,
			} ,]
		}]
	}
}