<template>
  <view class="charts-box">
    <uni-section title="出入园报表-时间" style="padding: 10px">
      <template v-slot:right>
				<u-button size="small" type="primary" @click="exportReport"
					>导出</u-button
				>
			</template>
      <uni-card>
        <qiun-data-charts
          v-if="chartData.categories&&chartData.categories.length"
          type="line"
          :ontouch="true"
          :opts="opts"
          :chartData="chartData"
          :inScrollView="true"
        />
        <u-empty
          width="120"
          height="120"
          marginTop="20"
          v-else text="暂无数据"
          icon="/static/images/iconImg/empty.png"
        />
      </uni-card>
    </uni-section>
  </view>
</template>

<script>
export default {
  name: 'EnterPark',
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      //您可以通过修改 config-ucharts.js 文件中下标为 ['area'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ['#aa001e'],
        padding: [15, 5, 5, 0],
        enableScroll: true,
        dataLabel: true,
        legend: {
          show: false,
        },
        xAxis: {
          axisLine: true,
          fontColor: '#333',
          rotateLabel: true,
          fontSize: 12,
          scrollShow: true,
          itemCount: 8
        },
        yAxis: {
          disabled: false,
          disableGrid: false,
          gridType: 'dash',
          dashLength: 20,
          fontColor: '#333',
          fontSize: 14,
          minInterval: 1,
        },
        extra: {
          column: {
            type: "stack",
            width: 12,
            barBorderRadius: [2, 2, 0, 0],
          },
        },
      },
    };
  },
  methods: {
    setOpts(val) {
      if (!val.length) {
        this.opts.extra.column.width = 10;
      }
      if (val.length <= 10) {
        this.opts.extra.column.width = 16;
      }
      if (10 < val.length <= 31) {
        this.opts.extra.column.width = 10;
      }
    },
    exportReport() {
      // 0-表示按时间
      this.$emit("exportReport", 0);
    }
  },
  onLoad() {
    // this.getServerData();
  },
  watch: {
    chartData(val) {
      this.setOpts(val.series);
    },
  },
};
</script>

<style lang="scss" scoped>
.charts-box {
  padding-bottom: 10px;
}
</style>
