<template>
	<view class="todoListPage topBgBox">
		<top-navbar bgColor="#AA001E" title="出入园报表" :autoBack="true" leftText=""></top-navbar>
		<view class="bgCard middle-container">
			<uni-row class="flex">
        <uni-col :span="24">
          <u--form>
            <u-form-item label="选择月份" labelWidth="85">
              <u--input
                placeholder="请选择"
                v-model="queryParams.month"
                clearable
                @focus="focusInput"
              ></u--input>
            </u-form-item>
          </u--form>
        </uni-col>
        <!-- <uni-col :span="2"> </uni-col> -->
      </uni-row>
			<view class="setOver">
				<ByTime :chartData="byTimeInfo" @exportReport="exportReport"></ByTime>
				<ByPersonnel :chartData="byPersonnelInfo" @exportReport="exportReport"></ByPersonnel>
			</view>
		</view>
		<u-datetime-picker
			:show="visible"
			v-model="pickerValue"
			mode="year-month"
			@confirm="bindChange"
			@close="cancel"
			@cancel="cancel"
		></u-datetime-picker>
	</view>
</template>

<script>
	import ByTime from "./components/byTime.vue"
	import ByPersonnel from "./components/byPersonnel.vue"
	import { getReportInfo } from "@/api/outInReport/index";
	import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
	import { fileOpenDownload } from '@/utils/download';
	export default {
		components:{
			ByTime,
			ByPersonnel
		},
		data() {
			const date = new Date();
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			return {
				pickerValue: Number(new Date()),
				queryParams: {
					month: `${year}-${month > 9 ? month : '0' + month}`,
				},
				visible: false,
				enterData: {
					categories: [],
					series: []
				},
				byTimeInfo: {},
				byPersonnelInfo: {},
				typeData: {}
			}
		},
		onLoad() {
			this.init();
		},
		methods: {
			init() {
				this.getInfoAtTime();
				this.getInfoAtPersonnel();
			},
			focusInput() {
				this.visible = true;
			},
			cancel() {
				this.visible = false;
			},
			bindChange(e) {
				this.queryParams.month = formatDate(e.value, 'yyyy-MM');
				this.init();
				this.visible = false;
			},
			async getInfoAtTime() {
				try {
					this.queryParams.type = 0;
					const res = await getReportInfo(this.queryParams)
					if (res.data && res.data.length>0) {
						const axisX = res.data.map(item => item.date)
						const axisIn = res.data.map(item => item.entering)
						const axisOut = res.data.map(item => item.leaving)
						this.byTimeInfo = {
							categories: axisX,
							series: [
								{
									name: "入园",
									data: axisIn,
								},
								{
									name: "出园",
									data: axisOut,
								},
							]
						} 
					} else {
						this.byTimeInfo = {
							categories: [],
							series: []
						}
					}
				} catch (error) {
					
				}
			},
			async getInfoAtPersonnel() {
				try {
					this.queryParams.type = 1;
					const res = await getReportInfo(this.queryParams)
					if (res.data && res.data.length>0) {
						const axisX = res.data.map(item => item.userName)
						const axisIn = res.data.map(item => item.entering)
						const axisOut = res.data.map(item => item.leaving)
						this.byPersonnelInfo = {
							categories: axisX,
							series: [
								{
									name: "出园",
									data: axisOut,
								},
								{
									name: "入园",
									data: axisIn,
								}
							]
						} 
					} else {
						this.byPersonnelInfo = {
							categories: [],
							series: []
						}
					}
				} catch (error) {
					
				}
			},
			/*数据导出*/
			async exportReport(type) {
				let url = '/appointment/record/exportReport';
				fileOpenDownload(`${url}?month=${this.queryParams.month}&type=${type}`);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.middle-container {
		padding-top: 10px;
	}
	.setOver{
		height: calc(100vh - 200rpx);
		overflow-y: auto;
	}
</style>