<template>
  <view class="busStepPage">
    <u-steps :current="busCurrent" :direction="direction" class="busStep">
      <u-steps-item :title="item.pointName" v-for="(item,index) in busTaskInfo" :key="index">
        <template slot="desc" v-if="taskStatus==2">
          <view class="descBox">
            <view class="busCurrent" v-if="item.currentStation">
              <u-icon class="busIcon" size="20" :name="globalConfig.iconImgPrefix + 'bus.svg'"></u-icon>
              <!-- <u-icon class="arrowIcon" size="20" :name="globalConfig.iconImgPrefix + 'longArrow.svg'"></u-icon> -->
            </view>
            <view>{{ item.timeTxt }}</view>
            <view>{{ item.tiemInfo }}<text>{{ item.timeTxt=='预计到达时间：'?'分钟':'' }}</text></view>
          </view>
          
        </template>
        <template slot="icon" v-if="taskStatus==2">
          <u-icon v-if="item.status == 0" name="close-circle"></u-icon>
          <u-icon v-else-if="item.status == 1" name="checkmark-circle" color="rgb(170, 0, 30)"></u-icon>
        </template>
      </u-steps-item>
    </u-steps>
  </view>
  
</template>

<script>
  export default {
    props: {
      direction: {
        default: 'column',
        type: [String],
      },
      taskStatus: {
        default: '',
        type: [String],
      },
    },
    data() {
      return {
        globalConfig: getApp().globalData?.config,
        busTaskInfo:[],
        busCurrent:0
      };
    },
    methods: {
      init(data) {
        if (this.taskStatus != '2') {
          this.busCurrent = null
        }
        if (data.busStations && data.busStations.length > 0 && this.taskStatus == '2') {
          // 创建副本并初始化currentStation字段
          const stations = data.busStations.map(item => ({
            ...item,
            currentStation: false,
            tiemInfo: '' // 初始化时间信息
          }))

          // 处理时间显示
          stations.forEach(item => {
            if (item.arrivalTime) {
              item.timeTxt = '到达时间：'
              item.tiemInfo = item.arrivalTime
            } else if (item.expectedArrivalTime) {
              item.timeTxt = '预计到达时间：'
              item.tiemInfo = item.expectedArrivalTime
            }
          })

          // 获取所有到达时间为空的站点
          const arrivalStations = stations.filter(item => item.arrivalTime)
          
          if (arrivalStations.length === 0) {
            // 寻找最小预计到达时间的站点（数字比较）
            const validStations = stations.filter(item => item.expectedArrivalTime)
            if (validStations.length > 0) {
              const minStation = validStations.reduce((prev, current) => 
                prev.expectedArrivalTime < current.expectedArrivalTime ? prev : current
              )
              const isFirstItem = stations.indexOf(minStation) === 0
              if (!isFirstItem) {
                minStation.currentStation = true
              }
            }
          } else {
            // 获取最后一个到达站点的索引
            const lastArrivalIndex = stations.map((item, index) => 
              item.arrivalTime ? index : -1
            ).filter(i => i !== -1).pop()
            
            // 排除最后一项且设置当前站点
            if (typeof lastArrivalIndex === 'number' && lastArrivalIndex < stations.length - 1) {
              stations[lastArrivalIndex].currentStation = true
            }
          }

          this.busTaskInfo = stations
        } else {
          this.busTaskInfo = data.busStations
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.busStepPage{
  display: flex;
  align-items: center;
  justify-content: center;
  .busStep {
    
    align-items: center;
    .u-steps-item--column{
      padding-bottom: 40rpx;
      width: 100%;
      
    }
    
    .u-steps-item__content{
      position: relative;
      .descBox{
        margin-top: 10rpx;
        display: flex;
        align-content: center;
        justify-content: flex-start;
        
        .busCurrent{
          position: absolute;
          transform:rotate(-90deg);
          top: 78rpx;
          left: -12rpx;
          .arrowIcon{
            margin-top: -20rpx;
          }
        }
      }
    }
  }
}
  
</style>