<template>
  <view class="parkBusPage topBgBox">
    <top-navbar bgColor="#AA001E" title="园区公交" :autoBack="autoBack" leftText=""></top-navbar>
    <view class="bgCard middle-container">
      <view style="margin-top: 10px">
        <u-search :showAction="true" placeholder="选择路线" clearable v-model="keyword" actionText="选择" :animation="false"
          disabled @search="serachOperate" @custom="serachOperate" @clear="serachOperate"></u-search>
        <!-- <div v-if="JSON.stringify(currentRouteInfo) !== '{}'">
          <uni-card>
            <view class="flex content-row">
              <u--text :text="currentRouteInfo.startPoint" size="17"></u--text>
              <u-icon name="arrow-rightward" color="#AA001E" size="30"></u-icon>
              <u--text
                class="flex justify-end"
                align="right"
                size="17"
                :text="currentRouteInfo.endPoint"
              ></u--text>
            </view>
            <view class="flex content-row">
              <u--text
                v-if="currentRouteInfo.currentLocation"
                type="info"
                class="flex justify-start"
                size="16"
                :text="`当前位置：${currentRouteInfo.currentLocation}`"
              ></u--text>
              <u--text
                v-else
                type="info"
                class="flex justify-start"
                size="16"
                :text="`当前位置：未知`"
              ></u--text>
            </view>
            <view class="content-row">
              <u--text
                size="14"
                :text="`发车时间：${this.currentRouteInfo.departureTimesStr}`"
              ></u--text>
            </view>
          </uni-card>
          <uni-card v-if="processList.length">
            <u-steps
              direction="column"
              :current="processList.length"
              activeIcon="checkmark-circle-fill"
              activeColor="#AA001E"
              inactiveColor="#bbbbbb"
              inactiveIcon="minus-circle-fill"
            >
              <u-steps-item
                v-for="(item, index) in processList"
                :title="item"
                :key="index"
                iconSize="20"
              ></u-steps-item>
            </u-steps>
          </uni-card>
          <uni-card>
    
          </uni-card>
        </div> -->
        <!-- <div v-for="(item, index) in appListArray" :key="index">
          {{ item.taskName }}
        </div> -->
        <card-list v-if="appListArray.length" :cardList="appListArray" class="busCardList">
          <template #default="slotProps">
            <item :itemData="slotProps.itemData" @handleDetail="cardItemClick(slotProps.itemData)" />
          </template>
        </card-list>
        <u-empty width="120" height="120" marginTop="20" v-else mode="list" icon="/static/images/iconImg/empty.png" />
      </view>
    </view>
    <u-action-sheet :show="pickerShow" :actions="pickerColumns" cancelText="取消" @select="pickerConfirm"
      @close="pickerCancel">
    </u-action-sheet>
  </view>
</template>

<script>
import { queryWorkLinesRequest, getAppList, getBusScheduling } from "@/api/parkBus/index";
import CardList from "@/pages/components/cardList.vue";
import Item from './item.vue';
export default {
  components: {
    Item,
    CardList
  },
  name: "ParkBus",
  data() {
    return {
      autoBack: true,
      keyword: "",
      pickerShow: false,
      currentRouteId: "",
      currentRouteInfo: {},
      pickerColumns: [],
      processList: [],
      sourcesData: [],
      appListArray: [],
    };
  },
  onLoad() {
    this.init();
  },
  methods: {
    // 上滑刷新
    scrolltolower() {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        return this.$refs.uToast.show(
          this.$setToastMsg({
            message: "已加载全部",
            type: "success",
          })
        );
      }
      this.getTableData();
    },
    init() {
      this.getBusRouteInfo();
    },
    serachOperate(val) {
      this.pickerShow = true;
    },
    async pickerConfirm(val) {
      try {
        console.log("-val-", val);
        this.currentRouteId = val.routeId;
        const res = await getBusScheduling(this.currentRouteId);
        this.appListArray = res.data;
        this.keyword = val.routeName;
        // this.getBusSchedulingInfo();
        // this.getAppListRequest(val);
        this.pickerShow = false;
      } catch (err) { }
    },
    pickerCancel() {
      this.pickerShow = false;
    },
    cardItemClick(data) {
      uni.navigateTo({
        url: `/pages/common/map/index`,
      });
      //this.$route.query
    },
    async getBusRouteInfo() {
      try {
        const res = await queryWorkLinesRequest();
        res.data.forEach((item) => {
          if (item)
            this.sourcesData.push(item);
        });
        console.log("-sourcesData-", this.sourcesData);
        this.sourcesData.forEach((item) => {
          item.id = item.routeId;
          item.name = item.routeName;
        });
        this.pickerColumns = this.sourcesData;
        this.pickerConfirm(this.pickerColumns[0]);
      } catch (error) { }
    },
    async getBusSchedulingInfo() {
      try {
        this.currentRouteInfo = {};
        this.processList = [];
        const res = []; // await getBusScheduling(this.currentRouteId);
        this.currentRouteInfo = res.data;
        this.currentRouteInfo.departureTimesStr =
          this.currentRouteInfo.departureTimes.join(", ");
        this.processList = this.currentRouteInfo.stationList;
      } catch (error) { }
    },
    async getAppListRequest(val) {
      try {
        const params = {
          routeId: val.routeId,
          pageSize: 10,
          pageNum: 1,
        };
        const res = await getAppList(params);
        console.log("--res--", res);
        this.appListArray = res.data.rows;
      } catch (error) { }
    },
  },
};
</script>
<style lang="scss" scoped>
.parkBusPage {
  .bgCard {
    padding-bottom: 0;
  }

  .busCardList {
    height: calc(100vh - 196rpx) !important;
    padding: 20rpx 20rpx 0;
    overflow: hidden;
  }
}

.middle-container {
  padding-top: 0;
  overflow: hidden;
  overflow-y: scroll;
}

.content-row {
  margin-top: 15px;
}

.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
}
</style>
