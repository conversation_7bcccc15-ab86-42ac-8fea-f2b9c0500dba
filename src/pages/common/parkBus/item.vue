<template>
  <uni-card :title="cardTitle" @click="handleDetail">
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-16 text_scrolling">
          <text>{{ itemData.taskName }}</text>
        </view>
        <u-tag
          size="mini"
          :text="queryStatus(itemData.taskStatus).text"
          :color="queryStatus(itemData.taskStatus).color"
          :bgColor="queryStatus(itemData.taskStatus).bgColor"
          :borderColor="queryStatus(itemData.taskStatus).color"
        ></u-tag>
      </view>
      <view class="mt4">
        <u-line></u-line>
      </view>
    </template>

    <uni-row slot="actions">
      <view
        class="flex mb5 align_center"
        v-for="(item, index) in queryItems(itemData.taskStatus)"
        :key="index"
      >
        <span class="mr4 item_name">{{ item.name }}:</span>
        <span>{{ itemData[item.prop] || '暂无' }}</span>
      </view>
    </uni-row>
  </uni-card>
</template>

<script>
import { getStatus, getItems } from '../list_config';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: '',
      type: String,
    },
  },
  data() {
    return {};
  },
  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
    queryStatus(val) {
      return getStatus(val);
    },
    queryItems(val) {
      return getItems(val,'parkBus');
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}
.uni-card {
  margin: 5px 0 !important;
  padding: 10px 10px !important;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
.item_name {
  color: #666;
}

.text_scrolling {
  white-space: nowrap; /* 保持文字在一行内显示 */
  overflow-x: auto; /* 隐藏溢出的内容 */
  // text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 75%; /* 设置容器宽度 */
}
</style>
