<template>
	<view class="todoListPage topBgBox">
		<top-navbar bgColor="#AA001E" title="通行详情" :autoBack="autoBack" leftText="">
		</top-navbar>
	<view class="bgCard middle-container">
		<view class="setOver">
			<uni-section type="line" title="预约信息" class="pb1 mb5">
				<uni-card>
					<uni-forms :modelValue="tableData" label-width="80">
						<uni-forms-item  v-for="(item, index) in columnOrderList" :label="item.label"
							:key="index" >
							<text>{{tableData[item.key] }}</text>
						</uni-forms-item>
						<uni-forms-item v-if="!!tableData.followVisitor" label="随访人员：" >
							<uni-list>
								<uni-list-item v-for="(item, index) in tableData.followVisitor" :key="index"
									:title="`${item.name}`" :note="`${item.phoneNumber}`">
								</uni-list-item>
							</uni-list>
						</uni-forms-item>
					</uni-forms>
				</uni-card>
			</uni-section>
			<uni-section type="line" title="审核信息" class="pb1 mb5">
				<uni-card>
					<uni-forms :modelValue="tableData" label-width="85">
						<uni-forms-item v-for="(item, index) in columnReviewList" :label="item.label"
							:key="index" >
							<text>{{tableData[item.key]}} </text>
						</uni-forms-item>
						<uni-forms-item label="审批结果：" >
							<u--text v-if="tableData.isChecked==='-1'" class="center-text" type="error" text="审核拒绝"></u--text>
							<u--text v-else-if="tableData.isChecked==='0'" class="center-text" type="warning" text="未审核"></u--text>
							<u--text v-else-if="tableData.isChecked==='1'" class="center-text" type="success" text="审核通过"></u--text>
						</uni-forms-item>
					</uni-forms>
				</uni-card>
			</uni-section>
			<uni-section type="line" v-if="tableData.passQrCode" title="通行证信息" class="pb1 mb5">
				<uni-card>
					<u--image :showLoading="true" :src="`data:image/png;base64,${tableData.passQrCode}`" width="160px" height="160px"></u--image>
					<u--text v-if="tableData.isChecked==='1'" class="center-text" type="error" :text="tableData.appointmentCode"></u--text>
				</uni-card>
			</uni-section>
			<uni-section type="line" v-if="tableData.transitCheckInInfo && tableData.transitCheckInInfo.length > 0" title="进场核验信息" class="pb1 mb5">
				<uni-card>
					<uni-forms :modelValue="tableData" label-width="85">
						<uni-forms-item label="入厂时间：" >
							{{ tableData.actuallyVisitTime }}
						</uni-forms-item>
						<view v-for="item in tableData.transitCheckInInfo" :key="item.infoId">
							<uni-forms-item v-if="item.fieldType =='radio' || item.fieldType =='input'"  :label="item.fieldName + '：'" >
								{{ item.fieldValue }}
							</uni-forms-item>
							<view v-else-if="(item.fieldType =='image'|| item.fieldType =='upload') && changeArray(item.fieldValue)" >
								<u--image v-for="(src, index) in changeArray(item.fieldValue)" width="160px" height="160px" :key="index"></u--image>
							</view>
							<uni-forms-item v-else  :label="item.fieldName + '：'"> 暂无 </uni-forms-item>
						</view>
					</uni-forms>
				</uni-card>
			</uni-section>
			<uni-section type="line" v-if="tableData.transitCheckOutInfo && tableData.transitCheckOutInfo.length > 0" title="出场核验信息" class="pb1 mb5">
				<uni-card>
					<uni-forms :modelValue="tableData" label-width="85">
						<uni-forms-item label="出厂时间：" >
							{{ tableData.actuallyLeaveTime }}
						</uni-forms-item>
						<view v-for="item in tableData.transitCheckOutInfo" :key="item.infoId">
							<uni-forms-item v-if="item.fieldType =='radio' || item.fieldType =='input'"  :label="item.fieldName + '：'" >
								{{ item.fieldValue }}
							</uni-forms-item>
							<view v-else-if="(item.fieldType =='image'|| item.fieldType =='upload') && changeArray(item.fieldValue)[0]!=null" >
								<u--image v-for="(src, index) in changeArray(item.fieldValue)" width="160px" height="160px" :key="index"></u--image>
							</view>
							<uni-forms-item v-else  :label="item.fieldName + '：'"> 暂无 </uni-forms-item>
						</view>
					</uni-forms>
				</uni-card>
			</uni-section>
		</view>
	</view>
	</view>
</template>

<script>
import { searchPassDetails} from '@/api/passRecode/index.js';
import { orderProp, reviewProp } from './config.js'
export default {
		data() {
			return {
				autoBack: true,
				appointmentId: '',
				type: '',
				columnList: {},
				columnReviewList: {},
				tableData:{}
			}
		},
		onLoad(query){
			this.appointmentId = query.id
			this.type = query.type
			this.init()
		},
		methods: {
			async init() {
			    this.columnOrderList = orderProp
			    this.columnReviewList = reviewProp
				try{
					let queryData = {
						appointmentId: this.appointmentId,
						checkType: parseInt(this.type)
					}
					const res = await searchPassDetails(queryData)
					this.tableData = res.data
					console.log(this.tableData,"opop")
				}catch(e){
					console.log(e)
				}
			},
			changeArray(val) {
				 
			    return JSON.parse(val)
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .uni-forms-item__content {
		align-self: center;
	}
	::v-deep .uni-forms-item {
		margin-bottom: 12px;
	}

	.setOver {
		height: calc(100vh - 200rpx);
		overflow-y: auto;
	}

	.map {
		width:100%;
		height: 400rpx;
	}

	.middle-container {
		padding-top: 10px;
	}

	.pb1 {
		padding-bottom: 1px;
	}
</style>