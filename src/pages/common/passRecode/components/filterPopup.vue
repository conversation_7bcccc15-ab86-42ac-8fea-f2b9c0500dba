<template>
	<view class="">
		<u-popup :show="popupShow" @close="close" @open="open">
			<u--form labelPosition="left" ref="uForm" :model="filterForm" labelWidth="20vw" style="padding: 0.625rem">
				<u-form-item label="入园核验人" prop="checkInUser">
					<u--input v-model="filterForm.checkInUser" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="被访人姓名" prop="intervieweeName">
					<u--input v-model="filterForm.intervieweeName" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="被访人手机号" prop="intervieweePhone">
					<u--input v-model="filterForm.intervieweePhone" placeholder="请输入" type="number" maxlength="11" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="访客姓名" prop="visitorName">
					<u--input v-model="filterForm.visitorName" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="访客手机号" prop="visitorPhone">
					<u--input v-model="filterForm.visitorPhone" placeholder="请输入" type="number" maxlength="11" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="开始时间" prop="actuallyStartTime" @click="selectStartTime">
					<u--input v-model="filterForm.actuallyStartTime" placeholder="请选择" style="border: 2rpx #dadbde solid; margin-top: 20rpx" disabled />
				</u-form-item>
				<u-form-item label="结束时间" prop="actuallyEndTime" @click="selectEndTime">
					<u--input v-model="filterForm.actuallyEndTime" placeholder="请选择" style="border: 2rpx #dadbde solid; margin-top: 20rpx" disabled/>
				</u-form-item>
				<u-form-item>
					<u-button text="重置" :loading="loading" loadingText="请稍后" @click="reset" />
					<u-button type="primary" text="确定" :loading="loading" loadingText="请稍后" @click="submitHandle" />
				</u-form-item>
			</u--form>
		</u-popup>
		<u-datetime-picker
			mode="datetime"
			:show="showStartTime"
			v-model="startTimeVal"
			@close="showStartTime = false"
			@cancel="showStartTime = false"
			@confirm="startTimeConfirm"
		>
		</u-datetime-picker>
		<u-datetime-picker
			mode="datetime"
			v-model="endTimeVal"
			:show="showEndTime"
			@close="showEndTime = false"
			@cancel="showEndTime = false"
			@confirm="endTimeConfirm"
		>
		</u-datetime-picker>
	</view>
</template>

<script>
import {
		formatDate
	} from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
	props: {
		
	},
	data() {
		return {
			popupShow: false,
			loading: false,
			showStartTime: false,
			showEndTime: false,
			startTimeVal: Number(new Date()),
			endTimeVal: Number(new Date()),
			filterForm: {
				actuallyStartTime: '',
				actuallyEndTime: '',
				visitorName: '',
				visitorPhone: '',
				intervieweeName: '',
				intervieweePhone: '',
				checkInUser: '',
			}
		};
	},
	methods: {
		submitHandle() {
			this.$emit('filterSearch', this.filterForm);
			this.popupShow = false;
		},
		selectStartTime() {
			this.showStartTime = true;
		},
		selectEndTime() {
			this.showEndTime = true;
		},
		startTimeConfirm(e) {
			this.filterForm.actuallyStartTime = formatDate(e.value, 'yyyy-MM-dd hh:mm:ss');
			this.showStartTime = false;
		},
		endTimeConfirm(e) {
			this.filterForm.actuallyEndTime = formatDate(e.value, 'yyyy-MM-dd hh:mm:ss');
			this.showEndTime = false;
		},
		async open() {
			this.popupShow = true;
		},
		close() {
			this.reset()
			this.popupShow = false;
		},
		reset() {
			this.filterForm = {
				actuallyStartTime: '',
				actuallyEndTime: '',
				visitorName: '',
				visitorPhone: '',
				intervieweeName: '',
				intervieweePhone: '',
				checkInUser: '',
			}
		},
	}
};
</script>

<style lang="scss" scoped>
::v-deep .handleWay_container {
	width: 100vw;
	.u-tag {
		display: inline-block;
		text-align: center;

		.u-tag__text {
			font-size: 0.875rem;
		}
	}
}
</style>
