<template>
  <uni-card :title="cardTitle" @click="handleDetail">
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-13">{{ itemData.visitReason }}</view>
        <u-tag
          plain
          plainFill
          :text="itemData.arriveStatus"
          :color="queryStatus(itemData.arriveStatus).color"
          :bgColor="queryStatus(itemData.arriveStatus).bgColor"
          :borderColor="queryStatus(itemData.arriveStatus).color"
        ></u-tag>
      </view>
      <view class="mt4">
        <u-line></u-line>
      </view>
    </template>
    <uni-row slot="actions">
      <view
        class="flex mb5 align_center"
        v-for="(item, index) in queryItems(itemData.arriveStatus)"
        :key="index"
      >
        <span class="mr4">{{ item.name }}:</span>
        <span>{{ itemData[item.prop] }}</span>
      </view>
    </uni-row>
  </uni-card>
</template>

<script>
import { stausPass, formPassItems } from './config.js';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: '',
      type: String,
    },
  },
  data() {
    return {};
  },
  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
    queryStatus(val) {
      return stausPass.find((item) => val == item.text);
    },
    queryItems(val) {
      let statusNum;
      statusNum = val == '已到达' ? 1 : 2;
      return this.getPassItems(statusNum);
    },
    getPassItems(key) {
      return formPassItems.filter((item) => item.status.includes(key));
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}
.uni-card {
  margin: 5px 0 !important;
  padding: 10px 10px !important;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
</style>
