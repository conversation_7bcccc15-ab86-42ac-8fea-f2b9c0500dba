<template>
  <view class="toWorkListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="通行记录"
      :autoBack="autoBack"
      :hasFilter="true"
      leftText=""
      @filterHandle="filterHandle"
    >
    </top-navbar>
    <view class="">
      <u-tabs
        :list="statusRadios"
        :current="currentIndex"
        @change="changeCurrent"
      >
      </u-tabs>
      <card-list
        class="card-list"
        v-if="tableData.length"
        :cardList="tableData"
        @scrolltolower="scrolltolower"
      >
        <template #default="slotProps">
          <item
            :itemData="slotProps.itemData"
            @handleDetail="cardItemClick(slotProps.itemData)"
          />
        </template>
      </card-list>
      <u-empty
        width="120"
        height="120"
        marginTop="20"
        v-else
        mode="list"
        icon="/static/images/iconImg/empty.png"
      />
    </view>
    <filter-popup ref="filterRef" @filterSearch="filterSearch" />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import TopNavbar from '@/components/top-navbar/top-navbar.vue';
import FilterPopup from './components/filterPopup.vue';
import item from './components/item.vue';
import cardList from '@/pages/components/cardList.vue';
import { getPass } from '@/api/passRecode/index.js';
export default {
  components: {
    TopNavbar,
    item,
    cardList,
    FilterPopup,
  },
  data() {
    return {
      autoBack: true,
      currentIndex: 0,
      keyIndex: 1,
      queryData: {
        arriveStatus: '',
        // pathSource: '1',
        visitorName: '',
        visitorPhone: '',
        intervieweeName: '',
        intervieweePhone: '',
        actuallyStartTime: '',
        actuallyEndTime: '',
        appointmentStartTime: '',
        appointmentEndTime: '',
        checkInUser: '',
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      statusRadios: [
        {
          name: '已到达',
          keyName: '1',
        },
        {
          name: '已离厂',
          keyName: '2',
        },
      ],
      totalPage: 0,
    };
  },
  onLoad() {
    this.changeCurrent({ keyName: 1 });
  },
  methods: {
    async init() {
      try {
        this.queryData.arriveStatus = this.keyIndex;
        const res = await getPass(this.queryData);
        let taskData = {
          list: [],
          total: 0,
        };
        taskData.list = res.rows;
        taskData.total = res.total;
        this.totalPage = Math.ceil(taskData.total / this.queryData.pageSize);
        if (taskData.list.length) this.tableData.push(...taskData.list);
      } catch (err) {
        console.log(err);
      }
    },
    filterHandle() {
      this.$refs.filterRef.open();
    },
    filterSearch(info) {
      this.queryData = { ...this.queryData, ...info };
      this.tableData = [];
      this.init();
    },
    cardItemClick(data) {
      uni.navigateTo({
        url: `/pages/common/passRecode/components/details?id=${data.appointmentId}&type=${this.keyIndex}`,
      });
    },
    changeCurrent(val) {
      this.tableData = [];
      this.keyIndex = val.keyName;
      this.init();
    },
    searchOperate() {},
    scrolltolower() {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        return this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        // this.$modal.msgError('已加载全部');
      }
      this.getNoticeListInfo();
    },
  },
};
</script>

<style lang="scss" scoped></style>
