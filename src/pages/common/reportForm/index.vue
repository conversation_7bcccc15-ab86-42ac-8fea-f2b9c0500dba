<template>
  <view class="toWorkListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="true"
      leftText=""
    ></top-navbar>
    <!-- <card-list class="card-list" v-if="tableData.length" :cardList="tableData" @scrolltolower="scrolltolower">
		   	<template #default="slotProps"> -->
    <view class="bgCard middle-container">
      <uni-row class="flex">
        <uni-col :span="24">
          <uni-forms>
            <uni-forms-item label="选择月份" label-width="85">
              <u--input
                placeholder="请选择"
                v-model="month"
                clearable
                readonly
                shape="circle"
                class="shadow"
                @focus="focusInput"
              ></u--input>
            </uni-forms-item>
          </uni-forms>
        </uni-col>

        <!-- <u-button size="small" type="primary" @click="exportReport"
            >导出</u-button
          > -->
      </uni-row>
      <view class="table-container">
        <uni-table
          ref="table"
          :loading="loading"
          stripe
          emptyText="暂无更多数据"
        >
          <uni-tr>
            <uni-th width="20" align="center">日期</uni-th>
            <uni-th width="35" align="center">已完成任务数</uni-th>
            <uni-th width="35" align="center">未完成任务数</uni-th>
            <uni-th width="10" align="center">告警数</uni-th>
          </uni-tr>
          <uni-tr v-for="(item, index) in tableData" :key="index">
            <uni-td align="center">{{ item.taskTime }}</uni-td>
            <uni-td align="center">
              {{ item.completedCount }}
            </uni-td>
            <uni-td align="center">{{ item.uncompletedCount }}</uni-td>
            <uni-td align="center">
              {{ item.alarmCount }}
            </uni-td>
          </uni-tr>
        </uni-table>
      </view>

      <u-action-sheet
        :actions="months"
        :show="visible"
        cancelText="取消"
        @select="bindChange"
        @close="cancel"
      ></u-action-sheet>
    </view>
  </view>
</template>

<script>
import { getReportList } from '@/api/reportExcel';
import { downloadFile, fileOpenDownload } from '@/utils/download';
export default {
  data() {
    const months = [];
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    for (let i = 1; i <= 12; i++) {
      let info = i + '月';
      months.push({
        name: info,
        index: i,
        fullName: `${year}-${i > 9 ? i : '0' + i}`,
      });
    }
    return {
      queryParams: {
        month: `${year}-${month > 9 ? month : '0' + month}`,
      },
      visible: false,
      months,
      month: month + '月',
      initmonth: month + '月',
      initvalue: `${year}-${month > 9 ? month : '0' + month}`,
      loading: false,
      tableData: [],
    };
  },
  onLoad(option) {
    console.log(option);
    this.title = option.title;
    this.init();
  },
  methods: {
    async init() {
      try {
        const res = await getReportList(this.queryParams);
        this.tableData = res.data || [];
      } catch (err) {}
    },
    /*数据导出*/
    async exportReport() {
      const config = {
        url: '/task/report/export',
        params: {
          month: this.queryParams.month,
        },
      };
      fileOpenDownload(`/task/report/export?month=${this.queryParams.month}`);
      //downloadFile(config);
    },
    queryItems(val) {
      return this.formItem.filter((item) => item.status.includes(val));
    },
    focusInput() {
      this.visible = true;
    },
    cancel() {
      // this.month = rhis.initmonth;
      // this.queryParams.month = this.initvalue;
      this.visible = false;
    },
    bindChange(e) {
      this.month = e.name;
      this.queryParams.month = e.fullName;
      this.init();
      this.visible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.middle-container {
  padding: 24rpx 24rpx 0;
}
.picker-view {
  width: 750rpx;
  height: 600rpx;
  margin-top: 20rpx;
}
.item {
  line-height: 100rpx;
  text-align: center;
}
.mark {
  background-color: #ccc;
}

.table-container {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 260rpx); // 减去顶部导航和表单的高度
}
.uni-table-th {
  background-color: rgba(170, 1, 28, 0.1);
}
::v-deep .table--stripe .uni-table-tr:nth-child(2n + 3) {
  background-color: rgba(170, 1, 28, 0.03);
}
.shadow {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
</style>
