<template>
  <view class="cardbox">
      <view class="car-number-box">
        <u--text size="30rpx" color="#FEFEFE" align="center" :text="itemData.dispatchCar.carNumber" />
      </view>
      <view class="text-box">
        <u--text size="24rpx" color="#666666" :text="`绑定：${itemData.bindTime}`" />
        <u--text size="24rpx" color="#666666" :text="`解绑：${itemData.unbindTime}`" />
      </view>
  </view>
</template>

<script>
export default {
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
    };
  },

  computed: {
  },

  onLoad(option) {
  },
  onReady() {},
  methods: {
  },
};
</script>

<style lang="scss" scoped>
.cardbox {
  display: flex;
  justify-content: space-between;
  height: 145rpx;
  background: #FEFEFE;
  box-shadow: 0rpx 4rpx 43rpx 0rpx rgba(0,0,0,0.1);
  border-radius: 20rpx;
  margin: 12px 12px 0;
}
.car-number-box {
  display: flex;
  width: 248rpx;
  height: 69rpx;
  background: #AA011C;
  border-bottom-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
}
.text-box {
  margin:30rpx 56rpx 30rpx 0; 
  display: flex;
  flex-direction: column;
}
</style>

