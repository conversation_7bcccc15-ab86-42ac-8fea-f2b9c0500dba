<template>
  <view>
    <u-row customStyle="margin-bottom: 10px" align="start">
      <u-col span="1">
        <u--image
          :src="globalConfig.iconImgPrefix + 'startpoint.png'"
          width="23px"
          height="23px"
          mode="aspectFit"
        ></u--image>
      </u-col>
      <u-col span="5">
        <u-text size="12" :lines="1" class="font-12" :text="itemData.startPointName" />
      </u-col>
      <u-col span="6">
        <u-row v-if="(itemData.status == 3 || itemData.status == 4)&&itemData.actuallyStartTime">
          <u-text size="11" color="#333333" :lines="1" class="font-12" :text="itemData.actuallyStartTime" />
          <u-tag
            text="开始"
            :color="queryStatus(itemData.status).color"
            :bgColor="queryStatus(itemData.status).bgColor"
            :borderColor="queryStatus(itemData.status).color"
            plain
            plainFill
            size="mini"
          ></u-tag>
        </u-row>
        <u-row v-else-if="itemData.status == 2">
          <u-tag
            :text="`今天${getTimenoDate(itemData.actuallyStartTime)}  开始`"
            :color="queryStatus(itemData.status).color"
            :bgColor="queryStatus(itemData.status).bgColor"
            :borderColor="queryStatus(itemData.status).color"
            plain
            plainFill
            size="mini"
          ></u-tag>
        </u-row>
        <u-row v-else-if="itemData.status == 1">
          <u-tag
            text="司机暂未开始作业"
            :color="queryStatus(itemData.status).color"
            :bgColor="queryStatus(itemData.status).bgColor"
            :borderColor="queryStatus(itemData.status).color"
            plain
            plainFill
            size="mini"
          ></u-tag>
        </u-row>
      </u-col>
    </u-row>
    <u-row customStyle="margin-bottom: 10px">
      <u--image
        v-if="itemData.status == 3 || itemData.status == 4"
        class="rotate-image"
        :src="globalConfig.iconImgPrefix + 'rightarrow.png'"
        width="21px"
        height="6px"
        mode="aspectFit"
      ></u--image>
    </u-row>
    <u-row customStyle="margin-bottom: 10px">
      <u-col span="1">
        <u--image
          :src="globalConfig.iconImgPrefix + 'endpoint.png'"
          width="23px"
          height="23px"
          mode="aspectFit"
        ></u--image>
      </u-col>
      <u-col span="5">
        <u-text size="12" :lines="1" class="font-12" :text="itemData.endPointName" />
      </u-col>
      <u-col span="6" v-if="itemData.actuallyEndTime">
        <u-row>
          <u-text size="11" color="#333333" :lines="1" class="font-12" :text="itemData.actuallyEndTime" />
          <u-tag
            text="到达"
            :color="queryStatus(itemData.status).color"
            :bgColor="queryStatus(itemData.status).bgColor"
            :borderColor="queryStatus(itemData.status).color"
            plain
            plainFill
            size="mini"
          ></u-tag>
        </u-row>
      </u-col>
    </u-row>

  </view>
</template>

<script>
import { getStatus } from '../../list_config';
export default {
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },
    getTimenoDate() {
      return (value) => {
        let timeList = value.split(" ");
        let timenoDate = timeList[1];
        return timenoDate;
      }
    }
  },

  onLoad(option) {
  },
  onReady() {},
  methods: {
    queryStatus(val) {
      return getStatus(val || 0);
    },
    
  },
};
</script>

<style lang="scss" scoped>
  .employee-sty {
    flex-direction: column;
  }
  .u-line {
    margin-right: 15px !important;
  }
  .rotate-image {
    margin: 6px 0;
    transform: rotate(90deg);
    transform-origin: center;
  }
</style>

