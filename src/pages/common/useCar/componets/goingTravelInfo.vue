<template>
  <view class="flex all-in">
    <view>
      <uni-card :is-shadow="true" :border="false" style="background: #F7F6F6;">
        <travel-info
          :itemData="itemData"
        />
      </uni-card>
      <view class="drive-info">
        <drive-info
          :itemData="itemData"
        />
      </view>
      <uni-card :is-shadow="true" :border="false" style="background: #F7F6F6;">
        <similar-task-info
          :itemData="itemData"
        />
      </uni-card>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import DriveInfo from '@/pages/common/useCar/componets/driveInfo.vue';
import TravelInfo from '@/pages/common/useCar/componets/travelInfo.vue';
import SimilarTaskInfo from '@/pages/common/useCar/componets/similarTaskInfo.vue';
import { getStatus } from '../../list_config';

export default {
  components: {
    TravelInfo,
    DriveInfo,
    SimilarTaskInfo,
  },
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
      gomapUrl: getApp().globalData.config.appInfo.gomapUrl,
      infoOption: {},
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },

    taskStatus() {
      return (status) => {
        switch (Number(status)) {
          case 0:
            return '未分配';
          case 1:
            return '未开始';
          case 2:
            return '进行中';
          case 3:
            return '已结束';
          case 4:
            return '已取消';
          case 5:
            return '已失效';
        }
      };
    },
  },

  onLoad(option) {
  },
  onReady() {
  },
  methods: {
    queryStatus(val) {
      return getStatus(val || 0);
    },
  },
};
</script>

<style lang="scss" scoped>
.all-in {
  height: 100%;
  flex-direction: column;
}
.drive-info {
  margin: 15PX;
  padding: 0 20px;
}
</style>
