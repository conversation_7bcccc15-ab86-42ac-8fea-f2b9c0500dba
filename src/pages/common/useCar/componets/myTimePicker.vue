<!-- <template>
  <u-picker
    :show="show"
    ref="uPicker"
    :columns="columns"
    :defaultIndex="indexMP"
    @confirm="confirm"
    @change="changeHandler"
  ></u-picker>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      columns: [],
      indexMP: [],
    };
  },
  watch: {
    showPicker(val) {
      if (val) {
        setColumns();
        setDefaultTime();
      }
    },
  },
  methods: {
    confirm(e) {},
    changeHandler() {},
  },
};
</script> -->
