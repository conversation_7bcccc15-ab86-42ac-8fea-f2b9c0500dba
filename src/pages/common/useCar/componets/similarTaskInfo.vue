<template>
  <u-row align="start">
    <u-col span="6">
      <u-row>
        <u--text size="13" :text="itemData.applicantName" />
        <u--text size="13" :text="itemData.applicantPhone" />
      </u-row>
      <u--text size="13" :text="`今天${getTimenoDate(itemData.usingTime)}`" />
      <u--text size="13" :text="`${itemData.passengerNum}人`" />
    </u-col>
    <u-col span="2">
    </u-col>
    <u-col span="4">
      <u-row>
        <u-tag
        class="appointment-type_tag"
        color="#AA011C"
        borderColor="AA011C"
        plain
        shape="circle"
        :text="appointmentType(itemData.appointmentType)"
        />
      </u-row>
    </u-col>
  </u-row>
</template>

<script>
export default {
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
      timenoDate: null
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },
    getTimenoDate() {
      return (value) => {
        if (!value) {
          return "";    
        }
        let timeList = value.split(" ");
        let timenoDate = timeList[1];
        return timenoDate;
      }
    }
  },

  onLoad(option) {
  },
  onReady() {},
  methods: {
  },
};
</script>

<style lang="scss" scoped>
  .appointment-type_tag {
    ::v-deep .u-tag--primary--plain {
      border-color: #AA011C;
    }
  }
  .u-line {
    margin-right: 15px !important;
  }
  .passenger-box {
    flex-direction: row;
  }
</style>

