<template>
  <u-row>
    <u-col span="6">
      <u-row>
        <u--text size="13" :text="itemData.applicantName" />
        <u--text size="13" :text="itemData.applicantPhone" />
      </u-row>
      <u--text size="13" :text="`${itemData.passengerNum}人`" />
      <u-row>
        <u--text size="11" :text="itemData.usingTime" />
      </u-row>
    </u-col>
    <u-col span="2">
      <u-line margin="0 10px" length="60" color="#999999" direction="col"></u-line>
    </u-col>
    <u-col span="4">
        <u--text class="appointment-type" size="15" :text="appointmentType(itemData.appointmentType)" />
        <u-row align="center" justify="center">
          <u-col span="9">
              <u-tag
                class="trip-des"
                size="mini"
                :text="tripDes(itemData.status)"
                :color="queryStatus(itemData.status).color"
                :bgColor="queryStatus(itemData.status).bgColor"
                :borderColor="queryStatus(itemData.status).color"
              ></u-tag>
          </u-col>
        </u-row>
    </u-col>
  </u-row>
</template>

<script>
import { getStatus } from '../../list_config.js';
export default {
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },
    tripDes() {
      return (value) => {
        switch (Number(value)) {
          case 3:
            return '行程结束';
          case 4:
            return '行程取消';
        }
      };
    },
  },

  onLoad(option) {
  },
  onReady() {},
  methods: {
    queryStatus(val) {
      return getStatus(val) ? getStatus(val) : { color: '', bgColor: '' };
    },
  },
};
</script>

<style lang="scss" scoped>
  .u-line {
    margin-right: 15px !important;
  }
  .appointment-type {
    width: 100%;
    display: flex;
    justify-content: center !important;
  }
  .trip-des {
    ::v-deep .u-tag__text {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
</style>

