<template>
  <view>
    <u-row>
      <u-col span="6">
        <u-row class="employee-sty">
          <u-row v-if="itemData.employeeName" align="center" justify="center">
            <u--text size="11" color="#999999" style="margin-right: 10px;" :text="itemData.employeeName" />
            <u--text size="11" color="#999999" :text="itemData.phoneNumber" />
          </u-row>
          <u--text v-if="itemData.carNumber" size="13" align="center" text="车牌号" />
          <u--text v-if="itemData.carNumber" size="16" align="center" color="#AA011C" :text="itemData.carNumber" />
          <u--text v-else size="16" align="center" text="暂无车辆信息" />
        </u-row>
      </u-col>
      <u-col span="2">
        <u-line margin="0 10px" length="60" color="#999999" direction="col"></u-line>
      </u-col>
      <u-col span="4">
        <u-row justify="center">
          <u--image
            :showLoading="true"
            :src="globalConfig.iconImgPrefix + 'busorigin.png'"
            :width="itemData.status != 3? '28px':'38px'"
            :height="itemData.status != 3? '27px':'37px'"
            mode="aspectFit"
          ></u--image>
        </u-row>
        <u-row justify="center" align="center">
          <u-tag
            v-if="itemData.status != 3"
            :text="queryStatus(itemData.status).text"
            :color="queryStatus(itemData.status).color"
            :bgColor="queryStatus(itemData.status).bgColor"
            :borderColor="queryStatus(itemData.status).color"
            plain
            plainFill
            size="mini"
          ></u-tag>
        </u-row>
      </u-col>
    </u-row>
  </view>
</template>

<script>
import { getStatus } from '../../list_config';
export default {
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },
  },

  onLoad(option) {
  },
  onReady() {},
  methods: {
    queryStatus(val) {
      return getStatus(val || 0);
    },
    
  },
};
</script>

<style lang="scss" scoped>
  .employee-sty {
    flex-direction: column;
  }
  .u-line {
    margin-right: 15px !important;
  }
</style>

