<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="行程详情"
      :autoBack="autoBack"
      leftText=""
    >
    </top-navbar>
    <view class="bgCard middle-container">
      <view class="setOver">
        <uni-card
          spacing="12px"
          margin="12px 12px 0"
          shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
          :is-shadow="true"
          :border="false">
          <task-info
            :itemData="itemData"
          />
        </uni-card>
        <uni-card
          spacing="12px"
          margin="12px 12px 0"
          shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
          :is-shadow="true"
          :border="false">
          <travel-info
            :itemData="itemData"
          />
          <u-gap height="10"></u-gap>
          <drive-info
            :itemData="itemData"
          />
        </uni-card>
      </view>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import DriveInfo from '@/pages/common/useCar/componets/driveInfo.vue';
import TaskInfo from '@/pages/common/useCar/componets/taskInfo.vue';
import TravelInfo from '@/pages/common/useCar/componets/travelInfo.vue';
import { getStatus } from '../list_config';
import { formItems, orderFormItems } from '../workComponents/form_list';
import { getAppointmentTaskDetail } from '@/api/work/index';
import { getToken } from '@/utils/auth';
const img = require('@/static/images/iconImg/banbenxinxi.png');

export default {
  name: 'EndUseDetail',
  components: {
    TaskInfo,
    TravelInfo,
    DriveInfo
  },
  data() {
    return {
      gomapUrl: getApp().globalData.config.appInfo.gomapUrl,
      itemData: {},
      autoBack: true,
      latitude: 34.007287,
      longitude: 119.188279,
      model: '',
      infoOption: {},
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },

    taskStatus() {
      return (status) => {
        switch (Number(status)) {
          case 0:
            return '未分配';
          case 1:
            return '未开始';
          case 2:
            return '进行中';
          case 3:
            return '已结束';
          case 4:
            return '已取消';
          case 5:
            return '已失效';
        }
      };
    },
  },

  onLoad(option) {
    this.infoOption = option;
    this.infoInit(option);
  },
  onReady() {},
  methods: {
    async showBusHistoryTrack() {
      uni.navigateTo({
        url: `/pages/common/webview/index?url=${this.gomapUrl}&id=${this.infoOption.taskId}&token=${getToken()}&title=地图`
      })
    },
    async infoInit() {
      try {
        uni.showLoading({title: '正在获取信息'});
        const res = await getAppointmentTaskDetail(this.infoOption.taskId);
        uni.hideLoading()
        this.itemData = res.data;
        this.itemData.carNumber = res.data?.currentDispatchCar?.carNumber || '';
        this.itemData.employeeName = res.data?.currentEmployee?.employeeName || '';
        this.itemData.phoneNumber = res.data?.currentEmployee?.phoneNumber || '';
        this.itemData.startPointName = res.data?.startPoint?.pointName || '';
        this.itemData.endPointName = res.data?.endPoint?.pointName|| '';
        if (Number(res.data.appointmentType) !== 3) {
          this.itemData.applicantName = res.data.appointmentUser.userName;
          this.itemData.applicantPhone = res.data.appointmentUser.phoneNumber;
        }
      } catch (e) {
        console.log(e);
      }
    },

    queryStatus(val) {
      return getStatus(val || 0);
    },

    getFormItem() {
      let res;
      if (this.infoOption.pathType === 'first') {
        res = formItems.filter((item) => {
          if (
            !item.statusMap ||
            (item.statusMap?.length &&
              item.statusMap.includes(Number(this.itemData.status)))
          ) {
            return item;
          }
        });
      } else {
        res = orderFormItems;
      }
      return res;
    },

    changeWorkerShow() {
      this.workerShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
::v-deep .uni-forms-item__content {
  align-self: center;
}

.setOver {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
}

.map {
  width: 100%;
  height: 400rpx;
}

.middle-container {
  padding-top: 0;
}

.pb1 {
  padding-bottom: 1px;
}

.text_scrolling {
  white-space: nowrap;
  /* 保持文字在一行内显示 */
  overflow-x: auto;
  /* 隐藏溢出的内容 */
  // text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 80%;
}

.setbuttonstyle {
  display: flex;
  justify-content: space-around;
}
</style>
