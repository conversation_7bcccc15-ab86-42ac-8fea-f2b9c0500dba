<template>
  <LimeFloatingPanel ref="limtPanelRef">
    <going-travel-info :itemData="itemData" />
  </LimeFloatingPanel>
</template>

<script>
import LimeFloatingPanel from '@/pages/common/useCar/map/limeFloatingPanel.vue';
import GoingTravelInfo from '@/pages/common/useCar/componets/goingTravelInfo.vue';
import DriveInfo from '@/pages/common/useCar/componets/driveInfo.vue';
import TravelInfo from '@/pages/common/useCar/componets/travelInfo.vue';
import SimilarTaskInfo from '@/pages/common/useCar/componets/similarTaskInfo.vue';
import { getStatus } from '../list_config';
import { formItems, orderFormItems } from '../workComponents/form_list';
import {
  getAppointmentTaskDetail,
  coordinateConversion,
} from '@/api/work/index';
import { getToken } from '@/utils/auth';
const img = require('@/static/images/iconImg/banbenxinxi.png');

export default {
  components: {
    TravelInfo,
    DriveInfo,
    SimilarTaskInfo,
    GoingTravelInfo,
    LimeFloatingPanel,
  },
  data() {
    return {
      itemData: {},
      autoBack: true,
      latitude: 34.014338,
      longitude: 119.198033,
      model: '',
      infoOption: {},
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },

    taskStatus() {
      return (status) => {
        switch (Number(status)) {
          case 0:
            return '未分配';
          case 1:
            return '未开始';
          case 2:
            return '进行中';
          case 3:
            return '已结束';
          case 4:
            return '已取消';
          case 5:
            return '已失效';
        }
      };
    },
  },

  onLoad(option) {
    this.infoOption = option;
    this.infoInit();
  },
  onShow() {
    if (this.infoOption.taskStatus == 2) {
      this.timer = setInterval(() => {
        this.updateInfo();
      }, 10000);
    } else {
      this.infoInit();
    }
  },
  onReady() {
    // this.loadScript();
    // this.initMap();
  },
  onHide() {
    clearInterval(this.timer);
  },
  onUnload() {
    clearInterval(this.timer);
  },
  methods: {
    // initMap() {
    //   //定义地图中心点坐标
    //   var center=new TMap.LatLng(34.014338,119.198033)
    //   //定义map变量，调用 TMap.Map() 构造函数创建地图showBusHistoryTrack
    //   var map = new TMap.Map(document.getElementById('mapbox'), {
    //       center: center,//设置地图中心点坐标
    //       zoom:16,   //设置地图缩放级别
    //   });
    // },
    // loadScript() {
    //     var script = document.createElement("script");
    //     script.type = "text/javascript";
    //     script.src = "https://map.qq.com/api/gljs?v=1.exp&key=FJ6BZ-DCTLJ-BZCFY-XXWJX-WVV6H-LKFAH&callback=initMap";
    //     document.body.appendChild(script);
    // },
    async infoInit() {
      try {
        uni.showLoading({ title: '正在获取线路信息' });
        const res = await getAppointmentTaskDetail(this.infoOption.taskId);
        uni.hideLoading();
        this.itemData = res.data;
        this.itemData.carNumber = res.data?.currentDispatchCar?.carNumber || '';
        this.itemData.employeeName =
          res.data?.currentEmployee?.employeeName || '';
        this.itemData.phoneNumber =
          res.data?.currentEmployee?.phoneNumber || '';
        this.itemData.startPointName = res.data?.startPoint?.pointName || '';
        this.itemData.endPointName = res.data?.endPoint?.pointName || '';
        if (Number(res.data.appointmentType) !== 3) {
          this.itemData.applicantName = res.data.appointmentUser.userName;
          this.itemData.applicantPhone = res.data.appointmentUser.phoneNumber;
        }
        this.$refs.limtPanelRef.initData(this.itemData);
      } catch (e) {
        console.log(e);
      }
    },
    async updateInfo() {
      try {
        uni.showLoading({ title: '正在获取线路信息' });
        const res = await getAppointmentTaskDetail(this.infoOption.taskId);
        uni.hideLoading();
        const data = res.data;
        let currentPointObj = {
          latitude: data.realtimeLocation?.lat || '',
          longitude: data.realtimeLocation?.lng || ''
        };
        if (currentPointObj.latitude && currentPointObj.longitude) {
          this.$refs.limtPanelRef.updateInfo(currentPointObj);
        }
      } catch (e) {
        console.log(e);
      }
    },

    queryStatus(val) {
      return getStatus(val || 0);
    },

    getFormItem() {
      let res;
      if (this.infoOption.pathType === 'first') {
        res = formItems.filter((item) => {
          if (
            !item.statusMap ||
            (item.statusMap?.length &&
              item.statusMap.includes(Number(this.itemData.status)))
          ) {
            return item;
          }
        });
      } else {
        res = orderFormItems;
      }
      return res;
    },

    changeWorkerShow() {
      this.workerShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.map-box {
  flex: 1;
  overflow: hidden;
}
.all-in {
  height: 100%;
  flex-direction: column;
}
.info-box {
  padding: 15px;
  background: #ffffff;
}
</style>
