<template>
  <view class="usecarListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="用车记录"
      :autoBack="autoBack"
      leftText=""
    ></top-navbar>
    <contain-tab
      :showSearch="false"
      :showSelect="false"
      @scrolltolower="scrolltolower"
      @changeCurrent="changeCurrent"
      @serching="searchOperate"
      :topSelectList="topSelectList"
      :current="currentIndex"
      :tableData="tableData"
    >
      <template #default="slotProps">
        <UserCarOverview
          :itemData="slotProps.itemData"
          @handleDetail="cardItemClick(slotProps.itemData)"
        />
      </template>
    </contain-tab>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import ContainTab from '@/pages/components/containTab.vue';
import UserCar from '../../common/workComponents/userCar.vue';
import UserCarOverview from '../../common/workComponents/userCarOverview.vue';
import { topAllSelectList } from '../list_config.js';
import { getApplyTaskRecordList } from '@/api/work/index';
export default {
  components: {
    ContainTab,
    UserCar,
    UserCarOverview,
  },
  name: 'UseCar',
  data() {
    return {
      autoBack: true,
      topSelectList: topAllSelectList,
      currentIndex: '1',
      keyIndex: '2',
      showModel: false,
      queryData: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        [
          { label: '园区用车', id: 'parkUserCar', urlType: 'park' },
          { label: '生产用车', id: 'proUserCar', urlType: 'produce' },
        ],
      ],
      tableData: [],
      totalPage: 0,
      appointmentTypeMap: {
        1: '园区用车',
        2: '生产用车',
        3: '园区接驳',
      },
    };
  },
  onLoad() {
    this.initApplyRecord();
  },
  methods: {
    async initApplyRecord() {
      try {
        const res = await getApplyTaskRecordList(this.queryData);
        if (res.data.rows && res.data.rows.length) {
          this.tableData.push(...res.data.rows);
          this.tableData.forEach((element) => {
            element.appointmentTypeName =
              this.appointmentTypeMap[element.appointmentType];
            element.startPointName = element.startPoint?.pointName;
            element.endPointName = element.endPoint?.pointName;
          });
          this.totalPage = Math.ceil(res.data.total / this.queryData.pageSize);
        }
      } catch (error) {
        console.log(error);
      }
    },
    cardItemClick(data) {
      // status任务状态（0：未分配，1：未开始，2：进行中，3：已结束，4-已取消，5：已失效）
      if (data.status === '0' || data.status === '5') {
        return;
      }
      if (data.status === '3' || data.status == '4') {
        uni.navigateTo({
          url: `/pages/common/useCar/endUseDetail?taskId=${data.taskId}`,
        });
      }
      if (data.status === '1' || data.status === '2') {
        uni.navigateTo({
          url: `/pages/common/useCar/goingUseDetail?taskId=${data.taskId}&taskStatus=${data.status}`,
        });
      }
      // uni.navigateTo({
      //   url: `/pages/common/useCar/useDetail?taskId=${data.taskId}`,
      // });
      //this.$route.query
    },
    changeCurrent(val) {
      this.keyIndex = val;
      this.queryData.status = val;
      this.research();
    },
    searchOperate(val) {
      this.queryData.taskName = val;
      this.research();
    },
    research() {
      this.tableData = [];
      this.queryData.pageNum = 1;
      this.initApplyRecord();
    },
    scrolltolower(val) {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        this.$refs.uToast?.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.initApplyRecord();
    },
  },
};
</script>
<style lang="scss" scoped>
.u-slot_value {
  text-align: center;
}
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
</style>
