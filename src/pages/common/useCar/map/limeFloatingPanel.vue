<template>
  <view>
    <top-navbar
      bgColor="#AA001E"
      title="行程详情"
      :autoBack="autoBack"
      leftText=""
    >
    </top-navbar>
    <cutom-map
      ref="mapRef"
      style="width:100%;z-index: 999;"
      :class="panelHeight==260?'height_260':'height_334'"
    />
    <mapcontrol @change-map-type="changeBaseMap" :hasSetCenter="false" />
    <l-floating-panel
      :height="height"
      :anchors="anchors"
      @heightChange="heightChange"
    >
      <slot></slot>
    </l-floating-panel>
  </view>
</template>

<script>
import mapcontrol from '@/pages/common/map/mapcontrol.vue';
import lFloatingPanel from '@/uni_modules/lime-floating-panel/components/l-floating-panel/l-floating-panel.vue';
import CutomMap from '@/pages/common/useCar/map/map.vue';
export default {
  components: {
    lFloatingPanel,
    CutomMap,
    mapcontrol
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      autoBack: true,
      latitude: 34.00623,
      longitude: 119.187811,
      anchors: [],
      height: 0,
      txMapCenter: null, // 地图中心点坐标
      map: null, // 地图对象
      panelHeight:334,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const { windowHeight } = uni.getSystemInfoSync()
      this.anchors = [
        334,
        Math.round(0.5 * windowHeight),
      ];
      this.height = this.anchors[0];
    },
    initData(itemData) {
      this.$nextTick(() => {
        this.getPoint(itemData);
        this.$refs.mapRef.initData(itemData);
      })
    },
    /**
     * 切换地图三维和二维
     * @param String type - satellite:卫星地图；vector:平面地图
      
     */
    changeBaseMap(type) {
      this.$nextTick(() => {
        this.$refs.mapRef.changeBaseMap(type);
      })
    },
    updateInfo(currentPointObj) {
      this.$nextTick(() => {
        this.$refs.mapRef.updateCurrentPoint(currentPointObj);
      })
    },
    heightChange(val) {
      this.panelHeight = val.height;
      // this.$refs.mapRef.setFitBounds();
    },
    getPoint(itemData) {
      itemData.startPointObj = this.getCoordinate(itemData.startPoint);
      itemData.endPointObj = this.getCoordinate(itemData.endPoint);
      if (itemData.status == '2') {
        itemData.currentPointObj = {
          latitude: itemData.realtimeLocation?.lat || '',
          longitude: itemData.realtimeLocation?.lng || ''
        };
      }

    },
    getCoordinate(val) {
      if (val) {
        let obj = {
          latitude: val?.pointGpsStandard?.gcj02?.lat || '',
          longitude: val?.pointGpsStandard?.gcj02?.lng || '',
        }
        return obj;
      }
    },
  },
}
</script>

<style lang="scss" scoped>
  ::v-deep .l-floating-panel {
    z-index: 9999;
  }
  .height_260 {
    height: calc(100vh - 280px);
  }
  .height_334 {
    height: calc(100vh - 350px);
  }
  a .csssprite {
    display: none;
  }
  ::v-deep .logo-text {
    display: none !important;
  }
</style>