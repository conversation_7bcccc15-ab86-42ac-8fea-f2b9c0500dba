<template>
  <view
    id="mytxMapContainer"
    ref="mapContainer"
    />
</template>

<script>
import lFloatingPanel from '@/uni_modules/lime-floating-panel/components/l-floating-panel/l-floating-panel.vue';
export default {
  components: {
    lFloatingPanel
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      latitude: 34.00623,
      longitude: 119.187811,
      anchors: [],
      height: '',
      txMapCenter: null, // 地图中心点坐标
      map: null, // 地图对象
      markerLayer: null, // 地图对象
      geometries: [], // 点标记集合
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.txMapCenter = new TMap.LatLng(this.latitude, this.longitude);
      //定义map变量，调用 TMap.Map() 构造函数创建地图
      this.map = new TMap.Map(document.getElementById("mytxMapContainer"), {
        center: this.txMapCenter, //设置地图中心点坐标
        zoom: 13.9, //设置地图缩放级别
        pitch: 43.5, //设置俯仰角
        rotation: 45, //设置地图旋转角度
        baseMap: {
          // 设置卫星地图
          type: "vector",
        },
        disableDefaultUI: false
      });
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM);
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE);
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
    },
    initData(itemData) {
      //创建并初始化MultiMarker
      this.geometries = [
        {
          "id": "startPoint",   //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          "styleId": 'startPointStyle',  //指定样式id
          "position": new TMap.LatLng(itemData?.startPointObj?.latitude, itemData?.startPointObj?.longitude),  //点标记坐标位置
        },
        {//第二个点标记
          "id": "endPoint",
          "styleId": 'endPointStyle',
          "position": new TMap.LatLng(itemData?.endPointObj.latitude, itemData?.endPointObj.longitude)
        }
      ]
      if (itemData.currentPointObj&&itemData.currentPointObj.latitude&&itemData.currentPointObj.longitude) {
        let currentPoint = {
          "id": "currentPoint",
          "styleId": 'currentPointStyle',
          "position": new TMap.LatLng(itemData?.currentPointObj.latitude, itemData?.currentPointObj.longitude)
        };
        this.geometries.push(currentPoint);
      }
      this.markerLayer = new TMap.MultiMarker({
          map: this.map,  //指定地图容器
          //样式定义
          styles: {
              //创建一个styleId为"startPointStyle"的样式（styles的子属性名即为styleId）
              "startPointStyle": new TMap.MarkerStyle({ 
                  "width": 23,  // 点标记样式宽度（像素）
                  "height": 27, // 点标记样式高度（像素）
                  "src": "data:image/png;base64,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",  //图片路径
                  // "src": this.globalConfig.iconImgPrefix + 'startpointorigin.png',  //图片路径
                  //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
                  "anchor": { x: 16, y: 32 }  
              }),
              "endPointStyle": new TMap.MarkerStyle({ 
                  "width": 23,  // 点标记样式宽度（像素）
                  "height": 27, // 点标记样式高度（像素）
                  "src": "data:image/png;base64,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",  //图片路径
                  //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
                  "anchor": { x: 16, y: 32 }  
              }),
              "currentPointStyle": new TMap.MarkerStyle({ 
                  "width": 23,  // 点标记样式宽度（像素）
                  "height": 27, // 点标记样式高度（像素）
                  "src": "data:image/png;base64,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",  //图片路径
                  //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
                  "anchor": { x: 16, y: 32 }  
              }) 
          },
          //点标记数据数组
          geometries: this.geometries,
      });
      this.setFitBounds();
    },
    setFitBounds() {
      let coords = this.geometries.map(item => {
        return item.position;
      });
      //创建LatLngBounds实例
      let latlngBounds = new TMap.LatLngBounds();
      //将坐标逐一做为参数传入extend方法，latlngBounds会根据传入坐标自动扩展生成
      for(let i = 0;i<coords.length; i++){
          latlngBounds.extend(coords[i]);
      }
      //调用fitBounds自动调整地图显示范围
      this.map.fitBounds(latlngBounds, {padding: {top:50, bottom:50, left:30, right:30}});
    },
    updateCurrentPoint(obj) {
      this.markerLayer.updateGeometries([
        {
          "id": "currentPoint",
          "styleId": 'currentPointStyle',
          "position": new TMap.LatLng(obj.latitude, obj.longitude),
        }
      ])
    },
        // 添加切换地图类型的方法
    changeBaseMap(type) {
      if (!this.map) return;
      this.map.setBaseMap({
        type: type, // 'satellite'为卫星图，'vector'为矢量图
      });
    },
    getCoordinate(val) {
      if (val) {
        let obj = {
          latitude: val?.pointGpsStandard?.gcj02?.lat || '',
          longitude: val?.pointGpsStandard?.gcj02?.lng || '',
        }
        return obj;
      }
    },
  },
}
</script>

<style lang="scss" scoped>
  .height_260 {
    height: calc(100vh - 304px);
  }
  .height_334 {
    height: calc(100vh - 380px);
  }
  ::v-deep .logo-text {
    display: none !important;
  }
</style>