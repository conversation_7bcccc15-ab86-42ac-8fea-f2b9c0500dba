<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="我的车辆"
      :autoBack="true"
      leftText=""
    ></top-navbar>
    <view class="bgCard middle-container">
      <view>
        <view class="topbox">
          <view v-if="isBind" style="height: 100%">
            <u-row style="height: 100%">
              <u-col span="6" align="center">
                <u-image
                  width="158rpx"
                  height="156rpx"
                  mode="aspectFit"
                  :src="globalConfig.iconImgPrefix + 'cartexi.png'"
                >
                </u-image>
                <u-gap height="32rpx"></u-gap>
                <u--text
                  size="11"
                  align="center"
                  color="#999999"
                  :text="formData.carTypeName"
                />
              </u-col>
              <u-col span="6">
                <u--text size="14" align="left" text="当前已绑定" />
                <u-gap height="16rpx"></u-gap>
                <u--text size="15" align="left" :text="formData.carNumber" />
                <u-gap height="32rpx"></u-gap>
                <u--text
                  size="12"
                  align="left"
                  color="#999999"
                  :text="formData.bindTime"
                />
                <u-gap height="16rpx"></u-gap>
                <view class="remove-bind">
                  <u-button
                    size="mini"
                    type="primary"
                    text="解除绑定"
                    @click="removeBind"
                  ></u-button>
                </view>
              </u-col>
            </u-row>

            <!-- <uni-row>
              <view
                class="flex mb5 align_center"
                v-for="(item, index) in formItem"
                :key="index"
              >
                <span class="mr4">{{ item.name }}:</span>
                <span>{{ formData[item.prop] }}</span>
              </view>
            </uni-row>
            <uni-row class="flex">
              <uni-col :span="10">
                <u-button type="info" size="small" @click="reBack"
                  >返回</u-button
                >
              </uni-col>
              <uni-col :span="4"> </uni-col>
              <uni-col :span="10">
                <u-button size="small" type="primary" @click="removeBind"
                  >解除绑定</u-button
                >
              </uni-col>
            </uni-row> -->
          </view>
          <view v-else class="flex justify-center align-center scan-car">
            <u-image
              width="247rpx"
              height="247rpx"
              mode="aspectFit"
              :src="globalConfig.iconImgPrefix + 'sanimage.png'"
              @click="scanCode"
            ></u-image>
            <u-gap height="13"></u-gap>
            <span class="scan-car-text">扫码绑定车辆</span>
          </view>
        </view>
        <u-gap height="20rpx" bgColor="#F2F3F3"></u-gap>
        <uni-section titleFontSize="15" title="车辆绑定记录" type="line" />
        <view>
          <card-list
            class="card-list"
            v-if="bindingHistory.length"
            :cardList="bindingHistory"
            @scrolltolower="scrolltolower"
          >
            <template #default="slotProps">
              <bind-car-item :itemData="slotProps.itemData" />
            </template>
          </card-list>
          <u-empty
            width="120"
            height="120"
            marginTop="20"
            v-else
            mode="list"
            icon="/static/images/iconImg/empty.png"
          />
        </view>
      </view>
    </view>
    <modal
      :show="modalShow"
      confirmText="确认绑定"
      @confirm="modalConfirm"
      @cancel="modalCancel"
    >
      <view class="info">
        <view
          class="flex mb5 align_center"
          v-for="(item, key) in carInfo"
          :key="key"
        >
          <template v-if="key === 'carNumber'">
            <span class="mr4">车牌号：</span>
            <span>{{ item }}</span>
          </template>
          <template v-else-if="key === 'carType'">
            <span class="mr4">车辆类型：</span>
            <span>{{ item }}</span>
          </template>
          <template v-else-if="key === 'deptName'">
            <span class="mr4">部门：</span>
            <span>{{ item }}</span>
          </template>
          <template v-else-if="key === 'supplierName'">
            <span class="mr4">服务商：</span>
            <span>{{ item }}</span>
          </template>
        </view>
      </view>
    </modal>
    <u-toast ref="uToast"></u-toast>
    <u-loading-page :loading="loading" loadingText="请稍后" />
    <qr-code
      v-show="isScaning"
      ref="qrScan"
      @upScaning="upScaning"
      @stopScan="stopScan"
    ></qr-code>
  </view>
</template>

<script>
import CardList from '@/pages/components/cardList.vue';
import BindCarItem from './componets/bindCarItem.vue';
import Modal from '@/pages/components/modal.vue';
import { getMyCar, bindMyCar, carBindList } from '@/api/myCar/index';
import { getDictionaryQueries } from '@/api/login.js';
import QrCode from '@/components/qr/code';
export default {
  components: {
    Modal,
    QrCode,
    CardList,
    BindCarItem,
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      tableloading: false,
      cartTypes: [],
      bindingHistory: [],
      isScaning: false,
      formItem: [
        {
          prop: 'carNumber',
          name: '当前绑定车辆',
        },
        {
          prop: 'carTypeName',
          name: '车辆类型',
        },
        {
          prop: 'bindTime',
          name: '车辆绑定时间',
        },
      ],
      formData: {
        carType: '',
        carTypeName: '',
        carNumber: '',
        bindTime: '',
      },
      carInfo: {
        carNumber: '',
        carType: '',
        deptName: '',
        supplierName: '',
      },
      isBind: false,
      modalShow: false,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      totalPage: 0,
      current: 1,
    };
  },
  onLoad() {
    this.init();
  },
  methods: {
    init() {
      this.getMyCarInfo();
      this.getBindingHistory();
    },
    stopScan() {
      this.isScaning = false;
    },
    /*扫码结果*/
    async upScaning(val) {
      this.isScaning = false;
      if (val.status) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: val.msg,
            type: 'error',
          })
        );
      } else {
        await this.$nextTick();

        if (val.carId) {
          this.modalShow = true;
          this.carInfo = { ...val };
        }
      }
    },
    scanCode() {
      //#ifdef H5
      this.isScaning = true;
      this.$refs.qrScan.scanBefore();

      //#endif
      //#ifdef APP-PLUS
      uni.scanCode({
        success: async (res) => {
          // 扫码跳转，IOS不可以，需要加入$nextTick和setTimeout
          await this.$nextTick();
          setTimeout(async () => {
            let info = JSON.parse(res.result);
            if (info.carId) {
              this.modalShow = true;
              this.carInfo = { ...info };
            } else {
              return;
            }
          }, 0);
        },
      });
      // #endif
    },
    queryItems(val) {
      return this.formItem.filter((item) => item.status.includes(val));
    },
    getCartTypeName(type) {
      let item = this.cartTypes.find((item) => item.dictValue === type);
      return item.dictLabel;
    },
    async getCartTypes() {
      const res = await getDictionaryQueries('dispatch_car_type');
      this.cartTypes = res?.data;
      this.formData.carTypeName = this.getCartTypeName(this.formData.carType);
    },
    async getMyCarInfo() {
      try {
        const res = await getMyCar();
        if (res.data && res.data.dispatchCar) {
          this.isBind = true;
          this.formData = {
            ...res?.data,
            ...res?.data?.dispatchCar,
            carTypeName: '',
          };
          this.getCartTypes();
        } else {
          this.isBind = false;
        }
      } catch (error) {}
    },
    async modalConfirm() {
      this.loading = true;
      let params = {
        dispatchCarId: this.carInfo.carId,
        bindStatus: '1',
      };
      try {
        const res = await bindMyCar(params);
        let msgType = res.code == 200 ? 'success' : 'error';
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: `${res.msg}`,
            type: msgType,
          })
        );
        this.modalShow = false;
        this.getMyCarInfo();
        this.bindingHistory = [];
        this.queryParams.pageNum = 1;
        this.getBindingHistory();
      } catch (error) {}
      this.loading = false;
    },
    async getBindingHistory() {
      try {
        this.tableloading = true;
        const res = await carBindList(this.queryParams);
        if (res.data && res.data.rows) {
          let bindingHistory = [];
          let total = 0;
          bindingHistory = res.data.rows;
          total = res.data.total;
          this.totalPage = Math.ceil(total / this.queryParams.pageSize);
          if (bindingHistory.length)
            this.bindingHistory.push(...bindingHistory);
        } else {
          console.log('err');
        }
        this.tableloading = false;
      } catch (error) {
        this.tableloading = false;
      }
    },
    // change(e) {
    //   this.current = e.current;
    //   this.queryParams.pageNum = e.current;
    //   this.getBindingHistory();
    // },
    scrolltolower() {
      this.queryParams.pageNum++;
      if (this.queryParams.pageNum > this.totalPage) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.getBindingHistory();
    },
    modalCancel() {
      this.carInfo = {};
      this.modalShow = false;
    },
    async removeBind() {
      let params = {
        bindId: this.formData.bindId,
        dispatchCarId: this.formData.dispatchCarId,
        bindStatus: '0',
      };
      const res = await bindMyCar(params);
      let msgType = res.code == 200 ? 'success' : 'error';
      this.$refs.uToast.show(
        this.$setToastMsg({
          message: `${res.msg}`,
          type: msgType,
        })
      );
      this.getMyCarInfo();
      this.bindingHistory = [];
      this.queryParams.pageNum = 1;
      this.getBindingHistory();
    },
    reBack() {
      this.$tab.reLaunch('/pages/work/index');
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.middle-container {
  padding: 0;
}

.scan-car {
  flex-direction: column;
}

.scan-car-text {
  font-size: 14px;
  color: #333333;
  font-weight: 400;
}

.info {
  width: 100%;
  margin-left: 20px;
}
.topbox {
  height: 370rpx;
  padding: 35rpx 0;
}
.remove-bind {
  width: 40%;
}
.todoListPage .bgCard .u-list {
  height: calc(100vh - 300px) !important;
  overflow-y: auto;
  ::v-deep .u-list-item:last-child {
    margin-bottom: 20px;
  }
}
</style>
