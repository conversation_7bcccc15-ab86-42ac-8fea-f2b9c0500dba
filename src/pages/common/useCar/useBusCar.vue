<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      leftText=""
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card
        shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <u--form
          :model="itemData"
          label-width="100"
          :rules="rules"
          ref="formRef"
        >
          <u-form-item label="申请人姓名" prop="applicantName" borderBottom>
            <u--input
              v-model="itemData.applicantName"
              placeholder="请输入申请人姓名"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item label="手机号码" prop="applicantPhone" borderBottom>
            <u--input
              v-model="itemData.applicantPhone"
              disabled
              placeholder="请输入手机号"
              disabledColor="#ffffff"
              border="none"
            ></u--input>
          </u-form-item>

          <!-- 选择站点起点 -->
          <!-- <u-form-item
            label="起点"
            prop="startPoint"
            borderBottom
            required
            @click="openAreaSelect('startPoint'), hideKeyboard()"
          >
            <u--input
              v-model="startPoint"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择起点"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item> -->
          <u-form-item label="起点" prop="startPoint" borderBottom required>
            <u--input
              v-model="itemData.startPoint"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              placeholder="请填写起点"
              border="none"
            ></u--input>
          </u-form-item>
          <!-- <u-form-item
            label="起点"
            prop="startPoint"
            borderBottom
            @click="openAreaScan(), hideKeyboard()"
          >
            <u--input
              v-model="startPoint"
              disabled
              disabledColor="#ffffff"
              placeholder="请扫码获取起点"
              border="none"
            ></u--input>
            <u-icon slot="right" name="scan" size="24"></u-icon>
          </u-form-item> -->

          <!-- 选择站点终点 -->
          <!-- <u-form-item
            label="终点"
            prop="endPoint"
            required
            @click="openAreaSelect('endPoint'), hideKeyboard()"
            borderBottom
          >
            <u--input
              v-model="endPoint"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择终点"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item> -->
          <u-form-item label="终点" prop="endPoint" required borderBottom>
            <u--input
              maxlength="50"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              v-model="itemData.endPoint"
              placeholder="请填写终点"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="开始时间"
            prop="usingTime"
            borderBottom
            required
            @click="chooseUsingTime"
          >
            <u--input
              v-model="itemData.usingTime"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              placeholder="请选择开始时间"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="结束时间"
            prop="endTime"
            borderBottom
            required
            @click="chooseEndTime"
          >
            <u--input
              v-model="itemData.endTime"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              placeholder="请选择结束时间"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="乘车人数"
            prop="passengerNum"
            borderBottom
            required
          >
            <u-number-box
              :min="1"
              :max="1000"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              v-model="itemData.passengerNum"
            ></u-number-box>
          </u-form-item>

          <u-form-item label="备注" prop="remark">
            <u--textarea
              v-model="itemData.remark"
              :disabled="!!applyId"
              disabledColor="#ffffff"
              placeholder="请输入内容"
              maxlength="100"
            ></u--textarea>
          </u-form-item>
        </u--form>
        <view slot="actions">
          <u-button
            v-if="!applyId"
            type="primary"
            text="提交申请"
            @click="pushData"
            size="large"
          ></u-button>
          <u-button
            v-if="applyId&&itemData.requestId"
            type="primary"
            text="流转记录"
            @click="lookWorkFlow"
            size="large"
          ></u-button>
        </view>
      </uni-card>
    </view>

    <tki-tree
      ref="tkitree"
      :foldAll="true"
      :range="areaList"
      rangeKey="pointName"
      idKey="pointId"
      @confirm="confirmArea"
      @cancel="cancelArea"
      confirmColor="#aa001e"
    />
    <qr-code
      v-show="isScaning"
      ref="qrScan"
      @upScaning="upScaning"
      @stopScan="stopScan"
    ></qr-code>
    <!-- <u-datetime-picker
      :show="showUsingTime"
      mode="time"
      v-model="usingTime"
      :minHour="minHour"
      :maxHour="maxHour"
      :minMinute="minMinute"
      :maxMinute="maxMinute"
      :filter="filterTime"
      @close="showUsingTime = false"
      @cancel="showUsingTime = false"
      @confirm="usingTimeConfirm"
      @change="usingTimeChange"
    ></u-datetime-picker> -->
    <u-datetime-picker
      :show="showUsingTime"
      mode="datetime"
      v-model="usingTime"
      :minDate="Number(new Date())"
      @close="showUsingTime = false"
      @cancel="showUsingTime = false"
      @confirm="usingTimeConfirm"
    ></u-datetime-picker>
    <u-datetime-picker
      :show="showEndTime"
      mode="datetime"
      v-model="endTime"
      :minDate="Number(new Date())"
      @close="showEndTime = false"
      @cancel="showEndTime = false"
      @confirm="endTimeConfirm"
    ></u-datetime-picker>
    <u-loading-page :loading="loading" loadingText="请稍后" />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { busAppointment, getBusStation } from '@/api/appointment/index';
import tkiTree from '@/components/tki-tree/tki-tree.vue';
import QrCode from '@/components/qr/code';
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
  components: {
    tkiTree,
    QrCode,
  },
  data() {
    return {
      applyId: '',
      usingTime: null,
      endTime: null,
      minHour: new Date().getHours(),
      maxHour: 17,
      minMinute: 0,
      maxMinute: 45,
      loading: false,
      showUsingTime: false,
      showEndTime: false,
      itemData: {
        startPoint: null,
        endPoint: null,
        usingTime: null,
        endTime: null,
        applicantName: this.$store.state.user.name,
        remark: null,
        applicantPhone: this.$store.state.user.phoneNumber,
        passengerNum: 1,
      },
      isScaning: false,
      autoBack: true,
      startPoint: null,
      startPointId: null,
      endPoint: null,
      endPointId: null,
      areaList: [],
      title: '',
      rules: {
        applicantName: {
          required: true,
          trigger: ['blur'],
          message: '请输入访客姓名',
        },

        // startPoint: {
        //   required: true,
        //   type: 'object',
        //   message: '请选择起点',
        //   trigger: ['blur', 'change'],
        // },
        // endPoint: {
        //   type: 'object',
        //   required: true,
        //   trigger: ['change'],
        //   message: '请选择终点',
        // },
        startPoint: {
          required: true,
          message: '请填写起点',
          trigger: ['blur'],
        },
        endPoint: {
          required: true,
          trigger: ['blur'],
          message: '请填写终点',
        },
        passengerNum: {
          type: 'number',
          required: true,
          trigger: 'blur',
          message: '请输入乘车人数',
        },
        // usingTime: [
        //   {
        //     required: true,
        //     trigger: 'blur',
        //     message: '请选择来访时间',
        //   },
        //   {
        //     validator: (rule, value, callback) => {
        //       const currentDate = new Date();
        //       const currentHour = currentDate.getHours();

        //       return currentHour < 18;
        //     },
        //     message: '17:45以后不可申请用车',
        //     trigger: ['change', 'blur'],
        //   },
        //   {
        //     validator: (rule, value, callback) => {
        //       const currentDate = new Date();
        //       const currentHour = currentDate.getHours();
        //       const currentMinute = currentDate.getMinutes();
        //       const str = value.slice(-5).split(':');

        //       const hour = Number(str[0]);
        //       const minute = Number(str[1]);

        //       return !(hour == currentHour && minute < currentMinute);
        //     },
        //     message: '申请时间不得小于当前时间',
        //     trigger: ['change', 'blur'],
        //   },
        // ],
        usingTime: [
          {
            required: true,
            trigger: 'change',
            message: '请选择开始时间',
          },
        ],
        endTime: [
          {
            required: true,
            trigger: 'change',
            message: '请选择结束时间',
          },
        ],
        typePoint: '',
        applicantPhone: [
          {
            type: 'number',
            max: 11,
            required: true,
            message: '请填写正确的手机号',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',

            trigger: ['change', 'blur'],
          },
        ],
      },
    };
  },
  onLoad(option) {
    this.title = option.title;
    if(option.applyId) {
      this.applyId = option.applyId;
      let info = option.info ? JSON.parse(decodeURIComponent(option.info)) : null;
      this.getInfo(info);
    }
    // this.getVehicleStation();
  },
  // onShow() {
  //   let currenDate = new Date();
  //   let date = formatDate(currenDate, 'yyyy-MM-dd');
  //   const currentHour = currenDate.getHours();
  //   const currentMinute = currenDate.getMinutes();
  //   const arr = [0, 15, 30, 45];
  //   for (var i = 0; i < arr.length; i++) {
  //     if (currentMinute > arr[3]) {
  //       this.itemData.usingTime = `${date} ${this.padZero(currentHour + 1)}:00`;
  //       this.usingTime = `${currentHour + 1}:00`;
  //       break;
  //     }

  //     if (currentMinute == arr[i]) {
  //       this.itemData.usingTime = `${date} ${this.padZero(currentHour)}:${
  //         currentMinute == 0 ? '00' : currentMinute
  //       }`;
  //       this.usingTime = `${currentHour}:${
  //         currentMinute == 0 ? '00' : currentMinute
  //       }`;
  //       break;
  //     }
  //     if (arr[i] < currentMinute && currentMinute < arr[i + 1]) {
  //       this.itemData.usingTime = `${date} ${this.padZero(currentHour)}:${
  //         arr[i + 1]
  //       }`;
  //       this.usingTime = `${currentHour}:${arr[i + 1]}`;
  //       break;
  //     }
  //   }
  //   //this.usingTime = this.itemData.usingTime;
  // },
  methods: {
    getInfo(info) {
      if (info && typeof info === 'object') {
        Object.assign(this.itemData, {
          startPoint: info.startPoint || '',
          endPoint: info.endPoint || '',
          usingTime: info.usingTime || '',
          endTime: info.endTime || '',
          remark: info.remark || '',
          passengerNum: info.passengerNum || 1,
          requestId: info.requestId || ''
        });
      }
    },
    //查看流转记录
    lookWorkFlow() {
      this.itemData?.requestId &&uni.navigateTo({
        url: `/pages/common/acrossAppointRecode/workFlow?requestId=${this.itemData.requestId}`,
      });
    },
    //补零
    padZero(val) {
      return (val + '').padStart(2, 0);
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    chooseUsingTime() {
      // const currentDate = new Date();
      // const currentHour = currentDate.getHours();
      // const currentMinute = currentDate.getMinutes();
      // if (currentHour >= 17 && currentMinute > 45) {
      //   this.$refs.uToast.show(
      //     this.$setToastMsg({
      //       message: '17:45以后不可申请用车',
      //       type: 'error',
      //     })
      //   );
      //   return;
      // } else {
      //   this.minHour = currentHour;
      //   if (currentMinute > 45) {
      //     this.minHour = currentHour + 1;
      //   }
      if (this.applyId) return;
      this.showUsingTime = true;
      this.hideKeyboard();
    },
    chooseEndTime() {
      if (this.applyId) return;
      this.showEndTime = true;
      this.hideKeyboard();
    },
    // usingTimeConfirm(e) {
    //   let date = formatDate(new Date(), 'yyyy-MM-dd');
    //   this.itemData.usingTime = `${date} ${e.value}`;
    //   this.showUsingTime = false;
    // },
    usingTimeConfirm(e) {
      let date = formatDate(e.value, 'yyyy-MM-dd hh:mm');
      this.itemData.usingTime = `${date}`;
      this.showUsingTime = false;
    },
    endTimeConfirm(e) {
      let date = formatDate(e.value, 'yyyy-MM-dd hh:mm');
      this.itemData.endTime = `${date}`;
      this.showEndTime = false;
    },
    usingTimeChange(e) {
      const currentDate = new Date();
      const currentHour = currentDate.getHours();
      const currentMinute = currentDate.getMinutes();
      const specifiedTime = e.value;
      const specifiedHour = parseInt(specifiedTime.split(':')[0]);
      //this.minMinute = specifiedHour > currentHour ? 0 : currentMinute;
      // this.maxMinute = specifiedHour < 18 ? 59 : 0;
    },
    /*过滤分钟选择*/
    filterTime(mode, options) {
      if (mode == 'minute') {
        return options.filter((item) =>
          ['00', '15', '30', '45'].includes(item)
        );
      }
      return options;
    },
    /*获取接驳路线树形集合*/
    async getVehicleStation() {
      try {
        const res = await getBusStation();
        if (res.data.length) this.areaList = this.checkGate(res.data);
      } catch (e) {
        console.log(e);
      }
    },

    checkGate(arr) {
      if (!Array.isArray(arr)) {
        return [];
      }
      const data = arr.map((item) => {
        if (item.routePoints?.length) {
          item.pointName = item.routeName;
          item.pointId = item.routeId;
          item.disabled = true;
          item.children = this.checkGate(item.routePoints);
        }
        return item;
      });
      return data;
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    stopScan() {
      this.isScaning = false;
    },
    /*扫码结果*/
    upScaning(val) {
      this.isScaning = false;
      if (val.status) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: val.msg,
            type: 'error',
          })
        );
        return;
      }

      this.itemData.startPoint = {
        pointName: val['pointName'],
        coordinate: val['coordinate'] || '',
      };
      this.startPoint = val['pointName'];
    },
    openAreaScan() {
      this.isScaning = true;
      this.$refs.qrScan.scanBefore();
    },

    openAreaSelect(type) {
      this.typePoint = type;
      this.$refs.tkitree._show();
      this.$refs.tkitree._showbackData(
        this.typePoint == 'endPoint' ? this.endPointId : this.startPointId
      );
    },
    confirmArea(e) {
      if (
        (this.typePoint == 'endPoint' && this.startPointId == e[0].pointId) ||
        (this.typePoint == 'startPoint' && this.endPointId == e[0].pointId)
      ) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '起点不能与终点相同',
            type: 'error',
          })
        );
        return;
      }
      this.itemData[this.typePoint] = {
        pointName: e[0].pointName,
        coordinate: e[0].coordinate || '',
      };
      if (this.typePoint == 'endPoint') {
        this.endPoint = e[0].pointName;
        this.endPointId = e[0].pointId;
      } else {
        this.startPoint = e[0].pointName;
        this.startPointId = e[0].pointId;
      }

      this.$refs.tkitree._hide();
    },
    cancelArea() {
      this.$refs.tkitree._hide();
    },

    pushData() {
      this.$refs.formRef
        .validate()
        .then(async (res) => {
          if (res) {
            try {
              this.loading = true;
              const res = await busAppointment(this.itemData);
              if (res.code == 200) {
                this.$refs.uToast.show(
                  this.$setToastMsg({
                    message: '接驳申请成功',
                    type: 'success',
                  })
                );
                setTimeout(() => uni.navigateBack(), 500);
              }
            } catch (e) {
              // this.$refs.uToast.show(
              //   this.$setToastMsg({
              //     message: '申请失败',
              //     type: 'error',
              //   })
              // );
            } finally {
              this.loading = false;
            }
          }

          //uni.navigateBack()
        })
        .catch((errors) => {
          // this.$refs.uToast.show(
          //   this.$setToastMsg({
          //     message: '校验失败',
          //     type: 'error',
          //   })
          // );
        });
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
::v-deep .u-form-item__content {
  align-self: center;
}
// .uni-card {
//   overflow: scroll;
//   height: 80%;
//   ::v-deep .uni-card__content {
//     height: 90%;
//   }
// }
.setOver {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
}

.middle-container {
  padding-top: 0px;
}

.pb1 {
  padding-bottom: 1px;
}
</style>
