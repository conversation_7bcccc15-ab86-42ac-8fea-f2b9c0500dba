<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      leftText=""
    ></top-navbar>
    <view>
      <uni-card
        shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <u--form
          :model="itemData"
          label-width="80"
          :rules="rules"
          ref="formRef"
        >
          <u-form-item
            label="作业类型"
            prop="kindId"
            borderBottom
            required
            @click="(showWorkType = true), hideKeyboard()"
          >
            <u--input
              v-model="workType"
              placeholder="请选择作业类型"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="用车时间"
            prop="usingTime"
            required
            @click="(showUserTime = true), hideKeyboard()"
            borderBottom
          >
            <u--input
              v-model="itemData.usingTime"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择时间"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="起点"
            prop="startPoint"
            required
            @click="openAreaSelect('start'), hideKeyboard()"
            borderBottom
          >
            <u--input
              v-model="startPoint"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择起点"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="终点"
            prop="endPoint"
            required
            @click="openAreaSelect('end'), hideKeyboard()"
            borderBottom
            v-if="this.useType == 2"
          >
            <u--input
              v-model="endPoint"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择终点"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item label="运输物品" prop="goods" borderBottom required>
            <u--input
              v-model="itemData.goods"
              placeholder="请填写运输物品"
              maxlength="20"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="货物重量"
            prop="goodsWeight"
            borderBottom
            required
          >
            <u--input
              v-model="itemData.goodsWeight"
              placeholder="请填写货物重量"
              type="number"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item label="物品照片" prop="goodsPhoto" borderBottom>
            <u-upload
              :fileList="fileList1"
              @afterRead="afterRead"
              @delete="deletePic"
              name="1"
              :multiple="true"
              :maxCount="3"
              uploadText="上传图片"
            />
          </u-form-item>
          <u-form-item label="备注" prop="remark">
            <u--textarea
              v-model="itemData.remark"
              placeholder="请输入内容"
            ></u--textarea>
          </u-form-item>
        </u--form>
        <view class="mt8">
          <u-button
            type="primary"
            text="提交"
            @click="pushData"
            size="large"
          ></u-button>
        </view>
      </uni-card>
    </view>
    <u-action-sheet
      :actions="workTypelist"
      :show="showWorkType"
      cancelText="取消"
      @select="selectType"
      @close="showWorkType = false"
    ></u-action-sheet>

    <u-datetime-picker
      :show="showUserTime"
      v-model="usingTime"
      mode="datetime"
      @close="showUserTime = fasle"
      @cancel="showUserTime = fasle"
      @confirm="timePanelConfirm"
    ></u-datetime-picker>

    <tki-tree
      ref="tkitree"
      :foldAll="true"
      :range="areaList"
      rangeKey="areaName"
      idKey="areaId"
      @confirm="confirmArea"
      @cancel="cancelArea"
      confirmColor="#aa001e"
    />

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import config from '@/config.js';
import { getWorkTypeList } from '@/api/work/index.js';
import {
  queryVehicleStationRequest,
  applyVehicleRequest,
} from '@/api/vehicleApply/index.js';
import { getToken } from '@/utils/auth.js';
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
import tkiTree from '@/components/tki-tree/tki-tree.vue';
import { result } from 'lodash';

export default {
  components: {
    tkiTree,
  },
  data() {
    return {
      itemData: {
        startPoint: null,
        endPoint: null,
        kindId: null,
        appointmentType: '',
        remark: null,
        goodsWeight: null,
        goods: null,
        usingTime: null,
      },
      title: '',
      useType: 2,
      autoBack: true,
      showUserTime: false,
      usingTime: null,
      showWorkType: false,
      workTypelist: [],
      workType: null,
      areaList: [],
      startPoint: null,
      endPoint: null,
      pointType: 'start',
      fileList1: [],
      header: {
        Authorization: 'Bearer ' + getToken(),
      },
      uploadUrl: config.baseUrl + '/system/oss/upload',
      rules: {
        kindId: {
          type: 'string',
          required: true,
          message: '请选择作业类型',
          trigger: ['blur', 'change'],
        },
        usingTime: {
          required: true,
          message: '请选择时间',
          trigger: ['blur', 'change'],
        },
        startPoint: {
          required: true,
          type: 'object',
          message: '请选择起点',
          trigger: ['blur', 'change'],
        },
        endPoint: [
          {
            required: true,
            message: '请选择终点',
            type: 'object',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              if (this.useType == 2 && !value) {
                return true;
              } else {
                callback();
              }
            },
            message: '请选择终点',
          },
        ],
        goods: {
          required: true,
          message: '请输入运输物品',
          trigger: ['blur', 'change'],
        },
        goodsWeight: {
          required: true,
          message: '请输入货物重量',
          trigger: ['blur', 'change'],
        },
      },
    };
  },
  onLoad(option) {
    this.useType = option.name == 'ParkUserCar' ? 2 : 1;
    this.title = option.title;

    this.getWorkType();
    this.getVehicleStation();
  },
  methods: {
    async getWorkType() {
      const res = await getWorkTypeList({ carUseType: this.useType });
      this.workTypelist = res.rows.reduce((pre, next) => {
        next.name = next.kindName;
        pre.push(next);
        return pre;
      }, []);
    },

    async getVehicleStation() {
      try {
        const res = await queryVehicleStationRequest();
        if (res.data.length) this.areaList = res.data;
      } catch (e) {
        console.log(e);
      }
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    queryStatus(val) {
      return getStatus(val || 0);
    },
    getFormItem() {
      return formItems.filter((item) => {
        if (
          !item.statusMap ||
          (item.statusMap?.length &&
            item.statusMap.includes(this.itemData.status))
        ) {
          return item;
        }
      });
    },
    selectType(obj) {
      this.workType = obj.kindName;
      this.itemData.kindId = obj.id;
      this.showWorkType = false;
    },

    timePanelConfirm(e) {
      this.itemData.usingTime = formatDate(e.value, 'yyyy-MM-dd hh:mm');
      this.showUserTime = false;
    },
    openAreaSelect(type) {
      this.pointType = type;
      this.$refs.tkitree._show();

      this.$refs.tkitree._showbackData(
        this.pointType == 'start'
          ? this.itemData.startPoint
          : this.itemData.endPoint
      );
    },
    confirmArea(e) {
      if (e?.length && this.pointType == 'start') {
        this.startPoint = e[0].areaName;
        this.itemData.startPoint = {
          pointName: e[0].areaName,
          coordinate: e[0].areaCoord || '',
        };
      }
      if (e?.length && this.pointType == 'end') {
        this.endPoint = e[0].areaName;
        this.itemData.endPoint = {
          pointName: e[0].areaName,
          coordinate: e[0].areaCoord || '',
        };
      }

      this.$refs.tkitree._hide();
    },
    cancelArea() {
      this.$refs.tkitree._hide();
    },
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;

      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
      });

      let urlList = [];

      for (let i = 0; i < lists.length; i++) {
        this.isShowOverlay = true;
        const result = await this.uploadFilePromise(lists[i].url);

        urlList.push(result.url);

        this.isShowOverlay = false;
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            ...result,
          })
        );
        fileListLen++;
      }

      this.itemData.goodsImage = urlList.join(',');
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.uploadUrl,
          filePath: url,
          name: 'file',
          header: this.header,
          success: (res) => {
            let resData = res.data.data;
            let resType = typeof res.data;
            if (resType == 'string') {
              resData = JSON.parse(res.data).data;
            }
            setTimeout(() => {
              resolve(resData);
            }, 1000);
          },
        });
      });
    },
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    pushData() {
      this.$refs.formRef
        .validate()
        .then(async (res) => {
          if (res) {
            this.itemData.appointmentType = this.useType;
            try {
              await applyVehicleRequest(this.itemData);
              this.$refs.uToast.show(
                this.$setToastMsg({
                  message: '申请成功',
                  type: 'success',
                })
              );
            } catch (e) {
              this.$refs.uToast.show(
                this.$setToastMsg({
                  message: '申请失败',
                  type: 'success',
                })
              );
            }
          }

          //uni.navigateBack()
        })
        .catch((errors) => {
          this.$refs.uToast.show(
            this.$setToastMsg({
              message: '校验失败',
              type: 'error',
            })
          );
        });
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
::v-deep .u-form-item__content {
  align-self: center;
}

.todoListPage {
  padding-bottom: 24rpx;
}
</style>
