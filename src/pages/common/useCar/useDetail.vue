<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="itemData.taskName"
      :autoBack="autoBack"
      leftText=""
    >
      <template slot="title-right">
        <u-tag
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
          plain
          plainFill
        ></u-tag>
      </template>
    </top-navbar>
    <view class="bgCard middle-container">
      <view class="setOver">
        <uni-section type="line" title="任务信息" class="pb1 mb5">
          <uni-card>
            <uni-forms :modelValue="itemData" label-width="80">
              <uni-forms-item
                :label="item.label"
                :name="item.name"
                v-for="item in getFormItem()"
                :key="item.name"
                :label-width="item['label-width']"
              >
                <view class="text_scrolling">
                  <text v-if="item.name === 'status'">{{
                    taskStatus(itemData[item.name])
                  }}</text>

                  <text v-else-if="item.name === 'appointmentType'">{{
                    appointmentType(itemData[item.name])
                  }}</text>
                  <text
                    v-else-if="['startPoint', 'endPoint'].includes(item.name)"
                  >
                    {{ itemData[item.name] && itemData[item.name].pointName }}
                  </text>
                  <text
                    v-else-if="
                      ['applicantName', 'applicantPhone'].includes(item.name)
                    "
                  >
                    {{ itemData[item.name] || '暂无' }}
                  </text>

                  <text v-else>{{ itemData[item.name] || '暂无' }}</text>
                </view>
              </uni-forms-item>
            </uni-forms>
            <uni-row class="flex">
              <uni-col :span="10">
                <u-button
                  v-if="itemData.status == 2"
                  text="刷新线路信息"
                  type="info"
                  style="margin-bottom: 20rpx"
                  @click="infoInit"
                />
              </uni-col>
              <uni-col :span="4"> </uni-col>
              <uni-col :span="10">
                <u-button
                  v-if="itemData.status == 2"
                  text="查看公交路线详情"
                  type="primary"
                  style="margin-bottom: 20rpx"
                  @click="showBusHistoryTrack"
                />
              </uni-col>
            </uni-row>
            <!-- <u-button v-if="itemData.status==2" text="查看公交位置" type="primary" style="margin-bottom: 20rpx;" @click="" /> -->
          </uni-card>
        </uni-section>

        <uni-section
          v-if="itemData.goodsImage"
          type="line"
          title="现场照片"
          class="pb1 mb5"
        >
          <uni-card>
            <u-album
              :urls="itemData.goodsImage.split(',')"
              singleSize="100vw"
              multipleSize="25vw"
            />
          </uni-card>
        </uni-section>
        <!--         <uni-section type="line" title="作业区域及路线" class="pb1 mb5">
          <uni-card>
            <view>
              <map
                id="map"
                class="map"
                show-scale
                :enable-zoom="true"
                :show-location="true"
                scale="16"
                :latitude="latitude"
                :longitude="longitude"
              ></map>
            </view>
          </uni-card>
        </uni-section> -->
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { getStatus } from '../list_config';
import { formItems, orderFormItems } from '../workComponents/form_list';
import { getAppointmentTaskDetail } from '@/api/work/index';
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      gomapUrl: getApp().globalData.config.appInfo.gomapUrl,
      itemData: {},
      autoBack: true,
      latitude: 34.007287,
      longitude: 119.188279,
      model: '',
      infoOption: {},
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },

    taskStatus() {
      return (status) => {
        switch (Number(status)) {
          case 0:
            return '未分配';
          case 1:
            return '未开始';
          case 2:
            return '进行中';
          case 3:
            return '已结束';
          case 4:
            return '已取消';
          case 5:
            return '已失效';
        }
      };
    },
  },

  onLoad(option) {
    this.infoOption = option;
    this.infoInit(option);
  },
  onReady() {},
  methods: {
    async showBusHistoryTrack() {
      uni.navigateTo({
        url: `/pages/common/webview/index?url=${this.gomapUrl}&id=${
          this.infoOption.taskId
        }&token=${getToken()}&title=地图`,
      });
    },
    async infoInit() {
      try {
        uni.showLoading({ title: '正在获取线路信息' });
        const res = await getAppointmentTaskDetail(this.infoOption.taskId);
        uni.hideLoading();
        this.itemData = res.data;
        this.itemData.carNumber = res.data.currentDispatchCar.carNumber;
        if (Number(res.data.appointmentType) !== 3) {
          this.itemData.applicantName = res.data.appointmentUser.userName;
          this.itemData.applicantPhone = res.data.appointmentUser.phoneNumber;
        }
      } catch (e) {
        console.log(e);
      }
    },

    queryStatus(val) {
      return getStatus(val || 0);
    },

    getFormItem() {
      let res;
      if (this.infoOption.pathType === 'first') {
        res = formItems.filter((item) => {
          if (
            !item.statusMap ||
            (item.statusMap?.length &&
              item.statusMap.includes(Number(this.itemData.status)))
          ) {
            return item;
          }
        });
      } else {
        res = orderFormItems;
      }
      return res;
    },

    changeWorkerShow() {
      this.workerShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .uni-forms-item__content {
  align-self: center;
}

.setOver {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
}

.map {
  width: 100%;
  height: 400rpx;
}

.middle-container {
  padding-top: 10px;
}

.pb1 {
  padding-bottom: 1px;
}

.text_scrolling {
  white-space: nowrap;
  /* 保持文字在一行内显示 */
  overflow-x: auto;
  /* 隐藏溢出的内容 */
  // text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 80%;
}

.setbuttonstyle {
  display: flex;
  justify-content: space-around;
}
</style>
