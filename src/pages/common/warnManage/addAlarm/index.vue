<template>
  <view class="content_container topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="'新增告警'"
      :autoBack="autoBack"
      :leftText="leftText"
      @leftClick="topNavcardItemClick"
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card>
        <u--form
          labelPosition="left"
          labelWidth="85"
          :model="alarmInfo"
          :rules="rules"
          ref="uForm"
        >
          <u-form-item
            label="选择告警"
            prop="alarmName"
            required
            borderBottom
            @click="(showAlarmCheck = true), hideKeyboard()"
          >
            <u--input
              v-model="alarmInfo.alarmName"
              placeholder="请选择告警"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="选择车牌号"
            prop="carNo"
            required
            borderBottom
            @click="(showCarNoList = true), hideKeyboard()"
          >
            <u--input
              v-model="alarmInfo.carNo"
              placeholder="请选择车牌号"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
        </u--form>
        <uni-row slot="actions" class="flex">
          <uni-col :span="10">
            <u-button
              type="info"
              text="取消"
              @click="cancelInvitation"
            ></u-button>
          </uni-col>
          <uni-col :span="4"> </uni-col>
          <uni-col :span="10">
            <u-button
              type="primary"
              text="新增告警"
              @click="sendNewAlarm"
            ></u-button>
          </uni-col>
        </uni-row>
      </uni-card>
      </view>
      <u-action-sheet
      :actions="alarmList"
      :show="showAlarmCheck"
      cancelText="取消"
      @select="alarmChange"
      @close="showAlarmCheck = false"
    ></u-action-sheet>
    <u-action-sheet
      :actions="carNoList"
      :show="showCarNoList"
      cancelText="取消"
      @select="carNoChange"
      @close="showCarNoList = false"
    ></u-action-sheet>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { transitAlarmGetDetail,addAlarm,strategyList,getCarList } from "@/api/alarm/index.js";
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';


export default {
  components: {},
  data() {
    return {
      autoBack: true,
      leftText: '',
      alarmList:[],
      carNoList:[],
      alarmInfo:{
        alarmName:'',
        alarmCode:'',
        carNo:'',
        alarmTime:''
      },
      showAlarmCheck:false,
      showCarNoList:false,
      rules: {
        alarmName: {
          type: 'string',
          required: true,
          message: '请选择告警',
          trigger: ['change'],
        },
        carNo: {
          type: 'string',
          required: true,
          message: '请选择车牌号',
          trigger: ['change'],
        }
      },
    };
  },

  methods: {
    //新增告警
    sendNewAlarm() {
      this.$refs.uForm.validate().then(async (res) => {
        if (res) {
          this.alarmInfo.alarmTime = formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss');
          const rep = await addAlarm(this.alarmInfo);
          if (rep.code === 200) {
            // this.$modal.msgSuccess('邀约成功');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '新增告警成功',
                type: 'success',
              })
            );
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          } else {
            // this.$modal.msgError('邀约失败');
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '新增告警失败',
                position: 'top',
                type: 'error',
              })
            );
          }
        }
      }).catch(err => {
        console.log(err);
      });
    },
    alarmChange(obj) {
      this.alarmInfo.alarmName = obj.name;
      this.alarmInfo.alarmCode = obj.value;
    },
    carNoChange(obj) {
      this.alarmInfo.carNo = obj.value;
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    //获取告警配置
    async getAlarmList() {
      this.alarmList = [];
      const res = await strategyList();
      res?.data.forEach((item) => {
        this.alarmList.push({
          name: item.strategyName,
          value: item.strategyCode
        });
      });
    },
    //获取车牌号
    async getCarNoList() {
      this.carNoList = [];
      let query ={
        pageNum: 1,
        pageSize: 9999,
      }
      const res = await getCarList(query);
      res?.rows.forEach((item) => {
          this.carNoList.push({
            name: item.carNumber,
            value: item.carNumber
          });
        });
    },


  },

  onLoad(option) {
    this.getAlarmList()
    this.getCarNoList()
  },
  onUnload() {
    
  },
};
</script>

<style lang="scss" scoped>

::v-deep .uni-section__content-title {
  font-weight: 600;
}

.btn_container {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
}
</style>
