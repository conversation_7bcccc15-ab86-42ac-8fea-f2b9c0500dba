<template>
	<view class="warnListPage topBgBox">
		<top-navbar
			bgColor="#AA001E"
			title="告警处置"
			:autoBack="autoBack"
			:leftText="leftText"
		>
		</top-navbar>
		<view class="bgCard">
			<u-steps
				:current="currentIndex"
				activeColor="#AA001E"
				inactiveColor="#bbbbbb"
				activeIcon="checkmark-circle-fill"
				inactiveIcon="minus-circle-fill"
			>
				<u-steps-item
					v-for="(item, index) in processData.processList"
					:key="index"
					:title="item.stepName"
					:desc="item.stepDetail"
					iconSize="20"
					></u-steps-item>
			</u-steps>
			<uni-card>
				<DynamicForm
					ref="dynamicForm"
					:formItems="fields"
					v-on="$listeners"
					:disposeWay="disposeWay"
				></DynamicForm>
				<!-- <view v-for="(item, index) in processList" :key="index"> -->
					<!-- <u--form :model="itemData" label-width="110" :rules="rules" ref="formRef">
						<u-form-item :label="processList[currentIndex]" borderBottom>
								<u--input v-model="itemData.currentItemother" placeholder="请填写" disabledColor="#ffffff" border="none"></u--input>
								<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
					</u--form> -->
				<!-- </view> -->
			</uni-card>
			<!-- <view>
					<view class="flex" v-if="(currentIndex+1)!==processList.length">
						<u-button :disabled="currentIndex==0" text="上一步" @click="goBack"></u-button>
						<u-button text="下一步" @click="goNext"></u-button>
					</view>
					<u-button v-if="(currentIndex+1)===processList.length" type="primary" text="提交" @click="submit"></u-button>
			</view> -->
			<uni-row class="flex space_b setOper">
				<uni-col :span="6">
					<u-button
						:type="currentIndex == 0 ? 'info' : 'primary'"
						:text="currentIndex == 0 ? '取消' : '上一步'"
						@click="goBack"
					></u-button>
				</uni-col>
				<uni-col :span="3"></uni-col>
				<!-- <uni-col :span="6">
					<u-button
						type="error"
						text="核验不通过"
						@click="cancelCheckIn"
						v-show="checkFields.length && currentIndex == 1"
					></u-button>
				</uni-col> -->
				<uni-col :span="3"></uni-col>
				<uni-col :span="6">
					<u-button
						type="primary"
						:text="submitText"
						@click="goNext"
						v-show="commonStatus"
					></u-button>
				</uni-col>
			</uni-row>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import TopNavbar from '@/components/top-navbar/top-navbar.vue';
import DynamicForm from '../component/dynamicForm.vue';
import {
	queryAlarmListRequest,
	transitAlarmGetMyAlarm,
	alarmDisposeGetDetail,
	transitAlarmGetDetail,
	handleAlarm
} from '@/api/alarm/index.js';
import cardList from '@/pages/components/cardList.vue';
import simpleItem from '@/pages/components/simpleItem.vue';
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
	components: {
		cardList,
		simpleItem,
		TopNavbar,
		DynamicForm
	},

	data() {
		return {
			disposeWay:null,
			appointResult:{
				checkInfos:[],
				submitInfos:{}
			},
			commonStatus: true,
			filedResult:[],
			fields:[],
			currentIndex: 0,
			currentItem: 0,
			processData: {
				processList: [],
			},
			itemData: {
				currentItemother: null,
			},
			cardType: 'leftType',
			globalConfig: getApp().globalData.config,
			sectionBtn: true,
			leftText: '',
			autoBack: true,
			navBarTitle: '',
			statusRadios: [
				{
					name: '未处理',
					keyName: '0',
					badge: { value: 0 }
				},
				{
					name: '已处理',
					keyName: '1',
					badge: { value: 0 }
				},
				{
					name: '处理中',
					keyName: '2',
					badge: { value: 0 }
				}
			],
			alarmType: '',
			queryData: {
				// searchValue: '',
				deviceCode: '',
				deviceName: '',
				strategyName: '',
				disposeStatus: '0',
				pageNum: 1,
				pageSize: 10
			},
			loading: false,
			tableData: [],
			requestList: [],
			totalPage: 0,
			currTabNumber: 0,
			alarmId: '',
			rules: []
		};
	},
	computed: {
		submitText() {
      return this.currentIndex == this.processData.processList.length - 1
        ? '提交'
        : '下一步';
    },
		isChecked() {
      // 获取当前步骤字段列表
      const currentStep = this.processData.processList[this.currentIndex]?.alarmDisposeFieldList || [];
      // 判断是否存在必填字段
      return currentStep.some(field => field.isMust === '1');
    }
	},
	watch: {
		currentIndex(newValue) {
			this.currentItem = this.processData.processList[newValue];
		},
		// processList(val) {
    //   console.log(val);
    //   if (val && val.length) {
    //     this.$nextTick(() => {
    //       this.$refs.dynamicForm.getFormInfo();
    //       this.$refs.dynamicForm.getFormRules();
    //     });
    //   }
    // }
		// statusComputed() {
		// 	return (status) => {
		// 		switch (status) {
		// 			case '2':
		// 				return {
		// 					type: 'success',
		// 					label: '处理中'
		// 				};
		// 			case '1':
		// 				return {
		// 					type: 'success',
		// 					label: '已处理'
		// 				};
		// 			case '0':
		// 				return {
		// 					type: 'warning',
		// 					label: '未处理'
		// 				};
		// 		}
		// 	};
		// }
	},
	onLoad(option) {
		this.alarmId = option.alarmId
		this.getDisposeGetDetail()
	},
	methods: {
		goBack() {
			if (this.currentIndex == 0) {
        uni.navigateBack(); //取消返回来时页
      }
			this.currentIndex--;
			this.fields = this.currentIndex>=0?this.processData.processList[this.currentIndex].alarmDisposeFieldList:[]
		},
		goNext() {
			if (this.currentIndex == this.processData.processList.length - 1) {
        this.submit(); // 入园或者出园操作
				return
      }
			if (this.isChecked) {
        this.$refs.dynamicForm.validateForm((val) => {
					this.filedResult = val.filter((item) => !!item.fieldValue);
					this.appointResult.checkInfos.push(this.filedResult[0])
          // this.appointResult.checkInfos = this.filedResult.reduce((pre, next) => {
          //   pre.push({
          //     fieldCode: next.fieldCode,
          //     fieldValue: next.fieldValue,
          //   });
          //   return pre;
          // }, []);
          this.currentIndex++;
					// this.$set(this,'fields',this.processData.processList[this.currentIndex].alarmDisposeFieldList)
					this.fields = this.processData.processList[this.currentIndex].alarmDisposeFieldList
					this.$nextTick(() => {
            this.$refs.dynamicForm.getFormInfo();
            this.$refs.dynamicForm.getFormRules();
          });
					console.log(this.$refs.dynamicForm)
        });
        return;
      }
			this.currentIndex = this.currentIndex + 1;
			this.fields = this.processData.processList[this.currentIndex].alarmDisposeFieldList
			this.$nextTick(() => {
				this.$refs.dynamicForm.getFormInfo();
				this.$refs.dynamicForm.getFormRules();
			});
		},
		submit() {
			this.$refs.dynamicForm.validateForm(async val => { 
					this.filedResult = val.filter((item) => !!item.fieldValue);
					this.appointResult.checkInfos.push(this.filedResult[0])
					this.appointResult.submitInfos = {}
          this.appointResult.submitInfos.alarmDisposeList = []
					this.appointResult.submitInfos.alarmId = this.alarmId
					this.appointResult.submitInfos.disposeWay = 3
					this.processData.processList.forEach((item,index)=>{
						this.appointResult.submitInfos.alarmDisposeList.push({'alarmDisposeInfoList':item.alarmDisposeFieldList})
					})
					let res = await handleAlarm(this.appointResult.submitInfos)
					if(res.code == 200) {
								 this.$refs.uToast.show(this.$setToastMsg({
									message:'处理完成',
									type:'success'
								}));
								uni.navigateBack({
									delta:2
								});
					}
        });
				
			
			
			// uni.navigateTo({
			// 	url: `/pages/common/warnManage/warnList/index?title=告警记录&type=1`
			// });
		},
		// statusSelect(value) {
		// 	this.currentIndex = value.index;
		// 	this.queryData.disposeStatus = value.keyName;
		// 	this.searchHandle();
		// },

		// // 告警列表
		// async getTableData() {
		// 	try {
		// 		this.loading = true;
		// 		let res
		// 		if (this.navBarTitle === "我的告警") {
		// 			res = await transitAlarmGetMyAlarm(this.queryData);
		// 		} else {
		// 			res = await queryAlarmListRequest(this.queryData);
		// 		};
		// 		this.totalPage = Math.ceil(res.total / this.queryData.pageSize);
		// 		this.currTabNumber= res.total;
		// 		if (res.rows.length) {
		// 			this.requestList.push(...res.rows);
		// 			const newArr = res.rows.map((item) => {
		// 				return {
		// 					alarmId: item.alarmId,
		// 					name: item.strategyName,
		// 					subName: formatDate(item.alarmTime, 'yyyy/MM/dd hh:mm'),
		// 					fullName: item.areaName,
		// 					status: this.statusComputed(item.disposeStatus)
		// 				};
		// 			});
		// 			this.tableData.push(...newArr);
		// 			this.getDotNumber();
		// 		}
		// 	} catch (err) {
		// 		console.log(err);
		// 	}
		// 	this.loading = false;
		// },
		// 处置列表
		async getDisposeGetDetail() {
			const res = await transitAlarmGetDetail(this.alarmId);
			this.processData.processList = [];
			this.disposeWay = res.data.disposeWay
			//按照stepOrder重新排序，确保index与stepOrder对应
			this.processData.processList = res.data.alarmDisposeList.sort((a, b) => {
        return parseInt(a.stepOrder) - parseInt(b.stepOrder);
      });
			this.fields = this.processData.processList[this.currentIndex].alarmDisposeFieldList
			this.$nextTick(() => {
				this.$refs.dynamicForm.getFormInfo();
				this.$refs.dynamicForm.getFormRules();
			})
			
			
		},
		// 上滑刷新
		// scrolltolower() {
		// 	this.queryData.pageNum++;
		// 	if (this.queryData.pageNum > this.totalPage) {
		// 		return this.$refs.uToast.show(this.$setToastMsg({
		// 			message:'已加载全部',
		// 			type:'success'
		// 		}));
		// 	}
		// 	this.getTableData();
		// },
		// getDotNumber() {
		// 	this.statusRadios.forEach((e, index) => {
		// 		if (this.currentIndex === index) {
		// 			e.badge.value = this.currTabNumber;
		// 		};
		// 	});
		// },
		//搜索
		// searchHandle(value = '') {
		// 	this.tableData = [];
		// 	this.requestList = [];
		// 	this.queryData.pageNum = 1;
		// 	this.getTableData();
		// },

		// clearHandle() {
		// 	this.queryData.searchValue = '';
		// 	this.searchHandle();
		// },
		// filterHandle() {
		// 	this.$refs.filterRef.open();
		// },
		// filterSearch(info) {
		// 	this.queryData = {...this.queryData, ...info }
		// 	this.searchHandle();
		// },
		//查看详情
		// cardItemClick(item) {
		// 	uni.navigateTo({
		// 		url: `/pages/common/warnManage/warnDetail/index?alarmId=${item.alarmId}&title=${this.navBarTitle}`
		// 	});
		// }
	},
};
</script>

<style lang="scss" scoped>
.uni-card {
	height: 75%;
}
::v-deep .u-steps-item__wrapper {
	background: #f7f7f7;
}
::v-deep .u-steps-item__content {
	.u-text {
		justify-content: center !important;
	}
}
</style>
