<template>
  <view>
    <u--form
      labelPosition="left"
      labelWidth="40%"
      :model="uFormModel"
      ref="uForm"
    >
      <u-form-item
        v-for="item in formItems"
        :key="item.fieldId"
        :label="item.fieldName"
        :prop="item.fieldCode"
      >
      <view v-if="item.fieldType == 'checkbox'">{{ JSON.parse(item.fieldValue).toString() }}</view>
      <view v-else-if="item.fieldType == 'upload'">
        <u-album :urls="JSON.parse(item.fieldValue)"></u-album>
      </view>
      <view v-else>{{ item.fieldValue }}</view>
      </u-form-item>
    </u--form>
  </view>
</template>

<script>
export default {
  name: 'DynamicForm',
  components: {
  },
  props: {
    formItems: {
      type: Array,
      default: () => [],
    },
    disposeWay: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      uFormModel: {},
    };
  },
  onLoad() {},
  watch:{
  },
  methods: {
    // 表单项
    getFormInfo() {
      this.formItems.forEach((element) => {
        this.uFormModel[element.fieldCode] = element.fieldValue;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
