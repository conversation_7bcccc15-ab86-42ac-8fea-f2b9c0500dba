// 引入各种表单组件
import TextField from './formFields/textField.vue';
import RadioField from './formFields/radioField.vue';
import UploadField from './formFields/uploadField.vue';
import ConfirmField from './formFields/confirmField.vue';
import CheckboxField from './formFields/checkboxField.vue';
import DatetimepickerField from './formFields/datetimepickerField.vue';
import DatepickerField from './formFields/datepickerField.vue';

export default {
  getFieldComponent(type) {
    const fieldsMap = {
      'input': TextField,
      'radio': RadioField,
      'upload': UploadField,
      'confirmationbox': ConfirmField,
      'checkbox': CheckboxField,
      'datetimepicker': DatetimepickerField,
      'datepicker': DatepickerField,
      // 如果有更多类型，可以在此继续添加
    };
    return fieldsMap[type] || TextField; // 如果没有匹配的类型，返回 null 或者一个默认的组件
  }
};

const rulesMap = {
  'input': {
    type: 'string',
    required: true,
    message: '请填写',
    trigger: ['blur', 'change']
  },
  'radio': {
    required: true,
    message: '请选择',
    trigger: ['blur', 'change']
  },
  'upload': {
    validator: (rule, value, callback) => {
      if (rule.required) {
        // uploadField组件将绑定的urlList使用了JSON.stringfy
        if (value) {
          return JSON.parse(value).length > 0
        } else {
          return false
        }
      } else {
          return
      }
    },
    message: '请上传照片'
  },
  'confirmationbox': {
    required: true,
    message: '请选择',
    trigger: ['blur', 'change']
  },
  'checkbox': {
    validator: (rule, value, callback) => {
      if (rule.required) {
        // checkboxField组件将绑定的urlList使用了JSON.stringfy
        if (value) {
          return JSON.parse(value).length > 0
        } else {
          return false
        }
      } else {
          return
      }
    },
    message: '请选择'
  },
  'datetimepicker': {
    required: true,
    message: '请选择',
    trigger: ['change']
  },
  'datepicker': {
    required: true,
    message: '请选择',
    trigger: ['change']
  },
};
// export const getFieldRule = (info) => {
//   console.log(info); 
//   if(rulesMap[info.fieldType]) {
//     if(info.fieldRemark) {
//       rulesMap[info.fieldType].message = info.fieldRemark;
//     }
//     rulesMap[info.fieldType].required = info.isMust === '1';
//     return rulesMap[info.fieldType];
//   }
// }
export const getFieldRule = (info) => {
  // 添加参数校验和空值保护
  if (!info || typeof info !== 'object' || !rulesMap[info.fieldType]) { 
    // console.warn('Invalid field config:', info); 
    return { required: false }; // 返回兜底规则 
  } 

  // 创建规则副本避免污染原始规则对象
  const rule = { ...rulesMap[info.fieldType] }; 
  
  if (info.fieldRemark) {
    rule.message = info.fieldRemark; 
  }
  rule.required = info.isMust === '1'; 
  
  return rule; 
}