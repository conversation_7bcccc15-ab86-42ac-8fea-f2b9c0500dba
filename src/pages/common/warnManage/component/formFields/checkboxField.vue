<template>
  <zxz-uni-data-select
    v-model="modelValue"
    :localdata="radiolistCop"
    multiple
    collapse-tags
    :collapse-tags-num="2"
    :clear="false"
    class="custom-zxz-select"
    :disabled="comDisabled"
  />
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
    comDisabled:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modelValue: [],
      radiolist: ['是', '否'],
      radiolistCop: []
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      let array = [];
      newValue.forEach(element => {
        array.push(this.radiolist[element]);
      });
      this.$emit(
        'update:modelValue',
        JSON.stringify(array),
        this.fieldProps.fieldCode
      );
    },
    'fieldProps':{
      handler(newValue){
        if (this.fieldProps.selectOption) {
          let list = this.fieldProps.selectOption.split(',');
          this.radiolist = list;
          this.radiolistCop = list.map((item, index) => {
            let obj = {
              value: index,
              text: item
            }
            return obj
          });
        }
        if(newValue.fieldValue) {
          this.radiolistCop.forEach(item=>{
            JSON.parse(newValue.fieldValue).forEach(element => {
              if(item.text == element){
                this.modelValue.push(item.value)
              }
            });
          })
        }
      },
      immediate: true
    }
  },
  created() {
    // if (this.fieldProps.selectOption) {
    //   let list = this.fieldProps.selectOption.split(',');
    //   this.radiolist = list;
    //   this.radiolistCop = list.map((item, index) => {
    //     let obj = {
    //       value: index,
    //       text: item
    //     }
    //     return obj
    //   });
    // }
  },
};
</script>

<style lang="scss" scoped>
</style>
