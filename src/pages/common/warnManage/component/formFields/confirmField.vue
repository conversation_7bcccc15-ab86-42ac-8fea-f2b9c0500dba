<template>
    <u-radio-group
      v-model="modelValue"
      placement="row"
      iconPlacement="right"
    >
      <u-radio
        v-for="(item, index) in radiolist"
        :key="item"
        :label="item"
        :name="item"
        :class="{'is-checked': modelValue===item}"
      >
        <u-button
          :type="modelValue===item?'primary':'info'"
          :text="item"
        />
      </u-radio>
    </u-radio-group>
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {}
    },
  },
  data() {
		return {
      modelValue: '',
      radiolist: [ '是', '否' ]
    }
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      this.$emit('update:modelValue', newValue, this.fieldProps.fieldCode);
    },
    'fieldProps':{
      handler(newValue){
        if (this.fieldProps.selectOption) {
          let list = this.fieldProps.selectOption.split(',');
          this.radiolist = list;
        }
        if(newValue.fieldValue) {
          this.modelValue = newValue.fieldValue;
        }
      },
      immediate: true
    }
  },
  created() {
    // if (this.fieldProps.selectOption) {
    //   let list = this.fieldProps.selectOption.split(',');
    //   this.radiolist = list;
    // }
  },
};
</script>

<style lang="scss" scoped>
.u-button {
  width: 70px;
}
::v-deep .u-radio__icon-wrap--circle {
  display: none;
}
.u-radio-group--row {
  justify-content: flex-end;
}
</style>