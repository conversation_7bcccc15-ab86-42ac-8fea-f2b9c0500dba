<template>
  <view style="width: 100%">
    <u--input
      :placeholder="fieldProps.fieldRemark"
      maxlength="50"
      v-model="modelValue"
      @focus="show = true"
    >
    </u--input>
    <u-datetime-picker
      :show="show"
      mode="date"
      v-model="initValue"
      @cancel="show = false"
      @confirm="dateConfirm"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      modelValue: '',
      show: false,
      initValue: Number(new Date()),
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      this.$emit('update:modelValue', newValue, this.fieldProps.fieldCode);
    },
    'fieldProps':{
      handler(newValue){
        if(newValue.fieldValue) {
          this.modelValue = newValue.fieldValue;
          this.initValue = newValue.fieldValue;
        }
      },
      immediate: true
    }
  },
  methods: {
    dateConfirm(e) {
      this.modelValue = formatDate(e.value, 'yyyy-MM-dd');
      this.show = false;
    },
  },
};
</script>
