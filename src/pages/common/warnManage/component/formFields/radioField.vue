<template>
  <zxz-uni-data-select
    v-model="modelValue"
    :localdata="radiolistCop"
    :clear="false"
    :disabled="comDisabled"
  />
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
    comDisabled:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modelValue: '',
      radiolist: ['是', '否'],
      radiolistCop: []
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      this.$emit('update:modelValue', this.radiolist[newValue], this.fieldProps.fieldCode);
    },
    'fieldProps':{
      handler(newValue){
        if (this.fieldProps.selectOption) {
          let list = this.fieldProps.selectOption.split(',');
          this.radiolist = list;
          this.radiolistCop = list.map((item, index) => {
            let obj = {
              value: index,
              text: item
            }
            return obj
          });
        }
        // if(newValue.fieldValue) {
          this.radiolistCop.forEach(item=>{
            if(item.text == newValue.fieldValue){
              this.modelValue = item.value
            }
          })
        // }
      },
      immediate: true
    }
  },
  created() {
    // if (this.fieldProps.selectOption) {
    //   let list = this.fieldProps.selectOption.split(',');
    //   this.radiolist = list;
    //   this.radiolistCop = list.map((item, index) => {
    //     let obj = {
    //       value: index,
    //       text: item
    //     }
    //     return obj
    //   });
    // }
  },
  methods: {
    
  }
};
</script>

<style lang="scss" scoped>
.u-radio {
  margin-right: 10px;
}
</style>
