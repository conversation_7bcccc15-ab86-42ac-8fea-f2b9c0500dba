<template>
   <u--input
      :placeholder="fieldProps.fieldRemark"
      maxlength="50"
      v-model="fieldProps.fieldValue"
      :disabled="comDisabled"
    >
  </u--input>
</template>

<script>
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {}
    },
    comDisabled:{
      type: Boolean,
      default: false
    }
  },
  data() {
		return {
      modelValue: ''
    }
  },
  emits: ['update:modelValue'],
  watch: {
    'fieldProps.fieldValue'(newValue) {
      this.$emit('update:modelValue', newValue, this.fieldProps.fieldCode);
    }
  },
};
</script>