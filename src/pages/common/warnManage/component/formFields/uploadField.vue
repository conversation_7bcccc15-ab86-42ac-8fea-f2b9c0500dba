<template>
  <u-upload
    :fileList="modelValue"
    :maxCount="5"
    :previewFullImage="false"
    name="1"
    @delete="deletePic"
    @afterRead="afterRead"
  ></u-upload>
</template>

<script>
import config from '@/config.js';
import { getToken } from '@/utils/auth.js';
export default {
  props: {
    fieldProps: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      header: {
        Authorization: 'Bearer ' + getToken(),
      },
      uploadUrl: config.baseUrl + '/system/oss/upload',
      modelValue: [],
      radiolist: [{ name: '是' }, { name: '否' }],
    };
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(newValue) {
      let urlList = newValue.map((item) => item?.url);
      let transUrlList = JSON.stringify(urlList);
      this.$emit('update:modelValue', transUrlList, this.fieldProps.fieldCode);
    },
    'fieldProps':{
      handler(newValue){
        if(newValue.fieldValue) {
          let urlArr = JSON.parse(newValue.fieldValue)
          if(urlArr.length > 0) {
            urlArr.forEach(item=>{
              this.modelValue.push({url:item})
            })
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 删除图片
    deletePic(event) {
      this[`modelValue`].splice(event.index, 1);
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`modelValue`].length;
      lists.map((item) => {
        this[`modelValue`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url);

        console.log(result);
        let item = this[`modelValue`][fileListLen];
        this[`modelValue`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result.url,
          })
        );
        fileListLen++;
      }
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.uploadUrl,
          filePath: url,
          name: 'file',
          header: this.header,
          success: (res) => {
            let resData = res.data.data;
            let resType = typeof res.data;
            if (resType == 'string') {
              resData = JSON.parse(res.data).data;
            }
            setTimeout(() => {
              resolve(resData);
            }, 1000);
          },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.u-radio {
  margin-right: 10px;
}
</style>
