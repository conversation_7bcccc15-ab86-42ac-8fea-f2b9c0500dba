<template>
  <view class="">
    <u-popup :show="popupShow" style="" @close="close" @open="open">
      <!-- <u--form labelPosition="top" ref="uForm" :model="dealForm" :rules="rules" labelWidth="100vw" style="padding: 0.625rem"> -->
      <!-- <u-form-item label="告警研判" prop="alarmVerdict">
					<uni-data-select v-model="dealForm.alarmVerdict" :localdata="judgeList" />
				</u-form-item>
				<u-form-item label="处理描述" prop="disposeExplain">
					<u--textarea v-model="dealForm.disposeExplain" placeholder="请输入内容" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item> -->

      <!-- <u-form-item> -->
      <u-button
        text="误判"
        type="info"
        :round="false"
        :loading="loading"
        loadingText="请稍后"
        @click="ignore(1)"
      />
      <u-button
        text="忽略"
        :round="false"
        :loading="loading"
        loadingText="请稍后"
        @click="ignore(2)"
      />
      <u-button
        type="primary"
        text="去处理"
        :loading="loading"
        loadingText="请稍后"
        @click="submitHandle"
      />
      <!-- </u-form-item> -->
      <!-- </u--form> -->
    </u-popup>
    <u-popup :show="ignorePopupShow" style="" @close="ignoreClose" @open="ignoreOpen" :closeOnClickOverlay="false">
      <uni-card>
      <u--form
          :model="itemData"
          label-width="80"
          :rules="rules"
          ref="formRef"
        >
        <u-form-item label="忽略原因" prop="ignoreReason">
            <u--textarea
              v-model="itemData.ignoreReason"
              placeholder="请输入忽略原因"
            ></u--textarea>
          </u-form-item>
          <u-form-item label="现场照片" prop="ignorePicture" borderBottom>
            <u-upload
              :fileList="fileList1"
              @afterRead="afterRead"
              @delete="deletePic"
              name="1"
              :multiple="true"
              :maxCount="1"
              uploadText="上传图片"
            />
          </u-form-item>
        </u--form>
        <view slot="actions" class="flex">
          <u-button
            text="取消"
            @click="cancelIgnore"
          ></u-button>
          <u-button
            type="primary"
            text="提交"
            @click="comitIgnore"
          ></u-button>
        </view>
      </uni-card>
    </u-popup>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { transitAlarmDisposeAlarm, handleAlarm } from "@/api/alarm/index.js";
import config from '@/config.js';
import { getToken } from '@/utils/auth.js';
import { deepClone } from '@/utils/common';

export default {
  props: {
    judgeList: {
      type: Array,
      default: () => [],
    },
    handleWay: {
      type: Array,
      default: () => [],
    },
    warnInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      fileList1: [],
      header: {
        Authorization: 'Bearer ' + getToken(),
      },
      uploadUrl: config.baseUrl + '/system/oss/upload',
      itemData:{
        ignoreReason:null,
        ignorePicture:''
      },
      ignorePopupShow:false,
      popupShow: false,
      loading: false,
      dealForm: {
        alarmVerdict: "",
        disposeExplain: "",
      },
      rules: {
        ignoreReason: {
          type: "string",
          required: true,
          trigger: ["blur", "change"],
          message: "请填写忽略原因",
        }
      },
    };
  },
  methods: {
    cancelIgnore() {
      this.ignoreClose()
      this.itemData = {
      	ignoreReason: '',
      	ignorePicture: ''
      };
      this.$refs.formRef.clearValidate();
    },
    comitIgnore() {
      this.$refs.formRef.validate().then(async(res) => {
            if (res) {
              let params = deepClone(this.itemData);
              let params1 = this.fileList1.length > 0 ? this.fileList1.map((item) => item.ossId) : [];
              params.ignorePicture = [...params1].toString();
              params.alarmId = this.warnInfo.alarmId;
              params.disposeWay = 2;
              const rep = await handleAlarm(params);
              if (rep.code === 200) {
                this.$refs.uToast.show(this.$setToastMsg({
                  message:'处理完成',
                  type:'success'
                }));
                setTimeout(() => {
                  uni.$emit('refreshList')
                  uni.navigateBack();
                }, 1000);
              } else {
                this.$refs.uToast.show(this.$setToastMsg({
                  message:'处理失败',
                  position: 'top',
                  type:'error'
                }));
              }
            }
          }).catch((err) => {
          console.log(err);
        });
    },
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
      });


      for (let i = 0; i < lists.length; i++) {
        this.isShowOverlay = true;
        const result = await this.uploadFilePromise(lists[i].url);


        this.isShowOverlay = false;
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            ...result,
          })
        );
        fileListLen++;
      }
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.uploadUrl,
          filePath: url,
          name: 'file',
          header: this.header,
          success: (res) => {
            let resData = res.data.data;
            let resType = typeof res.data;
            if (resType == 'string') {
              resData = JSON.parse(res.data).data;
            }
            setTimeout(() => {
              resolve(resData);
            }, 1000);
          },
        });
      });
    },
    ignore(type) {
      if (type == 1) {
        this.$modal.confirm("确定该告警是误判吗？").then(async () => {
          
          let params = {
            alarmId: this.warnInfo.alarmId,
            disposeWay: type,
          };
          let res = await handleAlarm(params);
          if (res.code == 200) {
            this.$refs.uToast.show(this.$setToastMsg({
							message: "处理完成",
              type: "success",
              })
            );
            uni.$emit('refreshList')
            uni.navigateBack();
          }
        });
      } else if(type == 2) {
        this.ignoreOpen();
      }
			this.close();
    },
    submitHandle() {
      uni.navigateTo({
        url: `/pages/common/warnManage/alarmHandling/index?alarmId=${this.warnInfo.alarmId}`,
      });
      this.close();
      
    },
    async open() {
      this.popupShow = true;
    },
    close() {
      this.popupShow = false;
      // this.dealForm = {
      // 	alarmVerdict: '',
      // 	disposeExplain: ''
      // };
      // this.$refs.uForm.clearValidate();
    },
    ignoreOpen() {
      this.ignorePopupShow = true;
    },
    ignoreClose() {
      this.ignorePopupShow = false;
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .handleWay_container {
  width: 100vw;
  .u-tag {
    display: inline-block;
    text-align: center;

    .u-tag__text {
      font-size: 0.875rem;
    }
  }
}
</style>
