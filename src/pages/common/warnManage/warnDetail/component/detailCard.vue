<template>
  <uni-card
    :is-shadow="true"
    shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
    spacing="12px"
    margin="12px 12px 0"
  >
    <template v-slot:title>
      <uni-section type="line" :title="cardTitle" class="pb1 mb8"></uni-section>
    </template>

    <u-cell v-for="(item, key) in detailLabel" :key="item">
      <view slot="label">
        <u-row>
          <u-col :span="4" style="color: #666">{{ item }}</u-col>
          <u-col
            :span="8"
            v-if="key == 'noticeInfoType' && formData[key] == 'notice'"
          >
            <rich-text :nodes="formData[key]"></rich-text>
          </u-col>
          <u-col :span="8" v-else>{{ formData[key] }}</u-col>
        </u-row>
      </view>
    </u-cell>
  </uni-card>
</template>

<script>
export default {
  props: {
    cardTitle: {
      type: [String],
      default: '',
    },
    detailLabel: {
      type: [Object],
      default: () => {},
    },
    formData: {
      type: [Object],
      default: () => {},
    },
  },

  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.u-col {
  font-size: $uni-font-size-base;
}
</style>
