<template>
  <view class="warnListPage topBgBox">
    <view>
      <!-- <u-steps
				:current="currentIndex"
				activeColor="#AA001E"
				inactiveColor="#bbbbbb"
				activeIcon="checkmark-circle-fill"
				inactiveIcon="minus-circle-fill"
			>
				<u-steps-item
					v-for="(item, index) in processData.processList"
					:key="index"
					:title="item.stepName"
					:desc="item.stepDetail"
					iconSize="20"
					></u-steps-item>
			</u-steps> -->
      <uni-list :border="false">
        <uni-list-item
          v-for="(item, index) in processData.processList"
          :key="index"
          :border="false"
        >
          <template v-slot:header>
            <view
              class="slot-box flex space_c align_center"
              style="margin-right: 10px"
            >
              <view class="stepStyle flex space_c align_center font-14 mr4">{{
                index + 1
              }}</view>
              <view style="position: relative">
                <view class="step" style="background: #999"></view>
                <view class="stepBefore"></view>
              </view>
            </view>
          </template>
          <template v-slot:body>
            <view class="list_item_body">
              <view class="flex space_b">
                <view class="font-14">{{
                  item.stepName ? item.stepName : '暂无'
                }}</view>
                <view
                  class="font-12"
                  style="color: #aa001e"
                  @click="checkFilds(index)"
                  v-show="item.alarmDisposeInfoList"
                  >查看处置项</view
                >
              </view>

              <view class="font-12 text-grey1 overBreak">
                {{ item.stepDetail || '' }}
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>

      <!-- <uni-card>
        <DynamicDetailForm
          ref="dynamicForm"
          :formItems="fields"
          v-on="$listeners"
        ></DynamicDetailForm>
      </uni-card> -->

      <uni-row class="flex space_b setOper" style="display: none">
        <uni-col :span="6">
          <u-button type="info" text="取消" @click="goBack"></u-button>
        </uni-col>
        <uni-col :span="3"></uni-col>

        <uni-col :span="3"></uni-col>
        <uni-col :span="6">
          <u-button
            v-if="false"
            type="primary"
            text="提交"
            @click="goNext"
          ></u-button>
        </uni-col>
      </uni-row>
    </view>

    <u-popup
      :show="lookFiled"
      :closeable="true"
      @close="lookFiled = false"
      :round="10"
    >
      <view class="popup_body">
        <view class="popTitle"> 处置项</view>
        <view class="popInfo">
          <u--form labelWidth="40%" ref="uForm">
            <u-form-item
              v-for="item in fields"
              :key="item.fieldId"
              :label="item.fieldName"
              :prop="item.fieldCode"
            >
              <view v-if="item.fieldType == 'checkbox'">{{
                JSON.parse(item.fieldValue).toString()
              }}</view>
              <view v-else-if="item.fieldType == 'upload'">
                <u-album :urls="JSON.parse(item.fieldValue)"></u-album>
              </view>
              <view v-else>{{ item.fieldValue }}</view>
            </u-form-item>
          </u--form>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import DynamicDetailForm from '../../component/dynamicDetailForm.vue';

import cardList from '@/pages/components/cardList.vue';
import simpleItem from '@/pages/components/simpleItem.vue';

export default {
  components: {
    cardList,
    simpleItem,
    DynamicDetailForm,
  },
  props: {
    stepData: {
      type: Array,
      default: () => [],
    },
    originData: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      appointResult: {
        checkInfos: [],
        submitInfos: {},
      },
      lookFiled: false,
      filedResult: [],
      fields: [],
      currentIndex: 0,
      currentItem: 0,
      processData: {
        processList: [],
      },
      itemData: {
        currentItemother: null,
      },
      cardType: 'leftType',
      globalConfig: getApp().globalData.config,
      sectionBtn: true,
      leftText: '',
      autoBack: true,
      navBarTitle: '',
      statusRadios: [
        {
          name: '未处理',
          keyName: '0',
          badge: { value: 0 },
        },
        {
          name: '已处理',
          keyName: '1',
          badge: { value: 0 },
        },
        {
          name: '处理中',
          keyName: '2',
          badge: { value: 0 },
        },
      ],
      alarmType: '',
      queryData: {
        // searchValue: '',
        deviceCode: '',
        deviceName: '',
        strategyName: '',
        disposeStatus: '0',
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData: [],
      requestList: [],
      totalPage: 0,
      currTabNumber: 0,
      alarmId: '',
      rules: [],
    };
  },
  computed: {
    showSubmitText() {
      return this.originData.disposeWay !== '3';
    },
  },
  watch: {
    currentIndex(newValue) {
      this.currentItem = this.processData.processList[newValue];
    },
  },
  onLoad(option) {},
  watch: {},
  methods: {
    goBack() {
      uni.navigateBack(); //取消返回来时页
    },
    checkFilds(val) {
      this.lookFiled = true;
      this.fields =
        this.processData.processList[this.currentIndex].alarmDisposeInfoList;
    },

    // 处置列表
    getDisposeGetDetail() {
      this.processData.processList = [];
      //按照stepOrder重新排序，确保index与stepOrder对应
      this.processData.processList = this.stepData.sort((a, b) => {
        return parseInt(a.stepOrder) - parseInt(b.stepOrder);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-card {
  height: 75%;
}
::v-deep .u-form-item__body__left__content__label {
  color: #666;
}
.stepStyle {
  width: 48rpx;
  height: 48rpx;
  border-radius: 100px;
  border: 1px solid $uni-color-primary;
  color: $uni-color-primary;
}
.list_item_body {
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #f7f6f6;
  flex-grow: 1;
}
.step {
  height: 20rpx;
  width: 20rpx;
  position: relative;
  border-radius: 100px;
}
.stepBefore {
  width: 2px;
  height: 40rpx;
  background-color: #999;
  margin-left: 10rpx;
  transform: translateX(-50%);
}
.popup_body {
  padding: 80rpx 48rpx 20rpx;

  position: relative;
  .popTitle {
    color: #aa001e;
    position: absolute;
    top: 30rpx;
    left: 30rpx;
    font-size: $uni-font-size-lg;
  }
  .popInfo {
    color: $uni-text-color;
  }
}
</style>
