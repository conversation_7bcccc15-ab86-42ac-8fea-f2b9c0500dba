<template>
  <view class="content_container topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="'告警详情'"
      :autoBack="autoBack"
      :leftText="leftText"
      @leftClick="topNavcardItemClick"
    ></top-navbar>
    <view class="bgCard">
      <detail-card
        cardTitle="告警信息"
        :detailLabel="alarmDetailLabel"
        :formData="alarmDetail"
      ></detail-card>

      <!-- <detail-card cardTitle="设备信息" :detailLabel="deviceDetailLabel" :formData="deviceDetail"></detail-card> -->

      <uni-card
        v-if="formData.disposeStatus === '1'"
        :is-shadow="true"
        shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <template v-slot:title>
          <uni-section
            type="line"
            title="处理详情"
            class="pb1 mb8"
          ></uni-section>
        </template>

        <u-cell>
          <view slot="label">
            <u-row>
              <u-col :span="4" style="color: #666">告警研判</u-col>
              <u-col v-if="formData.disposeWay" :span="8">{{
                typeof formData.disposeWay == 'number'
                  ? judgeList[formData.disposeWay - 1].label
                  : '暂无'
              }}</u-col>
              <u-col v-else :span="8">暂无</u-col>
            </u-row>
          </view>
        </u-cell>
        <u-cell>
          <view slot="label">
            <u-row>
              <u-col :span="4" style="color: #666">处置时间</u-col>
              <u-col :span="8">{{ formData.verdictTime || '暂无' }}</u-col>
            </u-row>
          </view>
        </u-cell>
        <u-cell v-if="formData.disposeWay == '2'">
          <view slot="label">
            <u-row>
              <u-col :span="4" style="color: #666">忽略原因</u-col>
              <u-col :span="8">{{ formData.ignoreReason || '暂无' }}</u-col>
            </u-row>
          </view>
        </u-cell>
        <u-cell v-if="formData.disposeWay == '2'">
          <view slot="label">
            <u-row>
              <u-col :span="4" style="color: #666">现场照片</u-col>
              <u-col :span="8" style="" class="imgCol">
                <image
                  :src="formData.sysOssVo ? formData.sysOssVo.url : ''"
                  mode="widthFix"
                  @click="
                    openPicPreview(
                      formData.sysOssVo ? formData.sysOssVo.url : ''
                    )
                  "
                ></image>
              </u-col>
            </u-row>
          </view>
        </u-cell>
        <u-cell>
          <view slot="label">
            <u-row>
              <u-col :span="4" style="color: #666">处理描述</u-col>
              <u-col :span="8">
                <div class="deal-describe">
                  <span>{{ formData.disposeExplain || '无' }}</span>
                </div>
              </u-col>
            </u-row>
          </view>
        </u-cell>
      </uni-card>
      <uni-card
        v-if="formData.disposeStatus === '1' && formData.disposeWay == '3'"
        :is-shadow="true"
        shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <template v-slot:title>
          <uni-section
            type="line"
            title="步骤详情"
            class="pb1 mb8"
          ></uni-section>
        </template>

        <step-detail
          ref="stepsDetail"
          :stepData="formData.alarmDisposeList"
          :originData="formData"
        ></step-detail>
      </uni-card>
      <view
        v-if="
          formData.disposeStatus === '0' && routeOption.name === 'WarnRecode'
        "
        class="btn_box"
      >
        <u-button type="primary" @click="dealHandle">研判 & 处理</u-button>
      </view>
    </view>

    <u-loading-page
      :loading="loading"
      loadingText="请稍后"
      bg-color="rgba(0, 0, 0, 0.3)"
    />

    <deal-popup
      ref="dealRef"
      :warnInfo="formData"
      :judgeList="judgeList"
      @getAlarmInfo="getAlarmInfo"
    />
  </view>
</template>

<script>
import { transitAlarmGetDetail } from '@/api/alarm/index.js';
import { getDictionaryQueries } from '@/api/login.js';
import detailCard from './component/detailCard.vue';
import dealPopup from './component/dealPopup.vue';
import stepDetail from './component/stepDetail.vue';

let categoryType = {
  0: 'device_repair',
  2: 'hidden_trouble',
  3: 'hidden_trouble',
};

export default {
  components: {
    detailCard,
    dealPopup,
    stepDetail,
  },

  data() {
    return {
      globalConfig: getApp().globalData.config,
      leftText: '',
      autoBack: true,
      loading: false,
      formData: {
        rule: {
          alarmDisposalMethod: [],
        },
      },
      productKeys: [],
      alarmLevels: {
        // 0: '严重',
        // 1: '警告'
      },
      judgeList: [
        {
          label: '误判',
          text: '误判',
          value: '1',
        },
        {
          label: '忽略',
          text: '忽略',
          value: '2',
        },
        {
          label: '真实告警',
          text: '真实告警',
          value: '3',
        },
      ],
      runningStatus: {
        0: '运行',
        1: '停机离线',
        2: '计划停机',
        2: '故障停机',
      },
      alarmDetailLabel: {
        strategyName: '告警事件',
        // areaName: "所属区域",
        alarmTime: '告警时间',
        alarmLevelName: '告警等级',
        // alarmContent: "当前信号值",
      },
      alarmDetail: {
        strategyName: '',
        areaName: '',
        alarmTime: '',
        alarmLevelName: '',
        alarmContent: '',
      },
      deviceDetailLabel: {
        deviceName: '设备名称',
        deviceCode: '设备编码',
        deviceTypeName: '设备类型',
        runningStatusName: '设备状态',
        deviceFactory: '厂家',
        deviceSafePerson: '安全负责人',
        remark: '备注',
      },
      deviceDetail: {
        deviceName: '',
        deviceCode: '',
        deviceTypeName: '',
        runningStatusName: '',
        deviceFactory: '',
        deviceSafePerson: '',
        remark: '',
      },
      routeOption: {},
    };
  },
  onUnload() {
    
    this.$closePreview();
  },
  methods: {
    /* 打开图片组件*/
    openPicPreview(img) {
      this.$openPreview(img);
    },
    //告警详情
    async getAlarmInfo() {
      this.loading = true;
      try {
        const res = await transitAlarmGetDetail(this.routeOption.alarmId);
        this.formData = res.data;
        this.getCardInfo();
      } catch (err) {
        console.log(err);
      }
      this.loading = false;
    },

    getCardInfo() {
      this.alarmDetail = { ...this.formData };
      this.deviceDetail = { ...this.formData };
      if (!this.alarmDetail.alarmContent)
        this.alarmDetail.alarmContent = '暂无';
      if (!this.deviceDetail.deviceFactory)
        this.deviceDetail.deviceFactory = '暂无';
      if (!this.deviceDetail.deviceSafePerson)
        this.deviceDetail.deviceSafePerson = '暂无';
      this.alarmDetail.alarmLevelName =
        this.alarmLevels[this.formData.alarmLevel];
      this.deviceDetail.runningStatusName =
        this.runningStatus[this.formData.runningStatus];
      if (
        this.formData.disposeStatus == '1' &&
        this.formData.disposeWay == '3'
      ) {
        this.$nextTick(() => {
          this.$refs.stepsDetail?.getDisposeGetDetail();
        });
      }
    },

    //告警处理
    dealHandle() {
      this.$refs.dealRef.open();
    },

    async getJudgeList() {
      try {
        const res = await getDictionaryQueries('sys_alarm_judge');
        res?.data.forEach((item) => {
          this.judgeList.push({
            label: item.dictLabel,
            text: item.dictLabel,
            value: item.dictValue,
          });
        });
      } catch (err) {
        console.log(err);
      }
    },

    //查看预案详情
    checkPlanDetail() {
      const queryData = {
        procInsId: this.formData.procInsId,
        planId: this.formData.planId,
        name: this.formData.emergencyPlan.planName,
        num: this.formData.emergencyPlan.startNum,
      };
      uni.navigateTo({
        url: `/pages/common/emergency/planDetail/index?query=${encodeURIComponent(
          JSON.stringify(queryData)
        )}`,
      });
    },

    topNavcardItemClick() {
      uni.$emit('refreshList');
    },
    // 告警等级
    async getAlarmLevel() {
      const res = await getDictionaryQueries('sys_alarm_rank');
      res.data.forEach((item) => {
        this.alarmLevels[item.dictValue] = item.dictLabel;
      });
    },
  },

  onLoad(option) {
    this.routeOption = option;
    // this.getJudgeList();
    this.getAlarmLevel();
    if (option.alarmId) this.getAlarmInfo();

    uni.$once('update', () => {
      this.$refs.dealRef.close();
      this.getAlarmInfo();
    });
  },
  onUnload() {
    uni.$off('update');
  },
};
</script>

<style lang="scss" scoped>
.imgCol {
  height: 100rpx !important;
  width: 100%;
}
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.u-col {
  font-size: $uni-font-size-base;
}
::v-deep .uni-section__content-title {
  font-weight: 600;
}

.btn_container {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
}
.statusStyle {
  height: 80rpx;
  justify-content: center;
  color: #fff;
  font-size: 18px;
}
</style>
