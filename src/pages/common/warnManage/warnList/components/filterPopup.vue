<template>
	<view class="">
		<u-popup :show="popupShow" @close="close" @open="open">
			<u--form labelPosition="left" ref="uForm" :model="filterForm" :rules="rules" labelWidth="20vw" style="padding: 0.625rem">
				<u-form-item label="设备编号" prop="deviceCode">
					<u--input v-model="filterForm.deviceCode" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="设备名称" prop="deviceName">
					<u--input v-model="filterForm.deviceName" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item label="告警事件" prop="strategyName">
					<u--input v-model="filterForm.strategyName" placeholder="请输入" maxlength="50" style="border: 2rpx #dadbde solid; margin-top: 20rpx" />
				</u-form-item>
				<u-form-item>
					<u-button text="重置" :loading="loading" loadingText="请稍后" @click="reset" />
					<u-button type="primary" text="确定" :loading="loading" loadingText="请稍后" @click="submitHandle" />
				</u-form-item>
			</u--form>
		</u-popup>
		<u-datetime-picker
			mode="datetime"
			:show="showStartTime"
			v-model="startTimeVal"
			@close="showStartTime = false"
			@cancel="showStartTime = false"
			@confirm="startTimeConfirm"
		>
		</u-datetime-picker>
		<u-datetime-picker
			mode="datetime"
			v-model="endTimeVal"
			:show="showEndTime"
			@close="showEndTime = false"
			@cancel="showEndTime = false"
			@confirm="endTimeConfirm"
		>
		</u-datetime-picker>
	</view>
</template>

<script>
import {
		formatDate
	} from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
	props: {
		judgeList: {
			type: Array,
			default: () => []
		},
		handleWay: {
			type: Array,
			default: () => []
		},
		warnInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			popupShow: false,
			loading: false,
			showStartTime: false,
			showEndTime: false,
			startTimeVal: Number(new Date()),
			endTimeVal: Number(new Date()),
			filterForm: {
				deviceCode: '',
				deviceName: '',
				strategyName: '',
			},
			rules: {
				// visitorName: {
				// 	type: 'string' || 'number',
				// 	required: true,
				// 	trigger: ['blur', 'change'],
				// 	message: '请选择告警研判'
				// },
				// visitorPhone: {
				// 	type: 'string',
				// 	// required: true,
				// 	trigger: ['blur', 'change'],
				// 	message: '请输入处理描述'
				// }
			}
		};
	},
	methods: {
		submitHandle() {
			this.$emit('filterSearch', this.filterForm);
			this.popupShow = false;
		},
		selectStartTime() {
			this.showStartTime = true;
		},
		selectEndTime() {
			this.showEndTime = true;
		},
		startTimeConfirm(e) {
			this.filterForm.startTime = formatDate(e.value, 'yyyy-MM-dd hh:mm:ss');
			this.showStartTime = false;
		},
		endTimeConfirm(e) {
			this.filterForm.endTime = formatDate(e.value, 'yyyy-MM-dd hh:mm:ss');
			this.showEndTime = false;
		},
		async open() {
			this.popupShow = true;
		},
		close() {
			this.popupShow = false;
		},
		reset() {
			this.filterForm = {
				deviceCode: '',
				deviceName: '',
				strategyName: '',
			};
		},
	}
};
</script>

<style lang="scss" scoped>
::v-deep .handleWay_container {
	width: 100vw;
	.u-tag {
		display: inline-block;
		text-align: center;

		.u-tag__text {
			font-size: 0.875rem;
		}
	}
}
</style>
