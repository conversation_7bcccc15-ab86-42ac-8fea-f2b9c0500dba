<template>
  <uni-card
    :title="cardTitle"
    @click="handleDetail"
    :is-shadow="true"
    shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
    spacing="12px"
    margin="12px 12px 0"
  >
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-16 text_scrolling">
          <text>{{ itemData.name }}</text>
        </view>
        <u-tag
          size="mini"
          :text="itemData.status.label"
          :type="itemData.status.type"
          plain
          plainFill
        ></u-tag>
      </view>
      <view class="mt4"> </view>
    </template>

    <uni-row slot="actions">
      <view class="flex mb5 align_center space_b font-12" style="color: #666">
        <span
          v-for="(item, index) in queryItems(itemData.disposeStatus)"
          :key="index"
          >{{ itemData[item.prop] || '暂无' }}</span
        >
      </view>
    </uni-row>
  </uni-card>
</template>

<script>
import { getStatus, getItems } from '../list_config.js';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: '',
      type: String,
    },
  },
  data() {
    return {};
  },
  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
    // queryStatus(val) {
    //   return getStatus(val);
    // },
    queryItems(val) {
      return getItems(val);
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}

.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
.item_name {
  color: #666;
}

.text_scrolling {
  white-space: nowrap; /* 保持文字在一行内显示 */
  overflow-x: auto; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 75%; /* 设置容器宽度 */
}
</style>
