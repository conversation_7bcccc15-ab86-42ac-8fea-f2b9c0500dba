<template>
  <view class="warnListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :hasFilter="false"
      :title="navBarTitle"
      :autoBack="autoBack"
      :leftText="leftText"
      @filterHandle="filterHandle"
    >
      <!-- <view slot="title-right" class="flex filter-box" style="color: #fff;" @click="filterHandle">
				<u--image class="top-filter" :src="globalConfig.iconImgPrefix + 'filter.png'"></u--image>
				<span class="filter-text">筛选</span>
			</view> -->
    </top-navbar>
    <contain-tab
      :showSearch="false"
      @scrolltolower="scrolltolower"
      @changeSelect="changeSelect"
      :tableData="tableData"
    >
      <template #topSlot>
        <view class="pb8" style="background: #f2f3f3">
          <u-tabs
            :itemStyle="{
              width: '50%',
              height: '2.75rem',
              background: '#fff',
              'padding-bottom': '4px',
            }"
            :activeStyle="{
              color: '#AA011C',
            }"
            :inactiveStyle="{ color: '#999999' }"
            :list="statusRadios"
            :current="queryData.disposeStatus"
            @change="statusSelect"
          ></u-tabs>
        </view>
      </template>

      <template #default="slotProps">
        <item
          :itemData="slotProps.itemData"
          @handleDetail="cardItemClick(slotProps.itemData)"
        />
      </template>
    </contain-tab>
    <filter-popup ref="filterRef" @filterSearch="filterSearch" />
    <u-toast ref="uToast"></u-toast>
    <u-loading-page :loading="loading" loadingText="请稍后" />
  </view>
</template>

<script>
import ContainTab from '@/pages/components/containTab.vue';
import Item from './components/item.vue';
import TopNavbar from '@/components/top-navbar/top-navbar.vue';
import {
  queryAlarmListRequest,
  transitAlarmGetMyAlarm,
} from '@/api/alarm/index.js';
import FilterPopup from './components/filterPopup.vue';
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
export default {
  components: {
    TopNavbar,
    FilterPopup,
    ContainTab,
    Item,
  },

  data() {
    return {
      cardType: 'leftType',
      globalConfig: getApp().globalData.config,
      sectionBtn: true,
      leftText: '',
      autoBack: true,

      navBarTitle: '',
      navBarName: '',
      statusRadios: [
        {
          name: '未处理',
          keyName: '0',
        },
        {
          name: '已处理',
          keyName: '1',
        },
      ],
      alarmType: '',
      queryData: {
        // searchValue: '',
        deviceCode: '',
        deviceName: '',
        strategyName: '',
        disposeStatus: '0',
        pageNum: 1,
        pageSize: 10,
      },
      loading: false,
      tableData: [],
      requestList: [],
      totalPage: 0,
      currTabNumber: 0,
    };
  },

  computed: {
    statusComputed() {
      return (status) => {
        switch (status) {
          case '2':
            return {
              type: 'success',
              label: '处理中',
            };
          case '1':
            return {
              type: 'success',
              label: '已处理',
            };
          case '0':
            return {
              type: 'warning',
              label: '未处理',
            };
        }
      };
    },
  },

  methods: {
    statusSelect(value) {
      this.currentIndex = value.index;
      this.queryData.disposeStatus = value.keyName;
      this.searchHandle();
    },

    // 告警列表
    async getTableData() {
      try {
        this.loading = true;
        let res;
        if (this.navBarName === 'MyWarnManage') {
          res = await transitAlarmGetMyAlarm(this.queryData);
        } else {
          res = await queryAlarmListRequest(this.queryData);
        }
        this.totalPage = Math.ceil(res.total / this.queryData.pageSize);
        this.currTabNumber = res.total;
        if (res.rows.length) {
          this.requestList.push(...res.rows);
          const newArr = res.rows.map((item) => {
            return {
              disposeStatus: item.disposeStatus,
              alarmId: item.alarmId,
              name: item.strategyName,
              alarmTime: formatDate(item.alarmTime, 'yyyy/MM/dd hh:mm'),
              fullName: item.areaName,
              status: this.statusComputed(item.disposeStatus),
              alarmType: { 1: '通行告警', 2: '作业告警' }[item.alarmType] || '',
            };
          });
          this.tableData.push(...newArr);
          // this.getDotNumber();
        }
      } catch (err) {
        console.log(err);
      }
      this.loading = false;
    },

    // 上滑刷新
    scrolltolower() {
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        return this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
      }
      this.getTableData();
    },
    getDotNumber() {
      this.statusRadios.forEach((e, index) => {
        if (this.currentIndex === index) {
          e.badge.value = this.currTabNumber;
        }
      });
    },
    //搜索
    searchHandle(value = '') {
      this.tableData = [];
      this.requestList = [];
      this.queryData.pageNum = 1;
      this.getTableData();
    },

    clearHandle() {
      this.queryData.searchValue = '';
      this.searchHandle();
    },
    filterHandle() {
      this.$refs.filterRef.open();
    },
    filterSearch(info) {
      this.queryData = { ...this.queryData, ...info };
      this.searchHandle();
    },
    //查看详情
    cardItemClick(item) {
      uni.navigateTo({
        url: `/pages/common/warnManage/warnDetail/index?alarmId=${item.alarmId}&name=${this.navBarName}`,
      });
    },
  },
  onLoad(option) {
     
    this.navBarTitle = option.title;
    this.navBarName = option.name;
    this.alarmType = option.type;
    uni.$on('refreshList', () => {
      this.searchHandle();
    });
  },
  onShow() {
    
    this.searchHandle();
  },
  onUnload() {
    uni.$off('refreshList');
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
</style>
