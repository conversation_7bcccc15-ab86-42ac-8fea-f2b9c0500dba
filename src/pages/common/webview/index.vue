<template>
  <view v-if="params.url">
    <web-view ref="webview"  :webview-styles="webviewStyles" :src="`${params.url}`"  @load="onWebViewLoad"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      params: {},
      webviewStyles: {
        progress: {
          color: '#FF3333',
        },
      },
      largeData:'',
      webviewReady: false  // 添加标记判断webview是否加载完成
      
    };
  },
  props: {
    src: {
      type: [String],
      default: null,
    },
  },
  onLoad(event) {
  
    // 处理页面参数
    this.params = event;
    this.params.url = decodeURIComponent( `${event.url}?id=${event.id}&token=${event.token}`);
    console.log('路由参数:', event);
    if (event.title) {
      uni.setNavigationBarTitle({
        title: event.title,
      });
    }
  },

  mounted() {

  },

  methods: {
   
  },

  onUnload() {
  
  }
};
</script>
<style>
.uni-page-head{
  background-color: brown;
  color: white;
}
</style>
