<template>
  <uni-card
    :title="cardTitle"
    @tap.native="handleDetail"
    :is-shadow="true"
    shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
    spacing="12px"
    margin="12px 12px 0"
  >
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-16 text_scrolling">
          <text>{{
            itemData.listName == 'second'
              ? `${itemData.kindName}——${itemData.usingTime}`
              : itemData.taskName
          }}</text>
        </view>
        <u-tag
          size="mini"
          shape="circle"
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
        ></u-tag>
      </view>
      <view class="mt4"> </view>
    </template>

    <u-row slot="actions" customStyle="margin-bottom: 10px" align="center">
      <u-col span="5">
        <view class="flex align_center space_b">
          <u--image
            v-if="itemData.status == 0"
            :src="globalConfig.iconImgPrefix + 'startpointpink.png'"
            width="23px"
            height="23px"
            mode="aspectFit"
          ></u--image>
          <u--image
            v-else
            :src="globalConfig.iconImgPrefix + 'startpoint.png'"
            width="23px"
            height="23px"
            mode="aspectFit"
          ></u--image>
          <!-- <u-text
          size="12"
          :lines="1"
          class="font-12"
          :text="itemData.startPointName || itemData.startPoint.pointName"
        /> -->
          <view class="font-12 setAllInfo">{{
            itemData.startPointName || ''
          }}</view>
        </view>
      </u-col>
      <u-col span="2" class="font-14">
        <view class="flex align_center space_c">
          <u--image
            class="right-arrow"
            :src="globalConfig.iconImgPrefix + 'rightarrow.png'"
            width="21px"
            height="6px"
            mode="aspectFit"
          ></u--image>
        </view>
      </u-col>
      <u-col span="5">
        <view class="flex align_center space_b">
          <view class="font-12 setAllInfo overBreak">{{
            itemData.endPointName || ''
          }}</view>
          <u--image
            :src="globalConfig.iconImgPrefix + 'endpoint.png'"
            width="23px"
            height="23px"
            mode="aspectFit"
          ></u--image>
        </view>
      </u-col>
    </u-row>

    <u-row
      slot="actions"
      class="flex font-12 mt8"
      v-for="(item, index) in queryItems(itemData.status, itemData.listName)"
      :key="index"
    >
      <u-col span="5">
        <view class="mr4 item_name flex space_end"> {{ item.name }}: </view>
      </u-col>
      <u-col span="2"> </u-col>
      <u-col span="5">
        <view class="mr4 item_name flex">
          {{ itemData[item.prop] || '暂无' }}
        </view>
      </u-col>
    </u-row>
    <template
      v-if="hasReceivOrders && queryStatus(itemData.status).text === '未分配'"
      slot="actions"
    >
      <view class="mt8">
        <u-button
          type="primary"
          text="接单"
          size="mini"
          @click.native.stop="receivOrders"
        />
      </view>
    </template>
  </uni-card>
</template>

<script>
import { getStatus, getItems } from '../list_config.js';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },

    cardTitle: {
      default: '',
      type: String,
    },
    hasReceivOrders: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
    };
  },

  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
    receivOrders() {
      this.$emit('receivOrders', this.itemData);
    },
    queryStatus(val) {
      return getStatus(val);
    },
    queryItems(val, val2) {
      return getItems(val, val2);
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}
.uni-card {
  border-radius: 20rpx !important;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
.item_name {
  color: #666;
}

.text_scrolling {
  white-space: nowrap; /* 保持文字在一行内显示 */
  overflow-x: auto; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 75%; /* 设置容器宽度 */
}
.setAllInfo {
  padding: 0 8rpx;
}
</style>
