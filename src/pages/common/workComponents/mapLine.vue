<template>
  <view>
    <top-navbar bgColor="#AA001E" title="地图" :autoBack="autoBack" leftText="">
    </top-navbar>
    <view id="myMap" ref="mapContainer"></view>
    <mapcontrol @change-map-type="changeBaseMap" :hasSetCenter="false" />
  </view>
</template>

<script>
import mapcontrol from '@/pages/common/map/mapcontrol.vue';
import {
  queryWorkDetailRequest,
  queryWorkRecordDetailRequest,
} from '@/api/work/index.js';
const pathEnum = {
  myWork: {
    first: '/myTask/plan/',
    second: '/myTask/appointment/',
  },
  workTask: {
    first: '/dispatch/plan/task/',
    second: '/dispatch/appointment/task/',
  },
  workRecord: {
    first: '/dispatch/record/plan/',
    second: '/dispatch/record/appointment/',
  },
};
export default {
  components: { mapcontrol },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      autoBack: true,
      latitude: 34.00623,
      longitude: 119.187811,
      anchors: [],
      height: '',
      scale: 16,
      minScale: 10, // 最小缩放级别
      maxScale: 20, // 最大缩放级别
      count: 0,
      covers: [],
      mapContext: null,
      infoOption: {},
      itemData: {},
      pathPointsArr: [],
    };
  },
  onLoad(option) {
    this.infoOption = option;
  },
  mounted() {
    this.infoInit(this.infoOption);
  },

  methods: {
    initMap() {
      const len = this.pathPointsArr.length;
      const index = Math.round(len / 2);
      const centerPoints = this.pathPointsArr[index];
      const startPoint = len
        ? this.pathPointsArr[0]
        : { latitude: '', longitude: '' };
      const endPoint = len
        ? this.pathPointsArr[len - 1]
        : { latitude: '', longitude: '' };
      this.map = new TMap.Map(document.getElementById('myMap'), {
        center: new TMap.LatLng(centerPoints.latitude, centerPoints.longitude), //地图显示中心点
        zoom: 13, //设置地图缩放级别
        pitch: 10, //设置俯仰角
        rotation: 20, //设置地图旋转角度
        disableDefaultUI: false,
        baseMap: {
          // 设置卫星地图
          //type: 'satellite',
          type: 'vector',
        },
      });
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ZOOM);
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.SCALE);
      this.map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);
      this.addStartEndPoints([startPoint, endPoint]);
      //设置移动边界限制
      this.setLimitBounds(
        { lat: 33.98981271513883, lng: 119.16046651161389 }, // 西南角坐标
        { lat: 34.036056860444155, lng: 119.21377142670633 } // 东北角坐标
      );
      this.addLine();
    },
    /**
     * 切换地图三维和二维
     * @param String type - satellite:卫星地图；vector:平面地图
      
     */
    changeBaseMap(type) {
      if (!this.map) return;
      this.map.setBaseMap({
        type,
      });
    },
    /**
     * 设置地图移动边界限制
     * @param {Object} startPoint - 始点坐标
     * @param {Object} endPoint - 钟点坐标
     */
    addStartEndPoints(val) {
      const [startPoint, endPoint] = val;
      //创建并初始化MultiMarker
      this.geometries = [
        {
          id: 'startPoint', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
          styleId: 'startPointStyle', //指定样式id
          position: new TMap.LatLng(startPoint.latitude, startPoint.longitude), //点标记坐标位置
        },
        {
          //第二个点标记
          id: 'endPoint',
          styleId: 'endPointStyle',
          position: new TMap.LatLng(endPoint.latitude, endPoint.longitude),
        },
      ];

      let markerLayer = new TMap.MultiMarker({
        map: this.map, //指定地图容器
        //样式定义
        styles: {
          //创建一个styleId为"startPointStyle"的样式（styles的子属性名即为styleId）
          startPointStyle: new TMap.MarkerStyle({
            width: 23, // 点标记样式宽度（像素）
            height: 27, // 点标记样式高度（像素）
            src: 'data:image/png;base64,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', //图片路径
            // "src": this.globalConfig.iconImgPrefix + 'startpointorigin.png',  //图片路径
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            anchor: { x: 16, y: 32 },
          }),
          endPointStyle: new TMap.MarkerStyle({
            width: 23, // 点标记样式宽度（像素）
            height: 27, // 点标记样式高度（像素）
            src: 'data:image/png;base64,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', //图片路径
            //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
            anchor: { x: 16, y: 32 },
          }),
        },
        //点标记数据数组
        geometries: this.geometries,
      });
    },
    /**
     * 做路线图层绘制
     */

    addLine() {
      const pathArr =
        this.pathPointsArr.length >= 2
          ? this.pathPointsArr.reduce((pre, next) => {
              const newpoint = new TMap.LatLng(next.latitude, next.longitude);
              pre.push(newpoint);
              return pre;
            }, [])
          : [];

      this.polylineLayer = new TMap.MultiPolyline({
        id: 'polyline-layer', //图层唯一标识
        map: this.map, //绘制到目标地图
        //折线样式定义
        styles: {
          style_blue: new TMap.PolylineStyle({
            color: '#3777FF', //线填充色
            width: 10, //折线宽度
            borderWidth: 0, //边线宽度
            showArrow: true,
            arrowOptions: {
              space: 70,
            },
            lineCap: 'round',
          }),
        },
      });
      this.polylineLayer.add({
        //新的折线添加到图层中
        id: 'pl_1',
        styleId: 'style_blue',
        paths: pathArr,
      });
    },

    async infoInit(option) {
      try {
        let res;
        if (option.come === 'workRecord') {
          res = await queryWorkRecordDetailRequest(
            pathEnum[option.come][option.pathType],
            option.id
          );
        } else {
          res = await queryWorkDetailRequest(
            pathEnum[option.come][option.pathType],
            option.id
          );
          // if(res.data.status)
        }

        this.itemData = res.data;
        this.pathPointsArr = this.itemData.routePlan
          ? this.itemData.routePlan.reduce((pre, next) => {
              const newpoint = {
                latitude: next?.lat || '',
                longitude: next?.lng || '',
              };
              pre.push(newpoint);
              return pre;
            }, [])
          : [];
        this.initMap();
      } catch (e) {
        console.log(e);
      }
    },
    /**
     * 设置地图移动边界限制
     * @param {Object} sw - 西南角坐标，包含 lat 和 lng 属性
     * @param {Object} ne - 东北角坐标，包含 lat 和 lng 属性
     */
    setLimitBounds(sw, ne) {
      if (!this.map) return false;

      if (
        sw &&
        ne &&
        'lat' in sw &&
        'lng' in sw &&
        'lat' in ne &&
        'lng' in ne
      ) {
        // 创建西南角和东北角的坐标点
        const swPoint = new TMap.LatLng(sw.lat, sw.lng);
        const nePoint = new TMap.LatLng(ne.lat, ne.lng);

        // 创建边界对象
        const boundary = new TMap.LatLngBounds(swPoint, nePoint);

        // 使用 fitBounds 方法将地图视图调整到指定边界
        this.map.setBoundary(boundary);

        // 注意：腾讯地图API没有直接限制地图移动范围的方法
        // 我们可以监听地图移动事件，如果超出边界则重置
        this.map.on('dragend', () => {
          const center = this.map.getCenter();
          if (!boundary.contains(center)) {
            // 如果中心点超出边界，将地图重新调整到边界内
            this.map.setBoundary(boundary);
          }
        });

        return true;
      } else {
        console.error(
          '设置地图边界限制失败：参数格式不正确，需要包含 lat 和 lng 属性的 sw 和 ne 对象'
        );
        return false;
      }
    },

    /**
     * 移除地图移动边界限制
     */
    removeLimitBounds() {
      if (!this.map) return false;

      // 移除地图的移动边界限制
      this.map.setLimitBounds(null);

      return true;
    },
  },
};
</script>
<style scoped lang="scss">
#myMap {
  position: relative;
  width: 100%;
  height: 100vh;
}
</style>
