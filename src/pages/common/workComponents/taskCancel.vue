<template>
  <view>
    <u-modal
      :show="show"
      mode="center"
      @close="close"
      round="8"
      showCancelButton
      @confirm="confirm"
      @cancel="close"
      confirmColor="#aa001e"
    >
      <view class="slot-content">
        <uni-section title="取消原因" type="line">
          <u--textarea
            placeholder="请输入取消原因"
            border="surround"
            v-model="cancelReason"
            maxlength="100"
            count
          />
        </uni-section>

        <!-- <view class="footer_button">
                    <u-button style="width: 180rpx" plain hairline type="primary" text="确定" @click="confirm"></u-button>
                </view> -->
      </view>
    </u-modal>
  </view>
</template>

<script>
import { transferWorkRequest, assignTasksRequest } from '@/api/work/index.js';
import { cancelTaskRequest } from '@/api/work/index.js';

const pathEnum = {
  first: 'plan',
  second: 'appointment',
};

export default {
  name: 'TaskCancel',
  props: {
    taskId: {
      default: '',
      type: String,
    },
    pathType: {
      type: '',
      default: '',
    },
  },
  data() {
    return {
      show: false,
      cancelReason: '',
    };
  },

  methods: {
    close() {
      this.show = false;
      this.cancelReason = '';
    },
    open() {
      this.show = true;
    },
    async confirm() {
      if (!this.cancelReason) return uni.$u.toast('请输入取消原因');
      try {
        const payload = {
          taskId: this.taskId,
          cancelReason: this.cancelReason,
        };
        await cancelTaskRequest(pathEnum[this.pathType], payload);
        uni.$u.toast('取消成功');
        this.$emit('updateInfo');
        this.close();
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-modal__content {
  flex-direction: column;
}
.popup_content {
  width: 600rpx;
  padding: 40rpx;
}

.footer_button {
  margin-top: 40rpx;
  text-align: end;
}
</style>
