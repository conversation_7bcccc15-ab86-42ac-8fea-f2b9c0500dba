<template>
  <view>
    <u-modal
      :show="show"
      @close="close"
      round="8"
      showCancelButton
      @confirm="confirm"
      @cancel="close"
      confirmColor="#aa001e"
    >
      <view class="slot-content">
        <uni-section title="作业人员" type="line">
          <!-- <uni-data-select
            v-model="worker"
            :localdata="workerList"
          ></uni-data-select> -->
          <u-radio-group v-model="worker" placement="row" activeColor="#aa001e">
            <u-radio
              :customStyle="{ marginBottom: '8px' }"
              v-for="(item, index) in workers"
              :key="index"
              :label="item.text"
              :name="item.value"
              @change="radioClick"
            >
            </u-radio>
          </u-radio-group>
          <!-- <view class="flex" style="flex-wrap: wrap">
            <u-tag
              v-for="(item, index) in workers"
              :key="item.value"
              size="mini"
              :text="item.text"
              :plain="item.value != worker"
              type="warning"
              :name="index"
              @click="radioClick"
              class="mr8 mt4"
            >
            </u-tag>
          </view> -->
        </uni-section>
        <uni-section
          v-if="[1, 2].includes(Number(taskStatus))"
          title="转移原因"
          type="line"
        >
          <u--textarea
            maxlength="100"
            placeholder="请输入转移原因"
            border="surround"
            count
            v-model="transferReason"
          />
        </uni-section>

        <!-- <view class="footer_button">
          <u-button
            style="width: 180rpx"
            plain
            hairline
            type="primary"
            text="确定"
            @click="confirm"
            :disabled="!worker"
          ></u-button>
        </view> -->
      </view>
    </u-modal>
  </view>
</template>

<script>
import { transferWorkRequest, assignTasksRequest } from '@/api/work/index.js';

const pathEnum = {
  first: 'plan',
  second: 'appointment',
};

export default {
  name: 'TaskChange',
  props: {
    taskId: {
      default: '',
      type: String,
    },
    taskStatus: {
      type: [Array, String],
      default: '',
    },
    workerList: {
      default: () => [],
      type: [Array],
    },
    workerShow: {
      default: false,
      type: Boolean,
    },
    pathType: {
      type: '',
      default: '',
    },
  },
  data() {
    return {
      show: false,
      worker: '',
      transferReason: '',
      workers: [],
    };
  },
  onShow() {
    console.log(this.workers);
  },
  watch: {
    workerShow(val) {
      if (val) {
        this.show = true;
        this.worker = '';
        this.transferReason = '';
        this.workers = this.workerList.reduce((pre, next) => {
          pre.push(next);
          return pre;
        }, []);
      }
    },
  },
  methods: {
    close() {
      this.show = false;
      this.$emit('changeWorkerShow');
    },
    radioClick(name) {
      this.worker = name;
    },
    async confirm() {
      try {
        const data = {
          taskId: this.taskId,
          employeeId: this.worker,
        };
        if ([1, 2].includes(Number(this.taskStatus))) {
          await transferWorkRequest(pathEnum[this.pathType], {
            ...data,
            transferReason: this.transferReason,
          });
        } else if (Number(this.taskStatus) === 0) {
          await assignTasksRequest(pathEnum[this.pathType], data);
        }

        uni.$u.toast('操作成功');
        this.$emit('updateInfo');
        this.close();
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .u-modal__content {
  flex-direction: column;
}
::v-deep .uni-section-content {
  padding: 16rpx 32rpx;
}
::v-deep .u-radio-group--row {
  flex-wrap: wrap;
}
::v-deep .u-radio {
  width: 50%;
}
.popup_content {
  width: 600rpx;
  padding: 40rpx;
}

.footer_button {
  margin-top: 40rpx;
  text-align: end;
}
</style>
