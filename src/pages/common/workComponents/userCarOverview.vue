<template>
  <uni-card
    :is-shadow="true"
    :border="false"
    spacing="12px"
    margin="12px 12px 0"
    shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
    :title="cardTitle"
    @click="handleDetail">
    <template v-slot:title>
      <view class="flex space_b align_center">
        <view class="font-16">
          <text>{{ appointmentType(itemData.appointmentType) }}</text>
        </view>
        <view class="font-14 text-grey1">
          <text class="time-sty">{{ itemData.usingTime }}</text>
        </view>
        <u-tag
          size="mini"
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
        ></u-tag>
      </view>
      <!-- <view class="mt4">
        <u-line></u-line>
      </view> -->
    </template>

    <u-row slot="actions" customStyle="margin-bottom: 10px" align="bottom">
      <u-col span="5" style="display: contents;">
        <u--image
          v-if="itemData.status == 0"
          :src="globalConfig.iconImgPrefix + 'startpointpink.png'"
          width="23px"
          height="23px"
          mode="aspectFit"
        ></u--image>
        <u--image
          v-else
          :src="globalConfig.iconImgPrefix + 'startpoint.png'"
          width="23px"
          height="23px"
          mode="aspectFit"
        ></u--image>
        <u-text size="12" :lines="1" align="right" class="font-12" :text="itemData.startPoint.pointName" />
      </u-col>
      <u-col span="2" class="font-14" style="align-items: center;">
        <u-row>
          <u--image
            class="right-arrow"
            :src="globalConfig.iconImgPrefix + 'rightarrow.png'"
            width="21px"
            height="6px"
            mode="aspectFit"
          ></u--image>
        </u-row>
      </u-col>
      <u-col span="5" style="display: contents;">
        <u-text size="12" :lines="1" class="font-12" :text="itemData.endPoint.pointName" />
        <u--image
          :src="globalConfig.iconImgPrefix + 'endpoint.png'"
          width="23px"
          height="23px"
          mode="aspectFit"
        ></u--image>
      </u-col>
    </u-row>
  </uni-card>
</template>

<script>
import { getStatus, getItems } from '../list_config.js';
export default {
  name: 'Name',
  props: {
    itemData: {
      default: () => {},
      type: [Object],
    },
    listCard: {
      default: () => [],
      type: [Array],
    },
    cardTitle: {
      default: '',
      type: String,
    },
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
    };
  },
  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },
  },
  onLoad() {},
  onShow() {},
  methods: {
    handleDetail() {
      this.$emit('handleDetail', this.itemData);
    },
    queryStatus(val) {
      return getStatus(val);
    },
    queryItems(val,val2) {
      return getItems(val, val2);
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-col {
  padding: 10px 0;
}
.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.little-margin {
  margin-right: 8px;
}
.item_name {
  color: #666;
}
.custom-sty {
  border-bottom-color: #616367 !important;
}
.right-arrow {
  ::v-deep .u-image {
    margin-bottom: 8rpx;
  }
}
</style>
