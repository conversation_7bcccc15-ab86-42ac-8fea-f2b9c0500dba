<template>
  <view class="toTaskListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      leftText=""
    >
      <!-- <template slot="title-right">
        <u-tag
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
          plain
          plainFill
        ></u-tag>
      </template> -->
    </top-navbar>
    <view class="bgCard">
      <view class="flex align_center statusStyle">
        <u-tag
          shape="circle"
          :text="queryStatus(itemData.status).text"
          :color="queryStatus(itemData.status).color"
          :bgColor="queryStatus(itemData.status).bgColor"
          :borderColor="queryStatus(itemData.status).color"
        ></u-tag>
      </view>
      <view class="setOver">
        <uni-card
          :is-shadow="true"
          shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
          spacing="12px"
          margin="12px 12px 0"
        >
          <template v-slot:title>
            <uni-section
              type="line"
              title="任务信息"
              class="pb1 mb8"
            ></uni-section>
          </template>
          <uni-forms :modelValue="itemData" label-width="80">
            <uni-forms-item
              :label="item.label"
              :name="item.name"
              v-for="item in getFormItem()"
              :key="item.name"
              :label-width="item['label-width']"
            >
              <view class="text_scrolling">
                <text v-if="item.name === 'taskDuration'">{{
                  `${itemData[item.name]}分钟`
                }}</text>

                <text v-else-if="item.name === 'appointmentType'">{{
                  appointmentType(itemData[item.name])
                }}</text>

                <text
                  v-else-if="
                    ['applicantName', 'applicantPhone'].includes(item.name)
                  "
                >
                  {{ itemData[item.name] || '暂无' }}
                </text>

                <view class="setAllInfo" v-else>{{
                  itemData[item.name] || '暂无'
                }}</view>
              </view>
            </uni-forms-item>
          </uni-forms>
        </uni-card>
        <uni-card
          :is-shadow="true"
          shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
          margin="10px"
        >
          <template v-slot:title>
            <uni-section
              type="line"
              title="作业区域及路线"
              class="pb1 mb8"
            ></uni-section>
          </template>
          <u-row customStyle="margin-bottom: 10px" align="center">
            <u-col span="4">
              <view class="flex align_center space_b">
                <u--image
                  v-if="itemData.status == 0"
                  :src="globalConfig.iconImgPrefix + 'startpointpink.png'"
                  width="23px"
                  height="23px"
                  mode="aspectFit"
                ></u--image>
                <u--image
                  v-else
                  :src="globalConfig.iconImgPrefix + 'startpoint.png'"
                  width="23px"
                  height="23px"
                  mode="aspectFit"
                ></u--image>
                <!-- <u-text
          size="12"
          :lines="1"
          class="font-12"
          :text="itemData.startPointName || itemData.startPoint.pointName"
        /> -->
                <view class="font-12 setAllInfo pl4 pr4">{{
                  itemData.startPoint ? itemData.startPoint.pointName : ''
                }}</view>
              </view>
            </u-col>
            <u-col span="4" class="font-12">
              <view
                v-show="infoOption.pathType === 'first'"
                class="mb4 flex space_c"
              >
                <u-tag
                  size="mini"
                  text="轨迹地图"
                  color="#fff"
                  bgColor="#aa001e"
                  borderColor="#fff"
                  plain
                  class="mb4"
                  @click="goMapClick"
                />
              </view>

              <u--image
                class="right-arrow"
                :src="globalConfig.iconImgPrefix + 'rightarrow.png'"
                width="100%"
                height="6px"
                mode="aspectFit"
              ></u--image>
            </u-col>
            <u-col span="4">
              <view class="flex align_center space_b">
                <view class="font-12 setAllInfo pl4 pr4">{{
                  itemData.endPoint ? itemData.endPoint.pointName : ''
                }}</view>
                <u--image
                  :src="globalConfig.iconImgPrefix + 'endpoint.png'"
                  width="23px"
                  height="23px"
                  mode="aspectFit"
                ></u--image>
              </view>
            </u-col>
          </u-row>
        </uni-card>
        <uni-card
          :is-shadow="true"
          shadow="0px 4px 43px 0px rgba(0,0,0,0.1)"
          margin="10px"
          v-if="transferRecordLength"
        >
          <template v-slot:title>
            <uni-section
              type="line"
              title="执行人转移记录"
              class="pb1 mb8"
            ></uni-section>
          </template>
          <uni-list v-if="transferRecordLength" :border="false">
            <uni-list-item
              v-for="(item, index) in itemData.transferRecord"
              :key="index"
              :border="false"
            >
              <template v-slot:header>
                <view
                  class="slot-box"
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                  "
                >
                  <view style="position: relative">
                    <view
                      class="step"
                      :style="{
                        background:
                          index == transferRecordLength - 1
                            ? '#aa001e'
                            : '#999',
                      }"
                    ></view>
                    <view
                      class="stepBefore"
                      v-show="index != transferRecordLength - 1"
                    ></view>
                  </view>
                </view>
              </template>
              <template v-slot:body>
                <view class="flex space_b list_item_body">
                  <view>
                    <view class="font-14">{{
                      item.employee
                        ? item.employee.employeeName
                        : '暂无人员信息'
                    }}</view>
                    <view class="font-12 text-grey1">{{
                      item.employee ? item.employee.phoneNumber : '暂无联系方式'
                    }}</view>
                  </view>
                  <view class="transfer-sty">
                    <view class="font-12 text-grey1">
                      {{ item.transferTime }}
                    </view>
                    <u--text
                      v-show="item.transferReason"
                      size="12"
                      :lines="2"
                      type="error"
                      text="查看原因"
                      @click="open(item.transferReason)"
                    ></u--text>
                  </view>
                </view>
              </template>
              <!-- <template v-slot:footer>
                <view class="transfer-sty">
                  <view class="font-12 text-grey1">
                    {{ item.transferTime }}
                  </view>
                  <u--text
                    v-show="item.transferReason"
                    size="12"
                    :lines="2"
                    type="error"
                    text="查看原因"
                    @click="open(item.transferReason)"
                  ></u--text>
                </view>
              </template> -->
            </uni-list-item>
          </uni-list>
          <u-empty
            width="120"
            height="120"
            marginTop="20"
            v-else
            mode="list"
            icon="/static/images/iconImg/empty.png"
          />
        </uni-card>
      </view>
    </view>

    <!-- <OrderTaskInfo v-else-if="infoOption.pathType === 'second'" /> -->

    <view class="setbuttonstyle">
      <u-button
        v-if="model === 'myWork' && Number(itemData.status) === 1"
        type="primary"
        text="开始任务"
        style="margin: 0 10px"
        @click="doWork"
      />
      <u-button
        v-if="model === 'workTask' && Number(itemData.status) === 0"
        type="primary"
        text="任务分配"
        style="margin: 0 10px"
        @click="doWork"
      />
      <u-button
        v-if="model === 'workTask' && [1, 2].includes(Number(itemData.status))"
        type="primary"
        text="任务转移"
        style="margin: 0 10px"
        @click="doWork"
      />
      <u-button
        v-if="
          model === 'workTask' && [0, 1, 2].includes(Number(itemData.status))
        "
        type="primary"
        color="#909193"
        text="取消任务"
        style="margin: 0 10px"
        @click="cancelWork"
      />
      <u-button
        v-if="[1, 2].includes(Number(itemData.status))"
        type="primary"
        text="结束任务"
        :disabled="Number(itemData.status) == 1"
        style="margin: 0 10px"
        @click="finishWork"
      />
    </view>
    <u-toast ref="uToast"></u-toast>
    <task-change
      :taskId="itemData.taskId"
      :taskStatus="itemData.status"
      :pathType="this.infoOption.pathType"
      :workerShow="workerShow"
      :workerList="workerList"
      @changeWorkerShow="changeWorkerShow"
      @updateInfo="updateInfo"
    />

    <task-cancel
      ref="cancelTaskRefs"
      :taskId="itemData.taskId"
      :pathType="this.infoOption.pathType"
      @updateInfo="updateInfo"
    />

    <TaskCancelPopup
      :taskId="itemData.taskId"
      :cancelTitle="cancelTitle"
      :inputPlaceholder="inputPlaceholder"
      :confirmMessage="confirmMessage"
      :cancelTask="cancelTask"
      @close="handleClose"
      @success="handleSuccess"
      @error="handleError"
      ref="taskCancelPopup"
    />
    <u-popup
      :show="lookReason"
      :closeable="true"
      @close="lookReason = false"
      :round="10"
    >
      <view class="popup_body">
        <view class="popTitle"> 转移原因</view>
        <text class="popInfo setAllInfo">{{ transferReason }}</text>
      </view>
    </u-popup>
  </view>
</template>

<script>
import TaskChange from './taskChange';
import TaskCancel from './taskCancel.vue';
import TaskCancelPopup from '../../common/feedback/index.vue';
import {
  queryWorkDetailRequest,
  getWorkerList,
  startMyWorkRequest,
  queryWorkRecordDetailRequest,
  endTask,
  planEndTask,
} from '@/api/work/index.js';
import { getStatus } from '../list_config.js';
import { formItems, orderFormItems } from './form_list.js';
import { getToken } from '@/utils/auth';

const img = require('@/static/images/iconImg/banbenxinxi.png');

const pathEnum = {
  myWork: {
    first: '/myTask/plan/',
    second: '/myTask/appointment/',
  },
  workTask: {
    first: '/dispatch/plan/task/',
    second: '/dispatch/appointment/task/',
  },
  workRecord: {
    first: '/dispatch/record/plan/',
    second: '/dispatch/record/appointment/',
  },
};

export default {
  components: {
    TaskChange,
    TaskCancel,
    TaskCancelPopup,
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      itemData: {},
      autoBack: true,
      model: '',
      showOperation: false,
      workerShow: false,
      workerList: [],
      latitude: 34.007287,
      longitude: 119.188279,
      markers: [],
      reasonShow: false,
      infoOption: {},
      title: '',
      cancelTitle: '结束任务原因',
      inputPlaceholder: '请输入结束任务原因',
      confirmMessage: '请填写结束任务原因', // 新增 confirmMessage 属性
      length: 0,
      transferRecordLength: 0,
      lookReason: false,
      transferReason: '',
      // gomapUrl: "http://devops.smartcloud.com:10082/ztf-zixun/jsyp-frontend-gis/dev/#/map",
      gomapUrl: getApp().globalData.config.appInfo.gomapUrl,
    };
  },

  computed: {
    appointmentType() {
      return (value) => {
        switch (Number(value)) {
          case 1:
            return '园区用车';
          case 2:
            return '生产用车';
          case 3:
            return '园区接驳';
        }
      };
    },

    taskStatus() {
      return (status) => {
        switch (Number(status)) {
          case 0:
            return '未分配';
          case 1:
            return '未开始';
          case 2:
            return '进行中';
          case 3:
            return '已结束';
          case 4:
            return '已取消';
          case 5:
            return '已失效';
        }
      };
    },
    queryRole() {
      console.log(this.$store.state.user.roles);
      return this.$store.state.user.roles.indexOf('homeworkdriver') !== -1;
    },
  },

  onLoad(option) {
    this.infoOption = option;
    this.title = option.come === 'workTask' ? '任务详情' : '作业详情';
  },

  methods: {
    async infoInit(option) {
      try {
        let res;
        if (option.come === 'workRecord') {
          res = await queryWorkRecordDetailRequest(
            pathEnum[option.come][option.pathType],
            option.id
          );
        } else {
          res = await queryWorkDetailRequest(
            pathEnum[option.come][option.pathType],
            option.id
          );
          // if(res.data.status)
        }

        this.itemData = res.data;
        // // 获取首尾点位
        const points = this.itemData.taskPoints;
        if (points?.length > 0) {
          this.itemData.startPoint = points[0];
          this.itemData.endPoint = points[points.length - 1];
        }
        this.length = res.data?.transferRecord || 0;
        this.transferRecordLength = res.data?.transferRecord?.length || 0;
        if (
          Number(res.data.appointmentType) !== 3 &&
          this.infoOption.pathType === 'second'
        ) {
          this.itemData.applicantName = res.data.appointmentUser.userName;
          this.itemData.applicantPhone = res.data.appointmentUser.phoneNumber;
        }
        this.itemData.employeeName =
          res.data.currentEmployee?.employeeName || '';
        this.itemData.phoneNumber = res.data.currentEmployee?.phoneNumber || '';

        this.model = option.come;

        // if (option.come == 'myWork') {
        //     this.showOperation = [0].includes(this.itemData.status);
        // }
        // if (option.come == 'workRecode') {
        //     this.showOperation = [3, 4, 6].includes(this.itemData.status);
        // }

        this.getWorkers(option);

        //this.addMarkers();
      } catch (e) {
        console.log(e);
      }
    },
    async getWorkers(option) {
      const res = await getWorkerList(option.id);
      this.workerList = res.data?.reduce((pre, item) => {
        pre.push({
          text: item.userName,
          value: item.userId,
        });
        return pre;
      }, []);
      //this.workerList = new Array(20).fill({ text: '5', value: '666' });
    },

    queryStatus(val) {
      return getStatus(val || 0);
    },

    getFormItem() {
      let res;
      if (this.infoOption.pathType === 'first') {
        res = formItems.filter((item) => {
          if (
            !item.statusMap ||
            (item.statusMap?.length &&
              item.statusMap.includes(Number(this.itemData.status)))
          ) {
            return item;
          }
        });
      } else {
        res = orderFormItems;
      }
      return res;
    },

    changeWorkerShow() {
      this.workerShow = false;
    },

    async doWork() {
      if (this.model == 'workTask') {
        this.workerShow = true;
      } else if (this.model === 'myWork') {
        const payload = {
          taskId: this.itemData.taskId,
          carId: this.itemData.currentCarId,
        };
        try {
          let res = await startMyWorkRequest(
            pathEnum[this.infoOption.come][this.infoOption.pathType],
            payload
          );
          if (res.code == 200) {
            this.$refs.uToast.show(
              this.$setToastMsg({
                message: '操作成功',
                type: 'success',
              })
            );
            this.updateInfo();
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '操作成功',
            type: 'success',
          })
        );
        uni.navigateBack();
      }
    },
    cancelWork() {
      this.$refs.cancelTaskRefs.open();
    },
    openPopup() {
      this.$refs.taskCancelPopup.open();
    },
    open(val) {
      this.lookReason = true;
      this.transferReason = val;
    },
    handleClose() {},
    handleSuccess() {
      uni.navigateBack();
      // 处理取消成功后的逻辑，例如刷新页面数据
    },
    goMapClick() {
      uni.navigateTo({
        url: `/pages/common/workComponents/mapLine?come=${this.infoOption.come}&pathType=${this.infoOption.pathType}&id=${this.itemData.taskId}`,
      });
    },
    handleError(error) {
      console.error('取消失败', error);
    },
    async cancelTask(cancelReason) {
      try {
        const payload = {
          taskId: this.infoOption.id,
          cancelReason: cancelReason,
        };
        if (this.infoOption.pathType === 'first') {
          await planEndTask(payload);
        } else {
          await endTask(payload);
        }
        return Promise.resolve();
      } catch (e) {
        console.error(e);
        return Promise.reject(e);
      }
    },
    finishWork() {
      this.$modal.confirm('确定结束该任务吗？').then(() => {
        this.cancelTask()
          .then(() => {
            this.handleSuccess();
          })
          .catch(() => {});
      });
      // this.$refs.taskCancelPopup.open();
    },
    updateInfo() {
      this.infoInit(this.infoOption);
    },
  },
  onShow() {
    this.infoInit(this.infoOption);
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}

::v-deep .uni-forms-item__content {
  align-self: center;
}
::v-deep .uni-forms-item {
  margin-bottom: 8px;
}
::v-deep .uni-section-header {
  padding: 12px 0;
}
.statusStyle {
  height: 80rpx;
  justify-content: center;
  color: #fff;
  font-size: 18px;
}
.popup_body {
  padding: 100rpx 64rpx;

  position: relative;
  .popTitle {
    color: #aa001e;
    position: absolute;
    top: 30rpx;
    left: 30rpx;
    font-size: $uni-font-size-lg;
  }
  .popInfo {
    color: $uni-text-color;
    text-indent: 2em;
  }
}
.setOver {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
}

.pb1 {
  padding-bottom: 1px;
}
.list_item_body {
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #f7f6f6;
  flex-grow: 1;
}
.step {
  height: 20rpx;
  width: 20rpx;
  position: relative;
  border-radius: 100px;
}
.stepBefore {
  width: 2px;
  height: 40rpx;
  background-color: #999;
  position: absolute;
  top: 20rpx;
  left: 10rpx;
  transform: translateX(-50%);
}
.text_scrolling {
  white-space: nowrap;
  /* 保持文字在一行内显示 */
  overflow-x: auto;
  /* 隐藏溢出的内容 */
  // text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 80%;
}

.setbuttonstyle {
  display: flex;
  justify-content: space-around;
  bottom: 8rpx;
}
.transfer-sty {
  width: 120px;
}
.setAllInfo {
  white-space: normal;
  overflow-wrap: break-word;
}
</style>
