<template>
  <view class="toTaskListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      :hasFilter="false"
      leftText=""
      @filterHandle="filterHandle"
    />

    <contain-tab
      :showSearch="true"
      @scrolltolower="scrolltolower"
      @changeSelect="changeSelect"
      @serching="searchOperate"
      :tableData="tableData"
    >
      <template #topSlot>
        <view class="pb8" style="background: #f2f3f3">
          <u-tabs
            :itemStyle="{
              width: '50%',
              height: '2.75rem',
              background: '#fff',
              'padding-bottom': '4px',
            }"
            :activeStyle="{
              color: '#AA011C',
            }"
            :inactiveStyle="{ color: '#999999' }"
            :list="topTabList"
            :current="currentIndex"
            @change="changeTabHandle"
          ></u-tabs>
        </view>
      </template>

      <template #default="slotProps">
        <item
          :itemData="slotProps.itemData"
          :hasReceivOrders="moduleName === 'MyWork'"
          @handleDetail="cardItemClick(slotProps.itemData)"
          @receivOrders="receivOrders(slotProps.itemData)"
        />
      </template>
    </contain-tab>

    <search-sheet ref="searchSheetRef">
      <u--form
        :model="formData"
        ref="uForm"
        labelPosition="top"
        style="padding: 0 0.625rem"
        :labelStyle="{ 'font-weight': 600 }"
        labelWidth="6.25rem"
      >
        <u-form-item label="任务执行时间" prop="name">
          <u-input v-model="formData.name" />
        </u-form-item>

        <u-form-item label="任务状态" prop="status">
          <u-grid :border="false" col="3">
            <u-grid-item v-for="(item, index) in radios" :key="index">
              <view style="margin: 0.625rem 0">
                <u-tag
                  :text="item.text"
                  :plain="!item.checked"
                  borderColor="#aa001e"
                  :name="index"
                  @click="radioClick"
                />
              </view>
            </u-grid-item>
          </u-grid>
        </u-form-item>
      </u--form>
    </search-sheet>

    <u-toast ref="uToast"></u-toast>
    <u-modal
      :show="modalShow"
      showCancelButton
      @cancel="modalCancel"
      @confirm="modalConfirm"
      ref="receiveOrderRef"
    >
      确认接单吗？
    </u-modal>
    <u-loading-page :loading="loading" loadingText="请稍后" />
  </view>
</template>

<script>
import ContainTab from '@/pages/components/containTab.vue';
import item from '@/pages/common/workComponents/item.vue';
import SearchSheet from '../../components/searchSheet.vue';
import { topAllSelectList } from '../list_config.js';
import {
  queryWorkTaskRequest,
  queryWorkRecordListRequest,
  receiveOrder,
  receivePlanOrder,
} from '@/api/work/index.js';

const pathEnum = {
  MyWork: {
    first: '/myTask/plan/',
    second: '/myTask/appointment/',
  },
  WorkTask: {
    first: '/dispatch/plan/task/',
    second: '/dispatch/appointment/task/',
  },
  WorkRecode: {
    first: '/dispatch/record/plan/',
    second: '/dispatch/record/appointment/',
  },
};

const comeEnum = {
  MyWork: 'myWork',
  WorkTask: 'workTask',
  WorkRecode: 'workRecord',
};

export default {
  components: {
    ContainTab,
    item,
    SearchSheet,
  },
  data() {
    return {
      autoBack: true,
      topTabList: [
        {
          name: '计划任务',
          keyName: 0,
        },
        {
          name: '预约任务',
          keyName: 1,
        },
      ],
      loading: false,
      currentIndex: 0,
      title: '',
      queryData: {
        taskName: '',
        // status: 2,
        pageNum: 1,
        pageSize: 10,
      },
      totalPage: 0,
      tableData: [],
      listName: 'first',
      radios: topAllSelectList,
      timeRange: [],
      formData: {
        startTime: '',
        endTime: '',
        status: null,
      },
      modalShow: false,
      currentTaskId: '',
      moduleName: '',
    };
  },

  onLoad(option) {
    this.title = option.title;
    this.moduleName = option.name;
  },
  onShow() {
    this.queryData.pageNum = 1;
    this.tableData = [];
    this.init();
  },
  methods: {
    async init() {
      try {
        //计划任务接口
        this.loading = this.queryData.pageNum == 1 ? true : false;
        let res;
        if (this.moduleName === 'WorkRecode') {
          res = await queryWorkRecordListRequest(
            pathEnum[this.moduleName][this.listName],
            this.queryData
          );
        } else {
          res = await queryWorkTaskRequest(
            pathEnum[this.moduleName][this.listName],
            this.queryData
          );
        }

        if (res.rows && res.rows.length) {
          this.tableData.push(...res.rows);
          this.totalPage = Math.ceil(res.total / this.queryData.pageSize);
        } else if (res.data?.rows && res.data.rows.length) {
          this.tableData.push(...res.data.rows);
          this.totalPage = Math.ceil(res.data.total / this.queryData.pageSize);
        }

        this.tableData.forEach((item) => {
          item.listName = this.listName;
          if (item.startPoint) {
            item.startPointName = item.startPoint.pointName;
          }
          if (item.endPoint) {
            item.endPointName = item.endPoint.pointName;
          }
          if (item.appointmentUser)
            item.appointmentUserName = item.appointmentUser.userName;
        });
      } catch (e) {
        console.log(e);
      }
      this.loading = false;
    },

    cardItemClick(data) {
      uni.navigateTo({
        url: `/pages/common/workComponents/workInfo?id=${data.taskId}&come=${
          comeEnum[this.moduleName]
        }&pathType=${this.listName}`,
      });
    },

    changeTabHandle(val) {
      this.currentIndex = val.index;

      this.listName = val.index == 0 ? 'first' : 'second';

      this.research();
    },

    changeSelect(val) {
      this.queryData.status = val;
      this.research();
    },
    searchOperate(val) {
      this.queryData.taskName = val;
      this.research();
    },
    research() {
      this.tableData = [];
      this.queryData.pageNum = 1;
      this.init();
    },
    scrolltolower(val) {
      // console.log(val)
      this.queryData.pageNum++;
      if (this.queryData.pageNum > this.totalPage) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '已加载全部',
            type: 'success',
          })
        );
        return;
      }
      this.init();
    },

    //切换任务
    filterHandle() {
      this.$refs.searchSheetRef.open();
    },

    radioClick(name) {
      this.radios.map((item, index) => {
        item.checked = index === name ? true : false;
      });

      this.formData.status = this.radios[name].value;
    },

    selectHandle(val) {
      console.log(val);
      this.listName = val.listName;
      // this.topTabList = val.listName === 'first' ? topAllTabList : workTabList;
      this.research();
    },
    receivOrders(data) {
      this.currentTaskId = data.taskId;
      this.modalShow = true;
    },
    modalCancel() {
      this.modalShow = false;
    },
    async modalConfirm() {
      try {
        let res;
        if (this.listName === 'first') {
          res = await receivePlanOrder(this.currentTaskId);
        } else {
          res = await receiveOrder(this.currentTaskId);
        }
        this.modalShow = false;
        if (res.code === 200) {
          this.$refs.uToast.show(
            this.$setToastMsg({
              message: '接单成功',
              type: 'success',
            })
          );
          this.tableData = [];
          this.queryData.pageNum = 1;
          this.init();
        } else {
          this.$refs.uToast.show(
            this.$setToastMsg({
              message: '接单失败',
              type: 'error',
              position: 'top',
            })
          );
        }
      } catch (error) {}
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
</style>
