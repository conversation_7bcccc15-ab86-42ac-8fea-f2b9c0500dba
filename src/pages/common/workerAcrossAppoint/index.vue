<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="true"
      :leftText="leftText"
    ></top-navbar>
    <view>
      <uni-card
        shadow=" 0px 0px 30px 0px rgba(0,0,0,0.1)"
        spacing="12px"
        margin="12px 12px 0"
      >
        <u--form
          labelPosition="left"
          labelWidth="120"
          :model="appointInfo"
          :rules="rules"
          required
          ref="uForm"
        >
          <u-form-item label="申请人姓名" prop="applyerName" borderBottom>
            <u--input
              placeholder="请填写申请人姓名"
              v-model="appointInfo.applyerName"
              border="none"
              disabled
              disabledColor="#ffffff"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="申请人手机"
            prop="applyerPhone"
            required
            borderBottom
          >
            <u--input
              placeholder="请填写申请人手机"
              maxlength="11"
              v-model="appointInfo.applyerPhone"
              disabled
              border="none"
              disabledColor="#ffffff"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="人员通行区域"
            prop="applyDeptName"
            required
            @click="openAreaSelect()"
            borderBottom
          >
            <u--input
              placeholder="请填写通行区域"
              v-model="appointInfo.applyDeptName"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="车牌号"
            prop="carNumber"
            borderBottom
            @click="selectCar('number')"
          >
            <u--input
              disabled
              placeholder="请填写申请车牌号"
              v-model="appointInfo.carNumber"
              border="none"
              disabledColor="#ffffff"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="车辆类型"
            prop="carTypeName"
            borderBottom
            @click="selectCar('type')"
          >
            <u--input
              v-model="appointInfo.carTypeName"
              placeholder="请选择申请车辆类型"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="申请停车场"
            prop="parkCode"
            borderBottom
            @click="openParkSelect"
          >
            <u--input
              v-model="parkNames"
              placeholder="请选择停车场"
              disabled
              disabledColor="#ffffff"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="起始时间"
            prop="startDate"
            required
            borderBottom
            @click="selectTime('startDate')"
          >
            <u--input
              v-model="appointInfo.startDate"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择起始时间"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="结束时间"
            prop="endDate"
            required
            borderBottom
            @click="selectTime('endDate')"
          >
            <u--input
              v-model="appointInfo.endDate"
              disabled
              disabledColor="#ffffff"
              placeholder="请选择结束时间"
              border="none"
            ></u--input>
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>
          <u-form-item
            label="代为申请人手机"
            prop="substituteUserPhone"
            required
            borderBottom
            v-if="!isSysUser"
          >
            <u--input
              placeholder="请填写申请人手机"
              maxlength="11"
              v-model="appointInfo.substituteUserPhone"
              border="none"
              disabledColor="#ffffff"
            ></u--input>
          </u-form-item>
          <u-form-item label="备注" prop="remark">
            <u--textarea
              v-model="appointInfo.remark"
              placeholder="请输入内容"
              :count="true"
              :disabled="!!applyId"
              maxlength="50"
            ></u--textarea>
          </u-form-item>
        </u--form>
        <uni-row slot="actions" class="flex">
          <uni-col :span="10">
            <u-button
              type="info"
              text="取消"
              v-if="!applyId"
              @click="cancelInvitation"
            ></u-button>
          </uni-col>
          <uni-col :span="4"></uni-col>
          <uni-col :span="10">
            <u-button
              type="primary"
              text="流转记录"
              @click="lookWorkFlow"
              v-if="applyId && appointInfo.requestId"
            ></u-button>
            <u-button
              type="primary"
              v-if="!applyId"
              text="发起申请"
              @click="sendInvitation"
            ></u-button>
          </uni-col>
        </uni-row>
      </uni-card>
    </view>
    <u-overlay :show="plateShow">
      <plate-input
        v-if="plateShow"
        :carNoList.sync="appointInfo.carNos"
        :plate="plateNo"
        @export="setPlate"
        @close="plateShow = false"
        @deleteNo="deleteNo"
      />
    </u-overlay>

    <tki-tree
      ref="tkitree"
      :foldAll="true"
      :range="appointList"
      :multiple="true"
      rangeKey="areaName"
      idKey="areaId"
      @confirm="confirmArea"
      @cancel="cancelArea"
      cancelColor="#909193"
      confirmColor="#aa001e"
    />
    <u-datetime-picker
      :minDate="minDate"
      :show="showUserTime"
      v-model="useCarTime"
      mode="date"
      @close="showUserTime = false"
      @cancel="showUserTime = false"
      @confirm="timePanelConfirm"
    ></u-datetime-picker>
    <u-action-sheet
      :actions="typeRange"
      :show="showObjType"
      cancelText="取消"
      @select="typeChange"
      @close="showObjType = false"
    ></u-action-sheet>
    <!-- <u-action-sheet
      :actions="colorArray"
      :show="showObjColor"
      cancelText="取消"
      @select="colorChange"
      @close="showObjColor = false"
    ></u-action-sheet> -->
    <u-popup
      :show="showObjColor"
      :closeable="true"
      @close="showObjColor = false"
      :round="10"
    >
      <view class="popup_body">
        <view class="popTitle">选择停车场</view>
        <u-checkbox-group
          v-model="appointInfo.parkCodes"
          iconPlacement="right"
          placement="column"
          activeColor="#aa001e"
          @change="parkChange"
          :borderBottom="true"
        >
          <u-checkbox
            :customStyle="{ marginBottom: '8px' }"
            v-for="item in parkArray"
            :label="item.name"
            :name="item.value"
            :key="item.value"
          ></u-checkbox>
        </u-checkbox-group>
      </view>
    </u-popup>
    <u-popup
      :show="popupShow"
      mode="bottom"
      class="lyTreePopup"
      :closeOnClickOverlay="true"
      @close="popupShow = false"
      :customStyle="{ padding: '20rpx' }"
    >
      <ly-tree
        :tree-data="appointList"
        ref="lyTree"
        node-key="areaId"
        :checkOnClickNode="true"
        :showCheckbox="true"
        :expandOnClickNode="false"
        :highlightCurrent="true"
        :defaultExpandedKeys="defaultExpandedKeys"
        :childVisibleForFilterNode="true"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        :props="treeProps"
      ></ly-tree>
      <view class="btn-container">
        <u-button text="取消" style="width: 20%" @click="popupShow = false" />
        <u-button
          type="primary"
          text="确定"
          style="width: 20%"
          @click="confirmTree"
        />
      </view>
    </u-popup>
    <u-loading-page :loading="loading" loadingText="正在发出申请" />
  </view>
</template>

<script>
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format.js';
import tkiTree from '@/components/tki-tree/tki-tree.vue';
import { getDeptTree, getGateGuardTree } from '@/api/system/user.js';
import { passApply, applyInfo } from '@/api/aboutTask';
import { getInfo, getDictionaryQueries } from '@/api/login';
import LyTree from '@/components/ly-tree/ly-tree.vue';
import plateInput from '@/components/uni-plate-input/uni-plate-input.vue';
export default {
  name: 'AcrossAppoint',
  props: {},
  components: {
    tkiTree,
    LyTree,
    plateInput,
  },
  data() {
    return {
      minDate: Number(new Date()),
      showObjColor: false,
      plateNo: '',
      title: '',
      plateShow: false,
      popupShow: false,
      isSysUser: true, //判断是否为内部员工
      loading: false,
      treeProps: {
        label: 'areaName',
        disabled: (data, node) => {
          if (data.areaType != '5') {
            return true;
          }
        },
      },
      defaultExpandedKeys: [],
      autoBack: true,
      leftText: '',
      curFormItem: '',
      appointList: [],
      appointInfo: {
        applyerName: '',
        applyerPhone: '',
        parkCodes: [],
        applyDept: '',
        applyDeptName: '',
        startDate: '',
        endDate: '',
        carNos: [],
        carType: '',
        carTypeName: '',
        carNumber: '',
        remark: '',
        substituteUserPhone: '',
      },
      rules: {
        carTypeName: [
          {
            validator: (rule, value, callback) => {
              // 当存在车牌号时验证车辆类型
              if (this.appointInfo.carNos.length > 0) {
                return !!value && value.trim() !== '';
              }
              return true; // 无车牌号时不校验
            },
            message: '请选择车辆类型',
            // 触发器可以同时用blur和change
            trigger: ['change'],
          },
        ],

        applyDeptName: [
          {
            type: 'string',
            required: true,
            message: '请填写通行区域',
            trigger: ['change'],
          },
        ],
        startDate: [
          {
            type: 'string',

            required: true,
            message: '请选择起始时间',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return (
                this.appointInfo.endDate &&
                new Date(value).getTime() <
                  new Date(this.appointInfo.endDate).getTime()
              );
            },
            message: '起始时间不得晚于结束时间',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],

        endDate: {
          type: 'string',
          required: true,
          message: '请选择结束时间',
          trigger: ['blur', 'change'],
        },
        substituteUserPhone: [
          {
            type: 'number',
            max: 11,
            required: true,
            message: '请填写正确的手机号',
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],
      },
      showUserTime: false,
      applyId: true,
      useCarTime: null,
      typeRange: [],
      parkArray: [],
      parkNames: '',

      objId: '',
      showObjType: false,
      treeDataAreaIdArr: [],
      updateArray: {
        carType: '',
        carNos: [],
        areaIds: [],
        startDate: '',
        endDate: '',
        parkCodes: [],
        remark: '',
      },
    };
  },
  onLoad(option) {
    this.title = option.title;
    this.applyId = option.applyId;
    this.getAppointList();
    !this.applyId && this.GetInfo();
    this.getContractTypes('car_type', 'typeRange');
    this.getContractTypes('unregulated_parking_code', 'parkArray');
  },
  onShow() {
    this.applyId && this.getApplyInfo();
  },
  methods: {
    //查看详情
    async getApplyInfo() {
      try {
        const res = await applyInfo(this.applyId);
        const data = res.data;
        this.appointInfo = data;
        this.parkToName(this.appointInfo.parkCodes);
        this.appointInfo.applyerName = data.userName;
        this.appointInfo.applyerPhone = data.userPhone;
        this.isSysUser = !data.substituteUserPhone;
        if (data.areaList?.length) {
          let areaNameArr = [];
          res.data.areaList.forEach((item) => {
            areaNameArr.push(item.areaName);
          });
          this.appointInfo.applyDeptName = areaNameArr.toString();
        }
        this.appointInfo.carNumber = this.appointInfo.carNos?.length
          ? this.appointInfo.carNos.toString()
          : '';
        this.appointInfo.carTypeName = this.appointInfo.carType
          ? this.typeRange.filter(
              (item) => item.value == this.appointInfo.carType
            )[0].name
          : '';
      } catch (error) {
        console.log(error);
      }
    },
    //选择车牌or车辆类型
    selectCar(val) {
      if (this.applyId) return;
      if (val == 'number') this.plateShow = true;
      if (val == 'type') this.showObjType = true;
      this.hideKeyboard();
    },
    deleteNo() {
      this.appointInfo.carNumber = this.appointInfo.carNos.toString();
    },
    setPlate(plate) {
      if (plate.length >= 7) {
        this.appointInfo.carNos.push(plate);
        this.plateShow = false;
        this.appointInfo.carNumber = this.appointInfo.carNos.toString();
      }
    },
    confirmTree() {
      //(leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
      this.treeDataAreaIdArr = [];
      let treeData = this.$refs.lyTree.getCheckedNodes(true);
      if (treeData.length > 0) {
        let areaNameArr = [];
        let areaIdArr = [];
        treeData.forEach((item) => {
          areaNameArr.push(item.areaName);
          areaIdArr.push(item.areaId);
        });
        this.appointInfo.applyDeptName = areaNameArr.toString();
        this.treeDataAreaIdArr = areaIdArr;
      }
      this.popupShow = false;
    },

    handleNodeClick() {},
    //查询过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    async getContractTypes(query, array) {
      try {
        const res = await getDictionaryQueries(query);
        this[array] = [];
        if (res.data) {
          const types = res.data;
          this[array] = types.map((item) => ({
            name: item.dictLabel,
            value: item.dictValue,
          }));
        }
      } catch (err) {}
    },
    typeChange(obj) {
      this.appointInfo.carTypeName = obj.name;
      this.appointInfo.carType = obj.value;
      this.showObjType = false;
    },
    //打开停车场
    openParkSelect() {
      if (this.applyId) return;
      this.hideKeyboard();
      this.showObjColor = true;
    },
    //停车场选择
    parkChange(val) {
      const names = val.reduce((pre, next) => {
        const filters = this.parkArray.filter((item) => item.value == next);
        if (filters?.length) pre.push(filters[0].name);

        return pre;
      }, []);
      this.parkNames = names.join(',');
    },
    //停车场选择
    parkToName(val) {
      val && this.parkChange(val);
    },
    //选择时间
    selectTime(val) {
      if (this.applyId) return;
      this.hideKeyboard();
      this.showUserTime = true;
      this.curFormItem = val;
    },
    async GetInfo() {
      try {
        const res = await getInfo();
        const { user } = res.data;
        this.appointInfo.applyerName = user.userName;
        this.appointInfo.applyerPhone = user.phonenumber;
        this.isSysUser = user.userType == 'sys_user';
      } catch (error) {}
    },
    /*获取部门树形集合*/
    async getAppointList() {
      try {
        const res = await getGateGuardTree();
        this.appointList = res.data;
      } catch {}
    },
    cancelInvitation() {
      uni.navigateBack();
    },
    /* 打开选择部门组件*/
    openAreaSelect() {
      if (this.applyId) return;
      this.hideKeyboard();
      this.popupShow = true;
    },
    confirmArea(e) {
      if (e?.length) {
        this.appointInfo.applyDeptName = e[0].label;
        this.appointInfo.applyDept = e[0].id || '';
      }
      this.$refs.tkitree._hide();
    },
    cancelArea() {
      this.$refs.tkitree._hide();
    },
    sendInvitation() {
      this.$refs.uForm
        .validate()
        .then(async (res) => {
          this.loading = true;

          if (res) {
            // 创建提交数据对象
            const submitData = {};
            // 映射公共字段
            Object.keys(this.updateArray).forEach((key) => {
              if (this.appointInfo.hasOwnProperty(key)) {
                // 处理数组类型字段的拷贝
                if (Array.isArray(this.appointInfo[key])) {
                  submitData[key] = [...this.appointInfo[key]];
                } else {
                  submitData[key] = this.appointInfo[key];
                }
              }
            });

            // 添加门岗ID数组
            if (this.treeDataAreaIdArr.length > 0) {
              submitData.areaIds = [...this.treeDataAreaIdArr];
            }
            const rep = await passApply(submitData);
            this.loading = false;
            if (rep.code === 200) {
              this.$modal.msgSuccess('申请成功');
              setTimeout(() => {
                uni.navigateBack();
              }, 1000);
            } else {
              this.$modal.msgError('申请失败');
            }
          }
        })
        .catch((errors) => {
          this.loading = false;
        });
    },
    //查看流转记录
    lookWorkFlow() {
      this.appointInfo?.requestId &&
        uni.navigateTo({
          url: `/pages/common/acrossAppointRecode/workFlow?requestId=${this.appointInfo.requestId}`,
        });
    },
    timePanelConfirm(e) {
      this.appointInfo[this.curFormItem] = formatDate(e.value, 'yyyy-MM-dd');
      this.showUserTime = false;
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
::v-deep .u-textarea--disabled {
  background-color: #fff;
}
.middle-container {
  padding-top: 0;
}
.popup_body {
  padding: 100rpx 64rpx;

  position: relative;
  .popTitle {
    color: #aa001e;
    position: absolute;
    top: 30rpx;
    left: 30rpx;
    font-size: $uni-font-size-lg;
  }
  .popInfo {
    color: $uni-text-color;
    text-indent: 2em;
  }
}
</style>
