<template>
    <u-sticky offset-top="0" bgColor="#ffff">
        <u-row gutter="10" class="row-container">
            <u-col span="5">
                <u-row gutter="10">
                    <u-col span="6">
                        <uni-tooltip content="发起人" placement="bottom">
                            <u-tag :text="promoter" plain > </u-tag>
                        </uni-tooltip>
                    </u-col>
                    <u-col span="6">
                        <uni-tooltip content="任务节点">
                            <u-tag :text="taskNode" plain > </u-tag>
                        </uni-tooltip>
                    </u-col>
                </u-row>
            </u-col>
            <u-col span="7">
                <u-row justify="end">
                    <u-button
                        v-for="item in btnList"
                        :key="item.dictValue"
                        type="primary"
                        :text="item.dictLabel"
                        @click="btnClick(item)"
                    >
                    </u-button>
                </u-row>
            </u-col> 
        </u-row>
    </u-sticky>
</template>

<script>
    export default {
        name: "GreyTitle",
        components: {},
        props: {
            promoter: {
                type: String
            },
            taskNode: {
                type: String
            },
            btnList: {
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
            };
        },
        onLoad() {
        },
        onShow() {
        },
        methods: {
            btnClick(item) {
                this.$emit('btnClick', item.dictValue, item.dictLabel)
            }
        }
    };
</script>

<style lang="scss" scoped>
.row-container {
    height: 58px;
    padding: 0 15px;
}
::v-deep .u-tag {
    display: inline-block;
    width: max-content;
}
.u-button {
    width: auto;
    margin: 0 0 0 15px
}
</style>