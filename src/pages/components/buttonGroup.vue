<template>
	<u-grid :border="false" :col="buttonList.length" :class="sectionBtn ? 'sectionBtn' : ''">
		<u-grid-item v-for="item in buttonList" :key="item.value">
			<u-button :type="currentValue === item.value ? 'primary' : 'info'" :text="item.label" @click="handleButtonClick(item.value)" />
		</u-grid-item>
	</u-grid>
</template>

<script>
export default {
	name: 'Name',
	components: {},
	props: {
		buttonList: {
			type: [Array, Object],
			default: () => []
		},
		currentValue: {
			type: [Number, String]
		},
		sectionBtn: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {};
	},
	onLoad() {},
	onShow() {},
	methods: {
		handleButtonClick(value) {
			this.$emit('handleButtonClick', value);
		}
	}
};
</script>

<style lang="scss" scoped>
.u-button {
	margin-bottom: 10px;
}
</style>
