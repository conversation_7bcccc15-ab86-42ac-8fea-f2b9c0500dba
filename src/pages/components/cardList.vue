<template>
    <u-list v-if="cardList.length" @scrolltolower="scrolltolower">
        <u-list-item v-for="(itemData, index) in cardList" :key="index">
            <slot :itemData="itemData"></slot>
        </u-list-item>
    </u-list>
</template>

<script>
    export default {
        name: "Name",
        components: {},
        props: {
            cardList: {
                type: [Array],
                default: () => []
            }
        },
		watch:{
			cardList(val){
			}
		},
        data() {
            return {
            };
        },
        onLoad() {
        },
        onShow() {
        },
        methods: {
            scrolltolower() {
                this.$emit("scrolltolower")
            }
        }
    };
</script>

<style lang="scss" scoped>
</style>