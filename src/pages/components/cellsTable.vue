<template>
  <view class="cells-table">
    <u-row customStyle="margin-top: 10px;background-color:#00000005">
      <u-col :span="item.span|| 2" v-for="(item, index) in titleList" :key="index">
        <u-cell :title="item.label"></u-cell>
      </u-col>
    </u-row>
    <u-row v-for="(item, index) in tableList" :key="index">
      <u-col :span="innerItem.span|| 2" v-for="(innerItem, innerIndex) in titleList" :key="innerIndex">
        <u-cell v-if="innerItem.value==='photoUrl'">
          <u-avatar
            slot="icon"
            shape="circle"
            size="25"
            :src="item[innerItem.value]"
          ></u-avatar>
        </u-cell>
        <u-cell v-else :title="item[innerItem.value] || '无'">
        </u-cell>
      </u-col>
    </u-row>
  </view>
</template>

<script>
export default {
  props: {
    titleList: {
      type: Array,
      default: () => []
    },
    tableList: {
      type: Array,
      default: () => []
    },
    hasOperation: {
      type: Boolean,
      default: false
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.cells-table {
  ::v-deep .u-cell {
    .u-cell__body {
      padding: 10px 6px !important;
      height: 45px;
    }
  }
}
</style>