<template>
  <view class="bgCard middle-container">

    <slot name="topSlot"></slot>
    <view v-if="showTodayRecord" class="flex align_center mb-4 px-4" style="align-items: center;">
      <!-- <u-icon :name="iconName" size="20" color="#aa011c"></u-icon> -->
      <view style="width: 4px;
          height: 16px;
          background: #AA011C;
          border-radius: 2px;
          margin-right:30rpx;
          "></view>
      <text class="font-16 font-bold ml-2" style="color: #333333;line-height:20px;font-weight:400;">{{ title }}</text>
    </view>
    <view :class="showSearch || showListSearch ? 'show-search' : ''">
      <view style="border-radius: 100px; margin: 0 24rpx" v-if="showSearch" class="flex shadow">
        <u-search :showAction="true" clearabled v-model="keyword" actionText="搜索" :animation="true" bgColor="#fff"
          @search="serachOperate" @custom="serachOperate" @clear="serachOperate" />
      </view>

      <view :class="showTabs ? 'show-tabs' : ''">
        <u-tabs class="tabothers"  lineWidth="0" v-if="gateTypeList.length>0" :list="gateTypeList" :current="currentIndex" @change="changeGateTab" :activeStyle="{
          color: '#AA011C',
          transform: 'scale(1.2)'
        }" :inactiveStyle="{ color: '#999999' }"></u-tabs>
        <u-tabs  v-if="showTabs" :list="topTab" :current="currentIndex" @change="changeTab" :activeStyle="{
          color: '#AA011C',
        }" :inactiveStyle="{ color: '#999999' }"></u-tabs>
        <view style="border-radius: 100px; margin: 10rpx 24rpx" v-if="showListSearch" class="flex shadow">
          <u-search :showAction="true" clearabled v-model="keyword" actionText="搜索" :animation="true" bgColor="#fff"
            @search="serachOperate" @custom="serachOperate" @clear="serachOperate" />
        </view>
        <uni-data-select v-if="showSelect" v-model="selectValue" :localdata="topSelectList" placeholder="请选择任务状态"
          @change="changeSelect" />
      </view>

      <card-list class="card-list" v-if="tableData.length" :cardList="tableData" @scrolltolower="scrolltolower">
        <template #default="slotProps">
          <slot :itemData="slotProps.itemData"></slot>
        </template>
      </card-list>
      <u-empty width="120" height="120" marginTop="20" v-else mode="list" icon="/static/images/iconImg/empty.png" />
    </view>
  </view>
</template>

<script>
import CardList from '@/pages/components/cardList.vue';
export default {
  name: 'ContainTab',
  components: {
    CardList,
  },
  props: {
    showTabs: {
      type: Boolean,
      default: false,
    },
    topTabList: {
      type: [Array],
      default: () => [],
    },
    showSelect: {
      type: Boolean,
      default: false,
    },
    topSelectList: {
      type: Array,
      default: () => [],
    },
    gateTypeList:{
      type:Array,
      default: () => [],
    },
    tableData: {
      type: [Array],
      default: () => [],
    },
    current: {
      type: [Number, String],
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
    showListSearch: {
      type: Boolean,
      default: false,
    },
    showTodayRecord: {
      type: Boolean,
      default: false
    },
    iconName: {
      type: String,
      default: 'calendar'
    },
    title: {
      type: String,
      default: '今日记录'
    }
  },
  data() {
    return {
      tableList: [],
      keyword: '',
      topTab: [],
      selectValue: null,
    };
  },
  computed: {
    currentIndex: {
      get() {
        return this.current;
      },
      set(val) { },
    },
  },
  watch: {
    tableData(val) {
      this.tableList = val;
    },
    topTabList: {
      immediate: true,
      handler(val) {
        this.topTab = val;
      },
    },
  },

  methods: {
    changeTab(e) {
      this.$emit('changeCurrent', e.keyName);
    },
     changeGateTab(e) {
      console.log(e);
      this.$emit('changeGateCurrent', e.keyName);
    },
    changeSelect(e) {
      this.$emit('changeCurrent', e);
    },
    serachOperate(val) {
      this.$emit('serching', this.keyword);
    },
    scrolltolower(val) {
      this.$emit('scrolltolower', val);
    },
  },
};
</script>

<style lang="scss" scoped>
.show-tabs {
  margin-top: 10rpx;
}

.show-search {
  padding: 24rpx 0 0;
}

.middle-container {
  padding-top: 0px;
}

.shadow {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}

</style>
