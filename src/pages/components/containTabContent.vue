<template>
  <view class="bgCard middle-container">
    <slot name="topSlot"></slot>
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="handleScrollToLower"
      lower-threshold="50"
    >
    <view
      v-if="showTodayRecord"
      class="flex align_center mb-4 px-4"
      style="align-items: center"
    >
      <view
        style="
          width: 4px;
          height: 16px;
          background: #aa011c;
          border-radius: 2px;
          margin-right: 30rpx;
        "
      ></view>
      <text
        class="font-16 font-bold ml-2"
        style="color: #333333; line-height: 20px; font-weight: 400"
        >{{ title }}</text
      >
    </view>
    <u-sticky bgColor="#fff">
      <view :class="showSearch || showListSearch ? 'show-search' : ''">
        <view
          style="border-radius: 100px; margin: 0 24rpx"
          v-if="showSearch"
          class="flex shadow"
        >
          <u-search
            :showAction="true"
            clearabled
            v-model="keyword"
            actionText="搜索"
            :animation="true"
            bgColor="#fff"
            @search="serachOperate"
            @custom="serachOperate"
            @clear="serachOperate"
            @focus="searchFocus"
            @blur="searchBlur"
          />
        </view>

        <view :class="showTabs ? 'show-tabs' : ''">
          <u-tabs
            v-if="gateTypeList.length"
            :list="gateTypeList"
            :current="currentIndex"
            @change="changeTab"
            :activeStyle="{
              color: '#AA011C',
            }"
            :inactiveStyle="{ color: '#999999' }"
          ></u-tabs>
          <u-tabs
            v-if="showTabs"
            :list="topTab"
            :current="currentIndex"
            @change="changeTab"
            :activeStyle="{
              color: '#AA011C',
            }"
            :inactiveStyle="{ color: '#999999' }"
          ></u-tabs>
          <view
            style="border-radius: 100px; margin: 10rpx 24rpx"
            v-if="showListSearch"
            class="flex shadow"
          >
            <u-search
              :showAction="true"
              clearabled
              v-model="keyword"
              actionText="搜索"
              :animation="true"
              bgColor="#fff"
              @search="serachOperate"
              @custom="serachOperate"
              @clear="serachOperate"
              @focus="searchFocus"
              @blur="searchBlur"
            />
          </view>
          <uni-data-select
            v-if="showSelect"
            v-model="selectValue"
            :localdata="topSelectList"
            placeholder="请选择任务状态"
            @change="changeSelect"
          />
        </view>
      </view>
    </u-sticky>
    <card-list
      class="card-list"
      v-if="tableData.length"
      :cardList="tableData"
      @scrolltolower="scrolltolower"
      :key="cardListKey"
    >
        <template #default="slotProps">
          <slot :itemData="slotProps.itemData"></slot>
        </template>
      </card-list>
    <!-- <view v-if="tableData.length">
      <view v-for="item in tableData" :key="item.id" style="height: 200px">
        <uni-card @click="goAppointInfo(item)">
          <view class="flex" style="justify-content: space-between">
            <view>
              <view class="flex">
                <view
                  style="
                    font-size: 48rpx;
                    color: #333333;
                    line-height: 48rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    max-width: 200rpx;
                  "
                >
                  {{ item.visitorName }}
                </view>
                <view
                  v-if="item.carNo"
                  style="
                    font-size: 30rpx;
                    color: #333333;
                    line-height: 30rpx;
                    margin-left: 10rpx;
                  "
                  class="carno-over"
                >
                  {{ item.carNo }}
                </view>
                <view
                  class="flex"
                  style="
                    color: #999999;
                    margin-left: 30rpx;
                    font-size: 24rpx;
                    line-height: 24rpx;
                    margin-top: 4rpx;
                  "
                >
                  <view style="margin-right: 30rpx"> 访 </view>
                  <view>
                    {{ item.intervieweeDept }}
                  </view>
                </view>
              </view>
              <view style="color: #333333; margin-top: 18rpx; font-size: 28rpx">
                {{ item.visitTime }}
              </view>
            </view>

            <view
              class="u-tag-diy-wapper"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
  
            </view>
          </view>
        </uni-card>
      </view>
    </view> -->
      <u-empty
        width="120"
        height="120"
        marginTop="20"
        v-else
        mode="list"
        icon="/static/images/iconImg/empty.png"
      />
    </scroll-view>
  </view>
</template>

<script>
import CardList from '@/pages/components/cardList.vue';
export default {
  name: "ContainTab",
    components: {
    CardList,
  },
  props: {
    showTabs: {
      type: Boolean,
      default: false,
    },
    topTabList: {
      type: [Array],
      default: () => [],
    },
    showSelect: {
      type: Boolean,
      default: false,
    },
    topSelectList: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: [Array],
      default: () => [],
    },
    current: {
      type: [Number, String],
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
    showListSearch: {
      type: Boolean,
      default: false,
    },
    showTodayRecord: {
      type: Boolean,
      default: false,
    },
    iconName: {
      type: String,
      default: "calendar",
    },
    title: {
      type: String,
      default: "今日记录",
    },
    gateTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableList: [],
      keyword: "",
      topTab: [],
      selectValue: null,
      cardListKey: 0, // 用于强制重新渲染CardList组件
    };
  },
  computed: {
    currentIndex: {
      get() {
        return this.current;
      },
      set(val) {},
    },
  },
  watch: {
    tableData: {
      handler(val) {
        this.tableList = val;
        // 当数据变化时，强制重新渲染CardList组件
        this.cardListKey++;
      },
      deep: true
    },
    topTabList: {
      immediate: true,
      handler(val) {
        this.topTab = val;
      },
    },
  },

  methods: {
    changeTab(e) {
      this.$emit("changeCurrent", e.keyName);
    },
    changeSelect(e) {
      this.$emit("changeCurrent", e);
    },
    serachOperate(val) {
      this.$emit("serching", this.keyword);
    },
    scrolltolower(val) {
      this.$emit("scrolltolower", val);
    },
    searchFocus() {
      // 强制重新渲染CardList组件
      this.cardListKey++;
      // this.$emit("searchFocus", true);
    },
    searchBlur() {
      // 强制重新渲染CardList组件
      this.cardListKey++;
      // this.$emit("searchBlur", false);
    },
    // 手动触发CardList重新渲染的方法
    forceUpdateCardList() {
      this.cardListKey++;
    },
    // 处理滚动到底部事件
    handleScrollToLower() {
      this.scrolltolower();
    },
  },
};
</script>

<style lang="scss" scoped>
.show-tabs {
  margin-top: 10rpx;
}

.show-search {
  padding: 24rpx 0 0;
}

.middle-container {
  padding-top: 0px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.shadow {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}

/* 滚动容器样式 */
.scroll-container {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 自定义滚动条样式 */
.scroll-container::-webkit-scrollbar {
  width: 6px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保粘性头部不影响滚动 */
.u-sticky {
  z-index: 999;
}
</style>
