<template>
  <view>
    <u-row v-for="(item, index) in labelList" :key="index">
      <u-col :span="item.span ? item.span : 4">
        <u--text size="18" :text="item.label" />
      </u-col>
      <u-col :span="item.span ? 12 - item.span : 9">
        <u--text size="18" v-if="itemInfo[item.value]" :text="itemInfo[item.value]" />
        <u--text size="18" v-else-if="itemInfo[item.value]===0" :text="itemInfo[item.value]" />
        <u--text size="18" v-else text="无" />
      </u-col>
    </u-row>
  </view>
</template>

<script>
export default {
  props: {
		itemInfo: {
			default: () => {},
			type: [Object]
		},
		labelList: {
			default: () => [],
			type: [Array]
		},
	},
  data() {
		return {};
	},
}

</script>
<style lang="scss" scoped>
.u-row {
  height: 3.5rem;
}
</style>