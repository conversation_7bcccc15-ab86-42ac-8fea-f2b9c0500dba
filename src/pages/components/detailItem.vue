<template>
	<uni-section :title="cardTitle" type="line" class="workSection">
		<uni-card @click="handleDetail()">
			<slot name="top"></slot>
			<template v-if="listCard.length">
				<u-row v-for="(item, index) in listCard" :key="index" :class="{ 'image-class': item.isImage }">
					<u-col :span="item.span ? item.span : 5">
						<u--text type="info" lineHeight="1.875rem" :text="`${item.label}`" />
					</u-col>
					<u-col :span="item.span ? 12 - item.span : 7" :class="{ 'tag-cls': item.preStatus }">
						<template v-if="itemData[item.value]">
							<template v-if="item.preHandle">
								<u--text lineHeight="1.875rem" :text="item.preHandle(itemData[item.value])" />
							</template>
							<template v-else-if="item.isFee">
								<u--text lineHeight="1.875rem" mode="price" :text="`${itemData[item.value]}¥`" />
							</template>
							<template v-else-if="item.isImage">
								<u-album v-if="Array.isArray(itemData.url)" :urls="itemData.url" rowCount="2" keyName="url"></u-album>
								<u-image v-else :showLoading="true" :src="`${itemData.url}`" mode="aspectFit" width="160px" height="80px" @click="click"></u-image>
							</template>
							<template v-else-if="item.preStatus">
								<u-tag :text="item.preStatus(itemData[item.value]).label" :type="item.preStatus(itemData[item.value]).type"></u-tag>
							</template>
							<template v-else>
								<u--text lineHeight="1.875rem" :text="`${itemData[item.value]}`" />
							</template>
						</template>
						<template v-else-if="item.isImage">
							<u-empty mode="list" width="80" text="暂无图片" icon="/static/images/tabbar/noimage.png" />
						</template>
						<template v-else>
							<u--text lineHeight="1.875rem" text="无" />
						</template>
					</u-col>
				</u-row>
			</template>
			<slot name="bottom"></slot>
		</uni-card>
	</uni-section>
</template>

<script>
export default {
	name: 'Name',
	props: {
		itemData: {
			default: () => {},
			type: [Object]
		},
		listCard: {
			default: () => [],
			type: [Array]
		},
		cardTitle: {
			default: String,
			type: ''
		}
	},
	data() {
		return {};
	},
	onLoad() {},
	onShow() {},
	methods: {
		handleDetail() {
			this.$emit('handleDetail', this.itemData);
		}
	}
};
</script>

<style lang="scss" scoped>
.image-class {
	align-items: flex-start !important;
}
.uni-card {
	margin: 15px 0 !important;
}
.u-empty {
	display: flex;
	align-items: flex-start;
}
.tag-cls {
	display: contents;
}
</style>
