<template>
  <view>
    <u-form
      labelPosition="left"
      labelWidth="40%"
      :model="uFormModel"
      :rules="uFormRules"
      ref="uForm"
      @submit.prevent
    >
      <u-form-item
        v-for="item in formItems"
        :key="item.fieldId"
        :label="item.fieldName"
        :prop="item.fieldCode"
        :required="item.isMust === '1'"
      >
        <!-- <u--input
          :placeholder="99"
          maxlength="50"
          v-model="modelValue"
        >
      </u--input> -->
        <component
          :is="fieldComponent(item.fieldType)"
          :fieldProps="item"
          @update:modelValue="handleUpdate"
        />
      </u-form-item>
    </u-form>
  </view>
</template>

<script>
// 引入各种表单组件
import TextField from '@/components/formFields/textField.vue';
import RadioField from '@/components/formFields/radioField.vue';
import UploadField from '@/components/formFields/uploadField.vue';
import ConfirmField from '@/components/formFields/confirmField.vue';
import CheckboxField from '@/components/formFields/checkboxField.vue';
import DatetimepickerField from '@/components/formFields/datetimepickerField.vue';
import DatepickerField from '@/components/formFields/datepickerField.vue';
import formFieldLoader from '@/utils/formFieldLoader';
import { getFieldRule } from '@/utils/formFieldLoader';
export default {
  name: 'DynamicForm',
  components: {
    TextField,
    RadioField,
    UploadField,
    ConfirmField,
    CheckboxField,
    DatetimepickerField,
    DatepickerField,
  },
  props: {
    formItems: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      uFormModel: {},
      uFormRules: {},
    };
  },
  onLoad() {},
  methods: {
    fieldComponent(type) {
      // 根据传入的表单类型动态加载组件
      return formFieldLoader.getFieldComponent(type);
    },
    handleUpdate(value, code) {
      this.uFormModel[code] = value;
      this.formItems.forEach((item) => {
        if (item.fieldCode === code) {
          item.fieldValue = value;
        }
      });
    },
    // 表单项
    getFormInfo() {
      this.formItems.forEach((element) => {
        this.uFormModel[element.fieldCode] = element.fieldValue;
      });
    },
    getFormRules() {
      for (const key in this.uFormModel) {
        if (Object.prototype.hasOwnProperty.call(this.uFormModel, key)) {
          let item = this.formItems.find((item) => item.fieldCode === key);
          this.uFormRules[key] = getFieldRule(item);
        }
      }
      this.$refs.uForm.setRules(this.uFormRules);
    },
    validateForm(fn) {
      this.$refs.uForm.validate().then(
        (res) => {
          if (res) {
            fn(this.formItems);
          }
        },
        (err) => {}
      );
    },
  },
};
</script>

<style lang="scss" scoped></style>
