<template>
	<u-popup class="fullPopup" :mode="mode" :show="show" :showCancelButton="showCancelButton" :showConfirmButton="showConfirmButton" @confirm="confirm" @cancel="cancel">
		<slot></slot>
		<view class="btn_box">
			<u-button v-if="showConfirmButton" type="primary" plain text="确认" @click="confirm" class="detail_btn"></u-button>
			<u-button v-if="showCancelButton" text="取消" @click="cancel" class="cancel_btn"></u-button>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'FullPopup',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: ''
		},
		showCancelButton: {
			type: Boolean,
			default: false
		},
		showConfirmButton: {
			type: Boolean,
			default: true
		},
		mode: {
			type: String,
			default: 'right'
		}
	},
	data() {
		return {};
	},

	mounted() {},
	onShow() {},
	methods: {
		confirm() {
			this.$emit('confirm');
		},
		cancel() {
			this.$emit('cancel');
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .u-modal__content {
	flex-direction: column;
}
::v-deep.u-popup__content {
	height: 90%;
}
.btn-cls {
	display: flex;
}
::v-deep .u-transition {
	overflow: hidden;
	overflow-y: auto;
	position: fixed;
	width: 100%;
}
</style>
