<template>
    <u-row>
        <u--text class="title-cls" align="center" :text="titleText"></u--text>
    </u-row>
</template>

<script>
    export default {
        name: "GreyTitle",
        components: {},
        props: {
            titleText: {
                type: String,
            }
        },
        data() {
            return {
            };
        },
        onLoad() {
        },
        onShow() {
        },
        methods: {
        }
    };
</script>

<style lang="scss" scoped>
.title-cls {
    color: #909193;
    background-color: #f3f4f6;
    padding: 15px;
    font-size: 15px;
}
</style>