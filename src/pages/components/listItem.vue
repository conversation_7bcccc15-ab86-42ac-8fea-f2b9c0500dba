<template>
    <uni-card :title="cardTitle" @click="handleDetail()">
        <uni-row>
            <uni-col :span="12" v-for="(item,index) in listCard" :key="index">
                <template v-if="item.preHandle">
                    <u--text v-if="item.isUppercase" :size="20" :text="item.preHandle(itemData[item.value])"></u--text>
                    <u--text v-else-if="itemData[item.value]&&item.isFee" lineHeight="1.875rem" type="info" :text="`${itemData[item.value]}¥`"></u--text>
                    <u-text v-else-if="itemData[item.value]" type="info" lineHeight="1.875rem" :block="true" :text="`${itemData[item.value]}`" />
                    <u-text v-else type="info" lineHeight="1.875rem" :block="true" text="无" />
                </template>
                <template v-else>
                    <u--text v-if="item.isUppercase" :size="20" :text="`${itemData[item.value]}`"></u--text>
                    <u--text v-else-if="itemData[item.value]&&item.isFee" lineHeight="1.875rem" type="info" :text="`${itemData[item.value]}¥`"></u--text>
                    <u-text v-else-if="itemData[item.value]" type="info" lineHeight="1.875rem" :block="true" :text="`${itemData[item.value]}`" />
                    <u-text v-else type="info" lineHeight="1.875rem" :block="true" text="无" />
                </template>
            </uni-col>
        </uni-row>
        <slot></slot>
    </uni-card>
</template>

<script>
    export default {
        name: "Name",
        props: {
            itemData: {
                default: () => {},
                type: [Object]
            },
            listCard: {
                default: () => [],
                type: [Array]
            },
            cardTitle: {
                default: String,
                type: ''
            }
        },
        data() {
            return {
            };
        },
        onLoad() {
        },
        onShow() {
        },
        methods: {
            handleDetail() {
                this.$emit("handleDetail", this.itemData)
            }
        }
    };
</script>

<style lang="scss" scoped>
.uni-col {
    padding: 10px 0;
}
.uni-card {
    margin: 15px 0 !important;
}
</style>