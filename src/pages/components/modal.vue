<template>
    <u-modal
        :show="show"
        :width="width"
        :title="title"
        :showCancelButton="showCancelButton"
        :showConfirmButton="showConfirmButton"
        :confirmText="confirmText"
        :confirmColor="confirmColor"
        :asyncClose="asyncClose"
        @confirm="confirm"
        @cancel="cancel"
    >
        <slot></slot>
    </u-modal>
</template>

<script>
export default {
    name: 'modal',
    props: {
        width: {
            type: String,
            default: '300px'
        },
        show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        confirmText: {
            type: String,
            default: '确认'
        },
        confirmColor: {
            type: String,
            default: '#aa001e'
        },
        showCancelButton: {
            type: Boolean,
            default: true
        },
        showConfirmButton: {
            type: Boolean,
            default: true
        },
        asyncClose: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        }
    },

    mounted() {},
    onShow() {},
    methods: {
        confirm() {
            this.$emit('confirm')
        },
        cancel() {
            this.$emit('cancel')
        }
    }
};
</script>

<style lang="scss" scoped>

</style>