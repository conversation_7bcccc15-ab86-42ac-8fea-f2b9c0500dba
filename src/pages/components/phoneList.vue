<template>
  <view>
      <u-list
        style="height: auto;"
      >
        <slot></slot>
        <u-list-item
          class="user"
          v-for="(item, index) in list"
          :key="index"
        >
          <view class="user-name">
            <uni-tooltip :content="item.userName" placement="top">
              <view class="user-name__inner">{{ item.userName }}</view>
            </uni-tooltip>
          </view>
          <view class="user-name ancestors-name">
            <uni-tooltip :content="item.nickName" placement="top">
              <view class="user-name__inner">{{ item.nickName }}</view>
            </uni-tooltip>
          </view>
          <view class="user-number"
            @click.stop="makePhone(item.phonenumber)">
            <view v-show="item.phonenumber">
              <i class="iconfont icon-Phone" style="color:#4cd964;font-size:16px;margin-right:10upx;" />
            </view>

            <view>{{ item.phonenumber || '无电话信息' }}</view>
          </view>
        </u-list-item>
      </u-list>
  </view>
</template>

<script>

export default {
  components: {
  },
  props: {
    list: {
      type: Array,
    }
  },
  data() {
    return {
    }
  },
  onLoad() {
  },
  methods: {
    makePhone(value) {
      this.$emit('makePhone', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  height: 120upx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 20upx;
  padding: 0 20upx;
  border-bottom: 1upx solid #C0C0C0;

  .user-name {
    font-size:18px;
    display:flex;
    flex: 1;
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .user-name__inner {
      width: 100px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .ancestors-name {
    font-size:12px;
    color:#7f7f7ffe;
    display:flex;
    align-items:center
  }
  .user-number {
    font-size: 16px;
    display: flex;
  }

}
</style>
