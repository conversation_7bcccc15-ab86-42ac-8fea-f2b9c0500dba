<template>
  <u-popup
    :show="showPicPreview"
    @close="close"
    @open="open"
    mode="center"
    background-color="raba(0,0,0,0)"
  >
    <view class="previewBody">
      <u-icon
        name="close-circle"
        class="closeIcon"
        color="#fff"
        @tap="closePicPreview"
        size="45"
      ></u-icon>
      <movable-area scale-area>
        <movable-view
          direction="all"
          @scale="onScale"
          sacle="true"
          scale-min="1"
          scale-max="4"
          scale-value="1"
        >
          <image :src="previewUrl" mode="widthFix"></image>
        </movable-view>
      </movable-area>
    </view>
  </u-popup>
</template>
<script>
export default {
  data() {
    return {
      showPicPreview: false,
      previewUrl: '',
      resolve: '',
      reject: '',
      promise: '',
    };
  },
  // props: {
  //   showPicPreview: {
  //     type: Boolean,
  //     require: true,
  //     default: false,
  //   },
  //   previewUrl: {
  //     tyep: String,
  //     default: '',
  //   },
  // },
  methods: {
    close() {},
    open(val) {
      this.showPicPreview = true;
      const that = this;
      this.promise = new Promise((resolve, reject) => {
        that.resolve = resolve;
        that.reject = reject;
        resolve();
      });
      // 返回promise对象
      return this.promise;
    },
    onScale() {},
    closePicPreview() {
      //this.resolve(22);
      if (this.showPicPreview == false) return;
      this.showPicPreview = false;
      document.body.removeChild(this.$el);
      this.$closePreview();
      this.$emit('closePicPreview', false);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .u-popup__content {
  background-color: unset;
}
.previewBody {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 9999;
  .closeIcon {
    position: absolute;
    right: 45rpx;
    top: 120rpx;
    z-index: 99;
  }
  movable-view {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  movable-area {
    width: 100%;
    height: 100%;
    position: fixed;
    overflow: hidden;
  }
  movable-view image {
    width: calc(100% - 300rpx);
  }
}
</style>
