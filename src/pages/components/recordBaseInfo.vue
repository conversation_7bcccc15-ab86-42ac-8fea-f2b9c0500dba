<template>
	<u--form :labelPosition="labelPosition" :labelWidth="labelWidth" :model="fromModel" :rules="fromRules" ref="uForm">
		<u-form-item label="任务名称" :borderBottom="borderBottom">
			<u--input v-model="fromModel.taskName" disabled placeholder="" border="none"></u--input>
		</u-form-item>
		<u-form-item label="要求完成时间" :borderBottom="borderBottom">
			<u--input v-model="fromModel.requireFinishTime" disabled placeholder="" border="none"></u--input>
		</u-form-item>
		<u-form-item label="执行时长" :borderBottom="borderBottom">
			<u--input v-model="fromModel.executionDuration" style="flex: 0.5" disabled placeholder="" border="none">
				<template slot="suffix">小时</template>
			</u--input>
		</u-form-item>
		<u-form-item label="任务内容" :borderBottom="borderBottom">
			<u--textarea v-model="fromModel.taskContent" disabled autoHeight border="none" placeholder=""></u--textarea>
		</u-form-item>
	</u--form>
</template>

<script>
export default {
	name: 'BaseInfo',
	components: {},
	props: {
		labelPosition: {
			type: String,
			default: 'left'
		},
		labelWidth: {
			type: [String, Number],
			default: 100
		},
		borderBottom: {
			type: Boolean,
			default: false
		},
		fromModel: {
			type: Object,
			default: () => {}
		},
		fromRules: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {};
	},
	onLoad() {},
	onShow() {},
	methods: {}
};
</script>

<style lang="scss" scoped></style>
