<template>
    <uni-card>
        <u--form
            labelPosition="left"
            labelWidth="70"
            :model="model"
            :rules="rules"
            ref="uForm"
        >
            <u-form-item
                label="巡检目录:"
                prop="itemName"
                ref="itemName"
            >
                <u--input
                    v-model="model.itemName"
                    :disabled="disabled"
                    border="none"
                ></u--input>
            </u-form-item>
            <u-form-item
                label="巡检标准:"
                prop="itemStandard"
                ref="itemStandard"
            >
                <u--input
                    v-model="model.itemStandard"
                    :disabled="disabled"
                    border="none"
                ></u--input>
            </u-form-item>
            <u-form-item
                v-if="model.resultHandle==='0'"
                label="巡检结果:"
                prop="itemResult"
                ref="itemResult"
            >
                <u-radio-group 
                    v-model="itemResultMap[model.itemResult]"
                    placement="row"
                    :disabled="disabled"
                    @change="groupChange"
                >
                    <u-radio
                        v-for="(item, index) in radiolist"
                        :key="index"
                        :label="item.name"
                        :name="item.name"
                    />
                </u-radio-group>
            </u-form-item>
            <u-form-item
                v-else
                label="巡检结果:"
                prop="itemResult"
                ref="itemResult"
            >
                <u--input
                    class="input-bg"
                    v-model="model.itemResult"
                    :disabled="disabled"
                    border="surround"
                ></u--input>
            </u-form-item>
            <u-form-item
                label="照片上传:"
                prop="fileList1"
                ref="imgUrl"
            >
                <u-upload
                    :fileList="fileList1"
                    :maxCount="1"
                    :deletable="!disabled"
                    :previewFullImage="false"
                    name="1"
                    :disabled="disabled||phtotoDisabled"
                    @delete="deletePic"
                    @afterRead="afterRead"
                ></u-upload>
            </u-form-item>
        </u--form>
    </uni-card>
</template>

<script>
import config from "@/config.js"
import {
    getToken
} from "@/utils/auth.js"
export default {
    name: 'ResultForm',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        model: {
            type: Object,
            default: () => {}
        },
        disabled: {
            type: Boolean,
            default: false
        },
        phtotoDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            radiolist: [
                {
                    name: '正常',
                    value: '1'
                },
                {
                    name: '异常',
                    value: '0'
                },
            ],
            itemResultMap: {
                '1': '正常',
                '0': '异常'
            },
            rules: {
				'itemResult': {
					type: 'string',
					required: true,
					message: '内容不能为空',
					trigger: ['blur', 'change']
				},
				'fileList1': [
                    { validator: (rule, value, callback) => {
                            if (rule.required) {
                                return this.fileList1.length > 0
                            } else {
                                return
                            }
                        },
                        message: '请上传照片'
                    }
				]
			},
            fileList1: [],
            header: {
                Authorization: "Bearer " + getToken(),
            },
            uploadUrl: config.baseUrl + "/system/oss/upload"
        }
    },

    mounted() {
        this.init()
    },
    onShow() {},
    methods: {
        init() {
            if(this.model.ossInfo) {
                let url = this.model.ossInfo.url
                this.fileList1.push(this.model.ossInfo)
            }
        },
        groupChange(e) {
            const currentItem = this.radiolist.find((item) => item.name === e )
            this.model.itemResult = currentItem.value
        },
        validate(fn) {
            this.$refs['uForm'].validate().then(res => {
                if (!this.phtotoDisabled) {
                    this.model.ossInfo = this.fileList1[0]
                    this.model.imgUrl = this.fileList1[0].url
                }
                fn(this.model)
			}).catch(errors => {
			})
        },
        deletePic() {
            if (this.disabled) return
            this.fileList1 = []
        },
        async afterRead(event) {
            // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
            // let lists = [].concat(event.file)
            // let fileListLen = this[`fileList${event.name}`].length
            // lists.map((item) => {
            //     this[`fileList${event.name}`].push({
            //         ...item,
            //         status: 'uploading',
            //         message: '上传中'
            //     })
            //     console.log('item', item);
            // })
            // console.log('fileList', this[`fileList${event.name}`]);
            // for (let i = 0; i < lists.length; i++) {
            //     const result = await this.uploadFilePromise(lists[i].url)
            //     let item = this[`fileList${event.name}`][fileListLen]
            //     this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
            //         status: 'success',
            //         message: '',
            //         ...result
            //     }))
            //     console.log('fileList', this[`fileList${event.name}`])
            //     fileListLen++
            // }
            const file = event.file
            this.fileList1.push({
                status: 'uploading',
                message: '上传中',
                file
            })
            const result = await this.uploadFilePromise(file.url)
            this.fileList1 = [{
                status: 'success',
                message: '',
                ...result
            }]
        },
        uploadFilePromise(url) {
            return new Promise((resolve, reject) => {
                let a = uni.uploadFile({
                    url: this.uploadUrl,
                    filePath: url,
                    name: 'file',
                    header: this.header,
                    success: (res) => {
                        let resData = res.data.data
                        let resType = typeof res.data
                        if (resType == 'string') {
                            resData = JSON.parse(res.data).data
                        }
                        setTimeout(() => {
                            resolve(resData)
                        }, 1000)
                    }
                });
            })
        },
    }
};
</script>

<style lang="scss" scoped>
.input-bg {
    background-color: #f4f5f7;
}
</style>