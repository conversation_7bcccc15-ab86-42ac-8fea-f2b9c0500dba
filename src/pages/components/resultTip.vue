<template>
  <view>
    <view v-if="status === 'success'">
      <u-icon
        class="flex justify-center"
        name="checkmark-circle-fill"
        color="#5ac725"
        size="56"
      ></u-icon>
      <u-gap height="15"></u-gap>
      <u--text class="tip" :text="text" size="20"></u--text>
    </view>
    <view v-else>
      <u-icon
        class="flex justify-center"
        :name="globalConfig.iconImgPrefix + 'fail.svg'"
        size="200"
      ></u-icon>

      <u-gap height="15"></u-gap>
      <u--text class="tip" :text="text" size="20"></u--text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ResultTip',
  data() {
    return {
      globalConfig: getApp().globalData?.config,
    };
  },
  props: {
    status: {
      type: String,
      default: '',
    },
    text: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="scss" scoped>
.tip {
  justify-content: center !important;
}
</style>
