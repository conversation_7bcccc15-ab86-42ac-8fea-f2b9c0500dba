<template>
	<view class="simpleItem">
		<uni-card :title="cardTitle" @click="handleDetail()" class="simpleItemCard">
			<u-cell :border="false">
				<view slot="label">
					<u-row v-if="cardType == 'leftType'" class="leftType">
						<u-col :span="span" class="leftTypeColSub">
							<view class="alarm-item-handle">
								<u-tag class="subfull-tag" v-if="itemData.status" :text="itemData.status.label" :type="itemData.status.type"></u-tag>
								<!-- <view class="alarm-item-nameTitle">{{ itemData.name }}</view> -->
								<u--text :block="true" color="#333" :lines="1" class="alarm-item-nameTitle" :text="itemData.name"></u--text>
								<u--text class="subfull-text" :lines="1" :size="12" block type="info" :text="itemData.subFullName || '-'"></u--text>
							</view>
						</u-col>
						<u-col :span="12 - span" class="leftTypeCol">
							<view class="alarm-item-title">{{ itemData.fullName }}</view>
							<view class="alarm-item-subTitle">
								<view class="alarm-item-subNameTitle">{{ itemData.subName }}</view>
							</view>
						</u-col>
					</u-row>
					<u-row v-else :class="meetingOrder ? 'rightType mettingRow' : 'rightType'">
						<u-col :span="12 - span" class="rightTypeCol">
							<view class="alarm-item-title">{{ itemData.fullName }}</view>
							<view class="alarm-item-subTitle">
								<view class="alarm-item-nameTitle">{{ itemData.name }}</view>
								<view class="alarm-item-subNameTitle">{{ itemData.subName }}</view>
							</view>
						</u-col>
						<u-col :span="span" class="rightTypeColSub">
							<view class="alarm-item-handle">
								<u-tag class="subfull-tag" v-if="itemData.status" :text="itemData.status.label" :type="itemData.status.type"></u-tag>
								<!-- <span class="subfull-text">{{itemData.subFullName || "-"}}</span> -->
								<u--text class="subfull-text" :lines="1" :size="12" block type="info" :text="itemData.subFullName || '-'"></u--text>
							</view>
						</u-col>
					</u-row>
					<slot name="other"></slot>
				</view>
			</u-cell>
		</uni-card>
	</view>
</template>

<script>
export default {
	components: {},
	props: {
		itemData: {
			default: () => {},
			type: [Object]
		},
		cardTitle: {
			default: '',
			type: String
		},
		meetingOrder: {
			default: false,
			type: Boolean
		},
		span: {
			type: Number,
			default: 3
		},
		cardType: {
			default: 'rightType',
			type: String
		}
	},
	data() {
		return {};
	},

	computed: {},

	methods: {
		handleDetail() {
			this.$emit('handleDetail', this.itemData);
		}
	}
};
</script>

<style lang="scss" scoped>
.u-page__tag-item {
	display: flex;
}
::v-deep .u-cell__body {
	padding: unset;
}
::v-deep .u-cell__body {
	min-height: 66px;
}
.subfull-text {
	padding-left: 6px !important;
}
.uni-card {
	// margin: 15px 0 !important;
}
</style>
