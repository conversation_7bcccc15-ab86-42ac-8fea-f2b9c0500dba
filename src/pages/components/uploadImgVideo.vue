<template>
	<!-- 信息提交 -->
	<view class="burst-info">
		<view class="uni-uploader-body">
			<view class="uni-uploader__files">
				<view class="uni-uploader__input-box" v-if="VideoOfImagesShow" @tap="chooseVideoImage">
					<u-icon name="camera-fill" size="26" color="#d3d4d6"></u-icon>
				</view>
				<!-- 图片 -->
				<view v-for="(image, index) in imageList" :key="index">
					<view class="uni-uploader__file">
						<u-icon class="icon-cuo" name="close-circle-fill" color="#f02d33ff" size="20" @tap="delect(image, index)"></u-icon>
						<image class="uni-uploader__img" :src="image.url" :data-src="image.url" @tap="previewImage"></image>
					</view>
				</view>
				<!-- 视频 -->
				<view v-for="(video, index) in videoList" :key="index">
					<view class="uni-uploader__file">
						<view class="uploader_video">
							<u-icon class="icon-cuo" name="close-circle-fill" color="#f02d33ff" size="20" @tap="delectVideo(video, index)"></u-icon>
							<video :src="video.url" class="video"></video>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { deleteImgRequest } from '@/api/public/index.js';
import { getToken } from '@/utils/auth.js';
import config from '@/config.js';
var sourceType = [['camera'], ['album'], ['camera', 'album']];
export default {
	data() {
		return {
			imageList: [], //图片
			imagesUrlPath: [], //图片
			videoList: [], //视频存放
			sourceTypeIndex: 2,
			checkedValue: true,
			checkedIndex: 0,
			sourceType: ['拍摄', '相册', '拍摄或相册'],
			cameraList: [
				{
					value: 'back',
					name: '后置摄像头',
					checked: 'true'
				},
				{
					value: 'front',
					name: '前置摄像头'
				}
			],
			cameraIndex: 0,
			VideoOfImagesShow: true
		};
	},
	props: {
		maxNum: {
			type: Number,
			default: 3
		}
	},
	watch: {
		imagesUrlPath: {
			handler(newVal, oldVal) {
				if (newVal.length >= this.maxNum) {
					this.VideoOfImagesShow = false;
				} else {
					this.VideoOfImagesShow = true;
				}
			},
			immediate: true
		}
	},
	methods: {
		chooseVideoImage() {
			uni.showActionSheet({
				title: '选择上传类型',
				itemList: ['图片', '视频'],
				success: (res) => {
					if (res.tapIndex == 0) {
						this.chooseImages();
					} else {
						this.chooseVideo();
					}
				}
			});
		},
		chooseImages() {
			// 上传图片
			uni.chooseImage({
				count: this.maxNum, //默认9
				// sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], //从相册选择
				success: (res) => {
					let igmFile = res.tempFilePaths;
					uni.uploadFile({
						url: config.baseUrl + '/system/oss/upload',
						header: {
							Authorization: 'Bearer ' + getToken()
							// 'Content-Type': 'multipart/form-data'
						},
						filePath: igmFile[0],
						name: 'file',
						success: (res) => {
							// let imgUrls = JSON.parse(res.data); //微信和头条支持
							let imgUrls = JSON.parse(res.data); //百度支持
							this.imagesUrlPath = this.imagesUrlPath.concat(imgUrls.data);
							this.imageList = this.imageList.concat(imgUrls.data); //微信
							// if (this.imageList.length >= 3) {
							// 	this.VideoOfImagesShow = false;
							// } else {
							// 	this.VideoOfImagesShow = true;
							// }
						}
					});
					// this.imageList = this.imageList.concat(res.tempFilePaths)  //头条
				}
			});
		},
		chooseVideo() {
			// 上传视频
			uni.chooseVideo({
				maxDuration: 60,
				count: this.maxNum,
				camera: this.cameraList[this.cameraIndex].value,
				sourceType: ['album'],
				success: (responent) => {
					let videoFile = responent.tempFilePath;
					uni.uploadFile({
						url: config.baseUrl + '/system/oss/upload',
						header: {
							Authorization: 'Bearer ' + getToken()
						},
						filePath: videoFile,
						name: 'file',
						success: (res) => {
							// let videoUrls = JSON.parse(res.data) //微信和头条支持
							let videoUrls = JSON.parse(res.data); //百度支持
							this.imagesUrlPath = this.imagesUrlPath.concat(videoUrls.data);
							this.videoList = this.videoList.concat(videoUrls.data); //微信
						}
					});
					// this.src = responent.tempFilePath;  //头条
				}
			});
		},
		previewImage(e) {
			//预览图片
			var current = e.target.dataset.src;
			uni.previewImage({
				current: current,
				urls: this.imageList
			});
		},
		delect(data, index) {
			uni.showModal({
				title: '提示',
				content: '是否要删除该图片',
				success: (res) => {
					if (res.confirm) {
						deleteImgRequest(this.imageList[index].ossId).then((res) => {
							this.imageList.splice(index, 1);
							this.imagesUrlPath.splice(this.imagesUrlPath.indexOf(data.ossId), 1);
							// this.$modal.msgSuccess('删除成功');
							this.$refs.uToast.show(this.$setToastMsg({
								message:'删除成功',
								type:'success'
							}));
						});
					}
				}
			});
		},
		delectVideo(data, index) {
			uni.showModal({
				title: '提示',
				content: '是否要删除此视频',
				success: (res) => {
					if (res.confirm) {
						deleteImgRequest(this.videoList[index].ossId).then((res) => {
							this.videoList.splice(index, 1);
							this.imagesUrlPath.splice(this.imagesUrlPath.indexOf(data.ossId), 1);
							// this.$modal.msgSuccess('删除成功');
							this.$refs.uToast.show(this.$setToastMsg({
								message:'删除成功',
								type:'success'
							}));
						});
					}
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.uni-uploader__files {
	display: flex;
	align-items: center;
}
.uni-uploader__input-box {
	width: 5rem;
	height: 5rem;
	background-color: #f4f5f7;
	border-radius: 2px;
	margin: 0 8px 0 0;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
}
.uni-uploader__file {
	width: 5rem;
	height: 5rem;
	position: relative;
	margin: 0 8px 0 0;
}
.uni-uploader__img,
.video {
	width: 5rem;
	height: 5rem;
}
.icon-cuo {
	position: absolute;
	right: 0;
	top: 0;
	z-index: 999;
}
</style>