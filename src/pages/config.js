export const children = [
  // {
  //   label: '预约列表',
  //   value: 'appointment',
  //   count: 0,
  //   addPath: 'AppointmentList',
  //   path: '/pages/common/appointment/index',

  //   bgPath: 'appointment.png',
  // },
  {
    label: '园区公交',
    value: 'parkBus',
    addPath: 'ParkBus',
    path: '/pages/common/map/index',

    bgPath: 'appointment.png',
  },
  {
    label: '我的作业',
    // value: 'myWork',
    value: 'workRecode',
    count: 0,
    addPath: 'MyWork',
    // path: '/pages/common/myWork/index',
    path: '/pages/common/workRecode/index',
    customIconPath: 'tuihuoliebiao.png',
    bgPath: 'myWork.png',
  },
  {
    label: '我的告警',
    value: 'mywarnManage',
    addPath: 'MyWarnManage',
    count: 0,
    path: '/pages/common/warnManage/warnList/index',

    bgPath: 'myAlarm.png',
  },
  {
    label: '作业记录',
    value: 'workRecode',
    addPath: 'WorkRecode',
    count: 0,
    path: '/pages/common/workRecode/index',

    bgPath: 'workRecode.png',
  },
  {
    label: '门岗核验',
    value: 'gateCheck',
    addPath: 'GateCheck',
    count: 0,
    path: '/pages/gate/index',
    bgPath: 'scan.png',
  },
];
export const applyList = [
  {
    title: '园区公交',
    path: '/pages/common/parkBus/index',
    value: 'ParkBus',
  },
  {
    title: '公交申请',
    value: 'useBusCar',
    path: '/pages/common/useCar/useBusCar',
  },
];
export const myTaskList = [
  // {
  // 	title: "预约审核",
  // 	parentType: "myProcess",
  // 	path: "/pages/common/myTaskList/taskType",
  // 	icon: "wofaqide",
  // 	customIconPath: "wofaqide.png"
  // },
  {
    title: '邀约',
    parentType: 'myProcess',
    path: '/pages/appointment/invitation',
    icon: 'wofaqide',
    customIconPath: 'wofaqide.png',
  },
  {
    title: '预约列表',
    parentType: 'myProcess',
    path: '/pages/common/appointment/index',
    icon: 'wofaqide',
    customIconPath: 'wofaqide.png',
  },
  {
    title: '跨部门申请',

    path: '/pages/common/workerAcrossAppoint/index',
    icon: 'wofaqide',
    customIconPath: 'wofaqide.png',
  },
].concat(applyList);

export const taskManageList = [...children];
