<template>
  <view class="todoListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      title="详情"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard middle-container">
      <uni-card>
        <view style="position: relative">
          <Detail :itemData="info"></Detail>
          <u-avatar
            :src="info.visitorPhoto"
            size="60"
            class="u_avatar"
            @click="openPicPreview"
          ></u-avatar>
        </view>
      </uni-card>
      <u-button
        type="primary"
        @click="checkAppoint"
        text="出入园核验"
        class="customBtn"
      ></u-button>
    </view>

    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import { getPassInfo, checkPassIsValid } from '@/api/gate/index.js';

import Detail from './components/detail';
export default {
  name: 'AppointDetail',
  components: {
    Detail,
  },

  data() {
    return {
      autoBack: true,
      leftText: '',
      passId: '',
      passCode: '',
      info: {},
    };
  },
  onLoad(option) {
    this.passId = JSON.parse(decodeURIComponent(option.query))?.passId;
    this.passCode = JSON.parse(decodeURIComponent(option.query))?.passCode;
    this.getRecord();
  },
  onShow() {},
  onUnload() {
    this.$closePreview();
  },
  methods: {
    checkAppoint() {
      let params = {
        passId: this.passId,
        passCode: this.passCode,
      };
      const url = `/pages/common/checkup/index?info=${encodeURIComponent(
        JSON.stringify(params)
      )}`;
      checkPassIsValid(params).then((res) => {
        if (res.code == 200) {
          const data = res.data;
          const url = `/pages/common/checkup/index?info=${encodeURIComponent(
            JSON.stringify(params)
          )}`;
          if (data.result) this.getCheckInfo(url);
          else this.jumpResutPage(data, params);
        }
      });
    },
    async getCheckInfo(url) {
      uni.navigateTo({
        url,
      });
    },
    /*跳转至检查结果显示页面*/
    jumpResutPage(val, params) {
      let query = {
        status: 'error',
        msg: val.message || '',
        errorsIgnored: val.errorsIgnored,
        passId: params.passId,
        passCode: params.passCode,
      };
      uni.redirectTo({
        url: `/pages/common/checkup/checkResult?query=${encodeURIComponent(
          JSON.stringify(query)
        )}`,
      });
    },
    async getRecord() {
      try {
        const res = await getPassInfo(this.passId);
        this.info = { ...res.data };
      } catch (error) {}
    },
    /* 打开图片组件*/
    openPicPreview() {
      this.$openPreview(this.info.visitorPhoto);
    },
  },
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
  overflow: hidden;
  overflow-y: scroll;
}
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
}
.u_avatar {
  position: absolute;
  right: 16rpx;
  top: 0rpx;
}
</style>
