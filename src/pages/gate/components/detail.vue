<template>
  <view>
    <!-- <u--text
      v-if="itemData.status == 0"
      class="center-text"
      type="error"
      text="未入场"
    ></u--text>
    <u--text
      v-else-if="itemData.isChecked == 1"
      class="center-text"
      type="success"
      text="已入园"
    ></u--text>
    <u--text
      v-else-if="itemData.isChecked == 2"
      class="center-text"
      type="warning"
      text="已出园"
    ></u--text>
    <view v-if="itemData.passQrCode" class="flex justify-center">
      <u--image
        :showLoading="true"
        :src="`data:image/png;base64,${itemData.passQrCode}`"
        width="160px"
        height="160px"
      ></u--image>
    </view> -->

    <u--form
      labelPosition="left"
      labelWidth="120"
      :model="itemData"
      ref="uForm"
    >
      <u-form-item label="访客姓名">
        <u--input
          placeholder=""
          maxlength="50"
          v-model="itemData.visitorName"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="访客手机">
        <u--input
          placeholder=""
          v-model="itemData.visitorPhone"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="车牌号">
        <!-- <u--input
          placeholder=""
          v-model="itemData.carNo"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input> -->
        <u-text :text="itemData.carNo || '无'" />
      </u-form-item>
      <u-form-item label="随访人数" v-if="itemData.followNumber">
        <u--input
          placeholder=""
          v-model="itemData.followNumber"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="访客公司">
        <!-- <u--input
          placeholder=""
          v-model="itemData.visitorCompany"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input> -->
        <u-text :text="itemData.visitorCompany || '无'" />
      </u-form-item>
      <u-form-item label="被访人姓名">
        <u--input
          placeholder=""
          v-model="itemData.intervieweeName"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="被访人手机">
        <u--input
          placeholder=""
          v-model="itemData.intervieweePhone"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="被访问人部门">
        <u--input
          placeholder=""
          v-model="itemData.intervieweeDept"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="来访事由">
        <u--input
          placeholder=""
          v-model="itemData.visitReason"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="是否来访">
        <u-tag
          size="mini"
          :text="itemData.arriveStatus == 0 ? '否' : '是'"
          :type="itemData.arriveStatus == 0 ? 'error' : 'success'"
        ></u-tag>
      </u-form-item>
      <u-form-item label="预约来访时间">
        <u--input
          placeholder=""
          v-model="itemData.visitTime"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="预约离厂时间">
        <u--input
          placeholder=""
          v-model="itemData.leaveTime"
          border="none"
          disabled
          :disabledColor="disabledColor"
        ></u--input>
      </u-form-item>
      <u-form-item label="是否离厂">
        <u-tag
          size="mini"
          :text="itemData.arriveStatus == 2 ? '是' : '否'"
          :type="itemData.arriveStatus == 2 ? 'success' : 'error'"
        ></u-tag>
      </u-form-item>

      <!-- <u-form-item
          label="来源"
          v-if="itemData.source"
      >
        <u--input
            placeholder=""
            v-model="itemData.source"
            border="none"
            disabled
            :disabledColor="disabledColor"
        ></u--input>
      </u-form-item> -->
    </u--form>
  </view>
</template>

<script>
export default {
  name: 'Detail',
  props: {
    itemData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      disabledColor: '#ffffff',
    };
  },
  onLoad() {},
  onShow() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.middle-container {
  padding-top: 0;
}
.uni-card {
  margin: 15px 0 !important;
  padding: 10px 10px !important;
}
.center-text {
  justify-content: center !important;
}
</style>
