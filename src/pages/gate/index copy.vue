<template>
  <view class="toGeteListPage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="title"
      :autoBack="autoBack"
      leftText=""
      @leftClick="leftClick"
    >
    </top-navbar>
    <contain-tab
      @changeCurrent="changeCurrent"
      :topTabList="topTabList"
      :current="currentIndex"
      :tableData="tableData"
      :showTabs="true"
      :showTodayRecord="true"
      iconName="calendar"
      title="今日记录"
      :showListSearch="true"
      @serching="searchOperate"
      :gateTypeList="gateTypeList"
      :showLoadMore="isLoading"
    >
      <template #topSlot>
        <view class="header-section">
          <view class="top-box">
            <view class="title-box">
              <u-avatar :src="avatar" size="60"></u-avatar>
              <!-- <image :src="avatar" class="avatarImg" mode="widthFix" @click="openPicPreview"></image> -->
              <view class="welcome-box">
                <text> {{ name + "," + "欢迎进行门岗核验" }}</text>
              </view>
            </view>
            <view class="operation-box">
              <view class="select-gate" @click="openAreaSelect">
                <view
                  v-if="!queryData.areaId"
                  @click="openAreaSelect"
                  class="login-tip"
                >
                  <text>选择门岗</text>
                </view>
                <view v-else class="login-tip" @click="openAreaSelect">
                  <text
                    class="flex flex-direction"
                    style="color: #f29ca9"
                  ></text>
                  <text class="flex flex-direction" style="width: 138rpx">{{
                    queryData.areaName
                  }}</text>
                </view>
              </view>
              <view class="scan-code" @click="handleScan">
                <text>扫码核验</text>
              </view>
            </view>
          </view>
        </view>
      </template>
      <template #default="{ itemData }">
        <uni-card @click="goAppointInfo(itemData)">
          <view class="flex" style="justify-content: space-between">
            <view>
              <view class="flex">
                <view
                  style="
                    font-size: 48rpx;
                    color: #333333;
                    line-height: 48rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    max-width: 200rpx;
                  "
                >
                  {{ itemData.visitorName }}
                </view>
                <view
                  v-if="itemData.carNo"
                  style="
                    font-size: 30rpx;
                    color: #333333;
                    line-height: 30rpx;
                    margin-left: 10rpx;
                  "
                  class="carno-over"
                >
                  {{ itemData.carNo }}
                </view>
                <view
                  class="flex"
                  style="
                    color: #999999;
                    margin-left: 30rpx;
                    font-size: 24rpx;
                    line-height: 24rpx;
                    margin-top: 4rpx;
                  "
                >
                  <view style="margin-right: 30rpx"> 访 </view>
                  <view>
                    {{ itemData.intervieweeDept }}
                  </view>
                </view>
              </view>
              <view style="color: #333333; margin-top: 18rpx; font-size: 28rpx">
                {{ itemData.visitTime }}
              </view>
            </view>

            <view
              class="u-tag-diy-wapper"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <u-tag
                size="mini"
                :text="queryStatus(itemData.arriveStatus).text"
                :color="queryStatus(itemData.arriveStatus).color"
                :bgColor="queryStatus(itemData.arriveStatus).bgColor"
                :borderColor="queryStatus(itemData.arriveStatus).color"
              ></u-tag>
            </view>
          </view>
          <!-- <template v-slot:title>
            <view class="flex space_b align_center">
              <view class="font-16 text_scrolling flex align_center">
                <u-avatar shape="square" size="35" :src="itemData.visitorPhotoUrl"
                  customStyle="margin: -3px 5px -3px 0"></u-avatar>
                <view>
                  <view class="font-14 setClor">
                    <text class="mr4">{{ itemData.visitorName }}</text>
                    <text>{{ itemData.visitTime }}</text>
                  </view>
                  <text class="font-12">{{ itemData.intervieweeName }}</text>
                </view>
              </view>
              <u-tag size="mini" v-show="itemData.isRemain == 1" text="已滞留" type="error"></u-tag>
            </view>
            <view class="mt4">
              <u-line></u-line>
            </view>
          </template> -->

          <!-- <uni-row slot="actions">
            <view class="flex mb5 align_center space_b">
              <u-tag size="mini" :text="queryStatus(itemData.arriveStatus).text"
                :color="queryStatus(itemData.arriveStatus).color" :bgColor="queryStatus(itemData.arriveStatus).bgColor"
                :borderColor="queryStatus(itemData.arriveStatus).color"></u-tag>
            </view>
          </uni-row> -->
        </uni-card>
      </template>
    </contain-tab>
    <qr-code
      v-show="isScaning"
      ref="qrScan"
      @upScaning="upScaning"
      @stopScan="stopScan"
    ></qr-code>
    <!-- <PicPreview
      :showPicPreview="showPicPreview"
      :previewUrl="avatar"
      @closePicPreview="closePicPreview"
    ></PicPreview> -->
    <tki-tree
      ref="tkitree"
      :foldAll="true"
      :range="appointList"
      rangeKey="areaName"
      idKey="areaId"
      @confirm="confirmArea"
      @cancel="cancelArea"
      cancelColor="#909193"
      confirmColor="#aa001e"
    />
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import ContainTab from "@/pages/components/containTabContent.vue";
import { topAllTabList, getStatus ,gateTypeList} from "./list_config.js";

import {
  getGate,
  getPassList,
  bindingGate,
  checkPassIsValid,
} from "@/api/gate/index.js";
import QrCode from "@/components/qr/code";
import { getGateAreaTree } from "@/api/vehicleApply/index.js";
import tkiTree from "@/components/tki-tree/tki-tree.vue";

export default {
  components: {
    ContainTab,
    tkiTree,
    QrCode,
  },

  data() {
    return {
      autoBack: true,
      topTabList: topAllTabList,
      gateTypeList:gateTypeList,
      appointList: [],
      currentIndex: 0,
      title: "",
      queryData: {
        areaId: "",
        areaName: "",
        status: 0,
        pageNum: 1,
        pageSize: 10,
        keyWord: "",
      },
      total: 0,
      tableData: [],
      isScaning: false,
      isLoading: false, // 加载状态
      name: this.$store.state.user.name,
    };
  },
  computed: {
    avatar() {
      return this.$store.state.user.avatar;
    },
  },
  onLoad(option) {
    this.title = option.title;
    this.getAreaList();

    this.getGate();
    this.tableData = [];
    this.getAllAppointRecode();
  },
  onShow() {},
  methods: {
    leftClick() {
      uni.navigateBack();
    },
    //先获取绑定门岗
    async getGate() {
      try {
        const res = await getGate();
        this.queryData.areaId = res?.data.areaId || "";
        this.queryData.areaName = res?.data.areaFullName || "";
      } catch (error) {}
    },
    async getAllAppointRecode() {
      try {
        // 设置加载状态
        this.isLoading = true;

        const res = await getPassList(this.queryData);

        // 如果是第一页，直接赋值；否则追加数据
        if (this.queryData.pageNum === 1) {
          this.tableData = res.rows || [];
        } else {
          this.tableData = this.tableData.concat(res.rows || []);
        }
        this.total = res.total || 0;
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        // 无论成功失败都要重置加载状态
        this.isLoading = false;
      }
    },
    searchOperate(val) {
      this.queryData.keyWord = val;
      this.research();
    },
    handleScan() {
      if (!this.queryData.areaId) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: "请先选择门岗",
            type: "warning",
          })
        );
        return;
      }
      this.scanCode();
    },
    scanCode() {
      this.isScaning = true;
      this.$refs.qrScan.scanBefore();
    },
    stopScan() {
      this.isScaning = false;
    },
    /*扫码结果*/
    upScaning(val) {
      this.isScaning = false;
      if (val.status) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: val.msg,
            type: "error",
          })
        );
      } else {
        let params = {
          passId: val.passId,
          passCode: val.passCode,
        };
        checkPassIsValid(params).then((res) => {
          if (res.code == 200) {
            const data = res.data;
            const url = `/pages/common/checkup/index?info=${encodeURIComponent(
              JSON.stringify(params)
            )}`;
            if (data.result) this.getCheckInfo(url);
            else this.jumpResutPage(data, params);
          }
        });
      }
    },
    async getCheckInfo(url) {
      uni.navigateTo({
        url,
      });
    },
    /*跳转至检查结果显示页面*/
    jumpResutPage(val, params) {
      let query = {
        status: "error",
        msg: val.message || "",
        errorsIgnored: val.errorsIgnored,
        passId: params.passId,
        passCode: params.passCode,
      };
      uni.redirectTo({
        url: `/pages/common/checkup/checkResult?query=${encodeURIComponent(
          JSON.stringify(query)
        )}`,
      });
    },
    /*获取区域树形集合*/
    async getAreaList() {
      try {
        const res = await getGateAreaTree();
        this.appointList = this.checkGate(res.data);
      } catch {}
    },
    checkGate(arr) {
      if (!Array.isArray(arr)) {
        return [];
      }
      const data = arr.map((item) => {
        if (item.children?.length) {
          item.children = this.checkGate(item.children);
        }
        if (item.areaType != 5) {
          item.disabled = true;
        }

        return item;
      });
      return data;
    },
    /* 打开选择区域组件*/
    openAreaSelect() {
      this.$refs.tkitree._show();
    },
    /* 打开图片组件*/
    openPicPreview() {
      this.$openPreview(this.avatar).then();
    },

    confirmArea(e) {
      if (e?.length) {
        this.queryData.areaName = e[0].areaFullName;
        this.queryData.areaId = e[0].areaId;
        try {
          bindingGate(this.queryData.areaId).then((res) => {
            if ((res.code = 200)) {
              this.research();
            }
          });
        } catch (e) {}
      }

      this.$refs.tkitree._hide();
    },
    cancelArea() {
      this.$refs.tkitree._hide();
    },
    changeCurrent(val) {
      this.keyIndex = val;
      this.queryData.status = val;
      this.research();
    },

    research() {
      this.tableData = [];
      this.queryData.pageNum = 1;
      this.getAllAppointRecode();
    },
    scrolltolower() {
      console.log('滚动到底部，当前状态:', {
        currentPage: this.queryData.pageNum,
        pageSize: this.queryData.pageSize,
        totalData: this.total,
        loadedData: this.tableData.length,
        isLoading: this.isLoading
      });

      // 检查是否还有更多数据可以加载
      // 当前已加载的数据量是否已达到总数
      if (this.tableData.length >= this.total && this.total > 0) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: "已加载全部",
            type: "success",
          })
        );
        return;
      }

      // 检查是否正在加载中，避免重复请求
      if (this.isLoading) {
        console.log('正在加载中，跳过请求');
        return;
      }

      // 增加页码并加载更多数据
      this.queryData.pageNum++;
      console.log('开始加载第', this.queryData.pageNum, '页');
      this.getAllAppointRecode();
    },
    queryStatus(val) {
      return getStatus(val);
    },
    //切换任务
    iconClick() {
      this.actionShow = true;
    },
    selectHandle(val) {
      this.actionLabel = val.name;
      this.listName = val.listName;

      this.research();
    },
    /*进入预约详情*/
    goAppointInfo(info) {
      let query = {
        passId: info?.passId || "",
        passCode: info?.passCode || "",
      };
      uni.navigateTo({
        url: `/pages/gate/appointDetail?query=${encodeURIComponent(
          JSON.stringify(query)
        )}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.toGeteListPage {
  background-image: url("@/static/images/bg.png");
  background-repeat: repeat;
  background-size: contain;
  height: 100%;
  ::v-deep .bgCard {
    background: none !important;
  }
  ::v-deep .uni-card--shadow {
    box-shadow: rgb(247, 247, 247) 0px 0px 8px 0.06px !important;
  }
  ::v-deep .show-search {
    padding: 0px !important;
  }
}

.uni-col {
  padding: 10px 0;
}

.uni-card {
  margin: 5px 0 !important;
  padding: 10px 10px !important;
}

.carno {
  ::v-deep .u-text__value {
    width: 100px;
  }
}
.u-tag-diy-wapper {
  ::v-deep .u-tag__text {
    padding: 4rpx 10rpx;
  }
}
.little-margin {
  margin-right: 8px;
}

.item_name {
  color: #666;
}

.text_scrolling {
  white-space: nowrap;
  /* 保持文字在一行内显示 */
  overflow-x: auto;
  /* 隐藏溢出的内容 */
  // text-overflow: ellipsis; /* 使用省略号表示文字被截断 */
  width: 75%;
  /* 设置容器宽度 */
  color: $uni-text-color-grey;
}

.setClor {
  color: $uni-text-color;
}

.header-section {
  .setScan {
    margin: 16px;
    padding: 12px;
    color: $uni-text-color-grey;
    background-color: #f4f5f9;
    border-radius: 5px;
  }

  .top-box {
    margin-bottom: 20rpx;

    .title-box {
      display: flex;
      align-items: center;
      margin-top: 60rpx;

      .avatarImg {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 1, 0.3);
      }

      .user-info {
        margin-left: 46rpx;
      }

      .login-tip {
        margin-top: 8rpx;
        cursor: pointer;
      }

      .u_title {
        font-size: 20px;
        line-height: 20px;
      }
    }

    .welcome-box {
      margin-left: 40rpx;
      font-size: 40rpx;
      color: #333;
    }

    .operation-box {
      display: flex;
      justify-content: flex-end;
      color: #f7f7f7;
      padding: 30px 0px;

      .select-gate {
        display: flex;
        align-items: center;
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        width: 50%;
        height: 186rpx;
        background-image: url("./images/gate.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

      .scan-code {
        width: 50%;
        display: flex;
        align-items: center;
        margin-left: 20rpx;
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        background-image: url("./images/scan.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
    }
  }
}
// tabs样式铺满整行
::v-deep .u-tabs__wrapper {
  flex: 1;

  .u-tabs__wrapper__scroll-view {
    flex: 1;
  }

  .u-tabs__wrapper__nav {
    flex: 1;

    .u-tabs__wrapper__nav__item {
      flex: 1;
    }
  }
}
.carno-over {
  max-width: 170rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
