<template>
  <view class="homePage topBgBox">
    <top-navbar
      bgColor="#AA001E"
      :title="topTitle"
      :autoBack="autoBack"
      :leftText="leftText"
      :safeAreaInsetTop="true"
    >
      <view slot="title-icon" @click="statisticeView('stat')">
        <uni-icons
          color="#fff"
          type="calendar"
          size="19"
          v-show="setEnterShow('Line')"
        ></uni-icons>
      </view>
      <!-- <view
        slot="title-right"
        @click="statisticeView('message')"
        v-show="setEnterShow('MessageCenter')"
      >
        <u--image
          class="topNotice"
          :src="globalConfig.iconImgPrefix + 'notice.png'"
        >
      <u-icon name="bell-fill" size="22" />
      </u--image>
        <u-badge
          type="error"
          absolute
          :offset="[6, 5]"
          max="99"
          :value="unreadNum"
        ></u-badge>
      </view> -->
    </top-navbar>
    <!-- 轮播图 -->
    <u-swiper :list="list" keyName="image" height="225" circular radius="0" />
    <u-notice-bar class="noticeCustom" :text="noticeText" bgColor="transparent" color="#1E2835" fontSize="12" url="pages/mine/messageCenter/index"/> 
    <view class="homeBody">
      <u-row :border="false" class="homeCardGrid" gutter="18">
        <u-col
          span="6"
          v-for="(item, index) in taskGrid"
          :key="index"
          @click="taskView(item)"
          class="homeCardGridItem"
        >
          <view
            class="gridStyle"
            :style="{
              'background-image': `url(${item.query.bgPath})`,
            }"
          >
            <view>
              <view class="grid-text">{{ item.meta.title }}</view>
              <u-button
                type="info"
                shape="circle"
                text="去查看"
                plain
                class="custom-style"
                size="mini"
              ></u-button>
            </view>
          </view>
        </u-col>
      </u-row>
    </view>
  </view>
</template>

<script>
import TopNavbar from '@/components/top-navbar/top-navbar.vue';
import { taskManageList, applyList } from './config.js';
import {
  getProcessCategoryList,
  getUnreadNum,
  todoProcessTypeNum,
  updateScanCodeTime,
} from '@/api/aboutTask/index.js';
import { getTodoListByDeviceId } from '@/api/meeting/index.js';
import { getPassCheckInfo } from '@/api/checkup/index.js';
import { pushRouter, getRouterVlaue } from '@/utils/permission';
import {
  getNoticeListAll
} from '@/api/system/user.js';
const img = require('@/static/images/profile.png');
export default {
  components: {
    TopNavbar,
  },
  data() {
    return {
      globalConfig: getApp().globalData?.config,
      leftText: '',
      img,
      autoBack: false,
      topTitle: '今世缘智慧通行',
      current: 0,
      swiperDotIndex: 0,
      taskGrid: [],
      applyGrid: [],
      categoryList: [],
      noticeText: '园区也“智慧”，业务更“流畅”',
      list: [
        {
          image: '/static/images/banner/busBanner.jpg',
          title: '',
        },
      ],

      unreadNum: 0,
      todoTotalNum: 0,
    };
  },
  onLoad: function () {
    //this.checkLogin();
    this.init();
  },
  onShow() {
    // this.taskGrid = this.filterSectionArr(taskManageList);
    // this.applyGrid = this.filterSectionArr(applyList);
  },
  watch: {
    curRoutes: {
      handler(val) {
        this.taskGrid = this.filterSectionArr(taskManageList);
        //this.applyGrid = this.filterSectionArr(applyList);
      },
      immediate: true,
    },
  },
  computed: {
    curRoutes() {
      return this.$store.getters.routerMenu;
    },
  },
  methods: {
    async init() {
      try {
        const res = await getNoticeListAll();
        console.log("noticeList", res);

        // 判断createTime是否是今天的函数
        const isToday = (dateString) => {
          const today = new Date();
          const createDate = new Date(dateString);
          return today.toDateString() === createDate.toDateString();
        };

        // 筛选出今天创建的通知
        const todayNotices = res.data.filter(notice => isToday(notice.createTime));

        if (todayNotices.length > 0) {
          if (todayNotices.length === 1) {
            // 如果只有一个今天的通知，直接赋值
            this.noticeText = todayNotices[0].noticeTitle;
          } else {
            // 如果有多个今天的通知，拼接成字符串
            const noticeTitles = todayNotices.map(notice => notice.noticeTitle);
            this.noticeText = noticeTitles.join(' | ');
          }
        } else {
          // 如果没有今天的通知，使用默认值或第一个通知的内容
          this.noticeText = res.data.length > 0 ? res.data[0].content : '园区也"智慧"，业务更"流畅"';
        }
      } catch (error) {
        console.log(error);
        // 出错时使用默认值
        this.noticeText = '园区也"智慧"，业务更"流畅"';
      }
    },
    getBackUrl(url) {
      return require(`@${url}`);
    },
    /*判断是否显示*/
    setEnterShow(val) {
      return this.curRoutes.some((item) => item.name == val && !item.hidden);
    },

    /*通过路径和组件以及组件的是否隐藏判断是否显示，考虑存在相同组件同一路由的情况*/
    filterSectionArr(arr) {
      let result = this.curRoutes.reduce((pre, tem) => {
        let val = arr.filter((item) => {
          if (item.addPath) {
            return (
              tem.path == item.path && item.addPath == tem.name && !tem.hidden
            );
          } else {
            return tem.path == item.path && !tem.hidden;
          }
        });

        if (val.length) {
          tem.query.bgPath = require(`@/static/images/banner/${val[0].bgPath}`);
          pre.push(tem);
        }
        return pre;
      }, []);
      return result;
    },

    listViewHandle(item) {
      pushRouter(item);
    },
    //查看工单
    taskView(item) {
      pushRouter(item);
    },
    async getUnread() {
      try {
        const res = await getUnreadNum();
        this.unreadNum = res?.data;
      } catch (error) {}
    },
    async getTodoNum() {
      Promise.all([todoProcessTypeNum()])
        .then(([res1]) => {
          this.todoTotalNum = res1.data.total;
          let countList = res1.data.list || [];
          countList.forEach((element) => {
            this.taskGrid.forEach((item) => {
              if (item.value === element.type) {
                item.count = element.count;
              }
            });
            console.log('this.taskGrid',this.taskGrid);
          });
        })
        .catch();
    },
    async getDeviceTodoList(deviceId) {
      const res = await getTodoListByDeviceId(deviceId);
      return res.code === 200 && res.data.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}

.homeCardBox {
  ::v-deep .uni-section-header {
    padding-bottom: 0;
  }
}
.homeBody {
  padding: 36rpx;
}
.gridStyle {
  height: 180rpx;
  border-radius: $uni-border-radius-lg;

  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-size: cover;
  .grid-text {
    color: #fff;
    padding-bottom: 16rpx;
  }
}
.custom-style {
  color: $uni-color-primary;
  background: #eddbde;
  border-radius: 19px;
  opacity: 0.6;
}
</style>
