<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <image
        style="width: 100rpx; height: 100rpx"
        :src="globalConfig.appInfo.logo"
        mode="widthFix"
      ></image>
      <text class="title">今世缘智慧通行</text>
    </view>
    <view class="login-form-content">
      <view class="input-item flex align-center">
        <view class="iconfont icon-phone icon"></view>
        <input
          v-model="loginForm.phonenumber"
          class="input"
          type="text"
          placeholder="请输入手机号"
          maxlength="30"
        />
      </view>
      <!-- <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input
          v-model="loginForm.password"
          type="password"
          class="input"
          placeholder="请输入密码"
          maxlength="20"
        />
      </view> -->
      <view class="flex align-center space_b">
        <view class="input-item flex align-center mt16">
          <view class="iconfont icon-code icon"></view>
          <input
            v-model="loginForm.smsCode"
            type="numeric"
            class="input"
            placeholder="请输入验证码"
            maxlength="6"
          />
        </view>

        <view class="ml5 setCode mt16">
          <u-code
            :seconds="seconds"
            @end="end"
            @start="start"
            ref="uCode"
            @change="codeChange"
            startText="获取验证码"
            endText="重新获取验证码"
          ></u-code>
          <u-button @tap="getCode" plain>{{ tips }}</u-button>
          <!-- <view class="login-code">
					  <u-button @click="getCode" plain v-show="!codeUrl">{{tips}}</u-button>
					  <image :src="codeUrl" @click="getCode" class="login-code-img"></image>
					</view> -->
        </view>
      </view>
      <!-- <view
        class="input-item flex align-center"
        style="width: 60%; margin: 0px"
        v-if="captchaEnabled"
      >
        <view class="iconfont icon-code icon"></view>
        <input
          v-model="loginForm.code"
          type="number"
          class="input"
          placeholder="请输入验证码"
          maxlength="4"
        />
        <view class="login-code">
          <image :src="codeUrl" @click="getCode" class="login-code-img"></image>
        </view>
      </view> -->
      <view class="action-btn mt16">
        <!-- <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">登录</button> -->
        <u-button
          type="primary"
          text="登录"
          @click="handleLogin"
          class="customBtn"
          size="large"
        ></u-button>
      </view>
      <view class="reg text-center" v-if="register">
        <text class="text-grey1">没有账号？</text>
        <text @click="handleUserRegister" class="text-blue">立即注册</text>
      </view>
      <view class="xieyi text-center">
        <u-checkbox-group v-model="checkboxValue1" @change="changeAgreement">
          <u-checkbox
            activeColor="#AA001E"
            name="agreement"
            :checked="isAgreement"
          ></u-checkbox>
        </u-checkbox-group>
        <text class="text-grey1">已阅读并同意</text>
        <text @click="handleUserAgrement" class="text-blue">《用户协议》</text>
        和
        <text @click="handlePrivacy" class="text-blue">《隐私政策》</text>
      </view>
    </view>
    <!-- 图形验证码 -->
		<custom-modal
			ref="customModal"
			title="请输入图形码结果"
			:show="imageCodeShow"
			:asyncClose="true"
			@confirm="codeModalConfirm"
			@cancel="codeModalCancel"
		>
			<u-form
				:model="imageCodeInfo"
				:rules="imageCodeInfoRules"
				labelWidth="0"
				ref="imageCodeInfoRef"
				>
				<u-form-item prop="code">
					<u--image
						mode="aspectFit"
						:showLoading="true"
						:src="imageCodeUrl"
						width="200rpx"
						height="100rpx"
						@click="getImageCode"
						/>
					<u--input
						style="width:45%;margin-left: 10%;"
						slot="right"
						v-model="imageCodeInfo.code"
						placeholder="请输入"
						type="number"
						maxlength="8"
					/>
				</u-form-item>
			</u-form>
		</custom-modal>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
import CustomModal from "@/pages/components/modal.vue"
import { appBoundUser, getVerification, getImgeCodeImg } from '@/api/login';
const test = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
export default {
  components: {
		CustomModal
	},
  data() {
    return {
      imageCodeShow: false,
			imageCodeUrl: '',
      seconds: 120,
      tips: '',
      express: false,
      checkboxValue1: [],
      isAgreement: false,
      codeUrl: '',
      captchaEnabled: true,
      // 用户注册开关
      register: false,
      globalConfig: getApp().globalData.config,
      loginForm: {
        phonenumber: '',
        // password: "admin123",
        // password: 'Cicdi@123456',
        smsCode: '',
      },
      imageCodeInfo: {
				uuid: '',
				code: '',
				phonenumber: ''
			},
			imageCodeInfoRules: {
				code: {
					type: 'number',
					required: true,
					message: '请填写',
					trigger: ['blur', 'change']
				}
			}
    };
  },

  created() {
    window.addEventListener('load', () => {
      const parentOrigin = window.location.ancestorOrigins[0];
      window.parent.postMessage(['getLocation'], parentOrigin);
      console.log(parentOrigin);
    });

    window.addEventListener('message', (e) => {
      console.log(e.data.location);
    });
  },
  methods: {
    // 获取图形验证码
		async getImageCode() {
			try {
				const res = await getImgeCodeImg();
				this.imageCodeUrl = 'data:image/gif;base64,' + res.data.img;
				this.imageCodeInfo.uuid = res.data.uuid;
			} catch (error) {}
		},
    // 图形验证码确认按钮
		codeModalConfirm() {
			this.$refs.imageCodeInfoRef.validate().then(async(valid) => {
				if(valid) {
					this.imageCodeShow = false;
					uni.showLoading({title: '正在获取验证码'});
					this.imageCodeInfo.phonenumber = this.loginForm.phonenumber;
					const res = await getVerification(this.imageCodeInfo) 
					this.loginForm.smsCode = res.data?.sendSmsCode;
					uni.hideLoading() 
					this.$refs.uCode.start();
				}
			}).catch(err => {
				uni.hideLoading();
        this.$refs.uCode.reset();
			})
		},
		// 图形验证码取消按钮按钮
		codeModalCancel() {
			this.imageCodeShow = false;
		},
    //验证码改变方法
    codeChange(e) {
      this.tips = e;
    },
    //验证码开始方法
    end() {
      this.express = true;
    },
    //验证码结束方法
    start() {
      this.express = false;
    },
    // 改变协议勾选
    changeAgreement(e) {},
    // 用户注册
    handleUserRegister() {
      this.$tab.redirectTo(`/pages/register`);
    },
    // 隐私协议
    handlePrivacy() {
      let site = this.globalConfig.appInfo.agreements[0];
      this.$tab.navigateTo(
        `/pages/common/webview/index?title=${site.title}&url=${site.url}`
      );
    },
    // 用户协议
    handleUserAgrement() {
      let site = this.globalConfig.appInfo.agreements[1];
      this.$tab.navigateTo(
        `/pages/common/webview/index?title=${site.title}&url=${site.url}`
      );
    },
    // 获取图形验证码
    async getCode() {
      if (
        !this.loginForm?.phonenumber ||
        !test.test(this.loginForm.phonenumber)
      ) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '请正确输入手机号',
            position: 'top',
            type: 'error',
          })
        );
        return;
      }
      if (this.$refs.uCode.canGetCode) {
        this.imageCodeShow = true;
				this.imageCodeInfo = {
					uuid: '',
					code: '',
					phonenumber: ''
				};
				this.imageCodeUrl = '';
				this.getImageCode();
      }
    },
    // 登录方法
    async handleLogin() {
      if (this.loginForm.phonenumber === '') {
        // this.$modal.msgError('请输入您的账号');
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '请输入您的账号',
            position: 'top',
            type: 'error',
          })
        );
        return;
      }
      if (
        !this.loginForm?.phonenumber ||
        !test.test(this.loginForm.phonenumber)
      ) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '请正确输入手机号',
            position: 'top',
            type: 'error',
          })
        );
        return;
      }
      if (this.loginForm.smsCode === '' || this.express) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: this.express ? '验证码已过期' : '请输入验证码',
            type: 'error',
            position: 'top',
          })
        );
        return;
      }

      if (this.checkboxValue1.length == 0) {
        this.$refs.uToast.show(
          this.$setToastMsg({
            message: '请先勾选已阅读并同意《服务协议》和《隐私政策》',
            type: 'error',
            position: 'top',
          })
        );
        return;
      }
      this.$refs.uToast.show(
        this.$setToastMsg({
          message: '登录中，请耐心等待...',
          type: 'info',
          position: 'top',
        })
      );
      // this.$modal.loading('登录中，请耐心等待...');
      this.pwdLogin();
    },
    // 密码登录
    async pwdLogin() {
      this.$store
        .dispatch('Login', this.loginForm)
        .then(() => {
          this.$modal.closeLoading();

          this.$refs.uCode.reset();
          this.loginSuccess();
        })
        .catch(() => {
          // if (this.captchaEnabled) {
          //   this.getCode();
          // }
        });
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      //#ifdef APP-PLUS
      var clientid;
      var timer = setInterval(function () {
        if (clientid != null && clientid != 'null') {
          clearInterval(timer);
          appBoundUser({ cid: clientid }).then((res) => {});
          return;
        }
        clientid = plus.push.getClientInfo().clientid;
      }, 1000);
      // #endif

      // 设置用户信息
      // this.$store.dispatch('GetInfo').then((res) => {
      this.$tab.reLaunch('/pages/index');
      //});
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 21px;
    text-align: center;
    padding-top: 15%;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .login-form-content {
    text-align: center;
    margin: 20px auto;
    margin-top: 15%;
    width: 80%;

    .input-item {
      // margin: 20px auto;
      background-color: #f5f6f7;
      height: 45px;
      border-radius: 20px;

      .icon {
        font-size: 38rpx;
        margin-left: 10px;
        color: #999;
      }

      .input {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        padding-left: 15px;
      }
    }

    .login-btn {
      margin-top: 40px;
      height: 45px;
    }

    .reg {
      margin-top: 15px;
    }

    .xieyi {
      color: #333;
      margin-top: 20px;
      display: flex;
      align-items: center;
    }

    .login-code {
      height: 38px;
      float: right;

      .login-code-img {
        height: 38px;
        position: absolute;
        margin-left: 10px;
        width: 200rpx;
      }
    }
  }
}
</style>
