<template>
	<view class="page-container">
		<view class="page-container__inner">
			<view class="search-input">
				<input class="uni-input" @input="onKeyInput" placeholder="搜索部门" />
				<uni-icons type="search" class="search-icon"></uni-icons>
			</view>
			<ly-tree :tree-data="treeData" ref="tree" node-key="id" :expandOnClickNode="false" :highlightCurrent="true"
				:defaultExpandedKeys="defaultExpandedKeys" :childVisibleForFilterNode="true" :filter-node-method="filterNode"
				@node-click="handleNodeClick">
			</ly-tree>
		</view>
	</view>
</template>

<script>
	import LyTree from '@/components/ly-tree/ly-tree.vue'
	import {
		getDeptTree
	} from "@/api/system/user"
	export default {
		components: {
			LyTree
		},
		data() {
			return {
				title: 'Hello',
				treeData: [],
				filterText: "",
				defaultExpandedKeys: [],
				// queryDepartTreeList: "/sys/sysDepart/queryTreeList",
				queryDepartTreeList: "/sys/sysDepart/queryWorkGroupList"
			}
		},
		onLoad(option) {
			if(JSON.stringify(option) !== '{}') {
				this.queryParam = JSON.parse(decodeURIComponent(option.query))
			}
			this.getDeptQuery()
		},
		methods: {
			async getDeptQuery() {
				this.treeData = []
				this.defaultExpandedKeys = []
				try {
					const res = await getDeptTree()
					// this.treeData = this.generateList(res.data)
					this.treeData = res.data
					this.defaultExpandedKeys.push(this.treeData[0].id)
				} catch (e) {}
			},
			generateList(data) {
				for (let i = 0; i < data.length; i++) {
					const node = data[i];
					data[i].label = data[i].title;
					if (node.children) {
						this.generateList(node.children);
					}
				}
				return data
			},
			//获取输入框的值
			onKeyInput(event) {
				this.filterText = event.target.value
				this.$refs.tree.filter(this.filterText)
			},
			//查询过滤
			filterNode(value, data) {
				if (!value) return true
				return data.label.indexOf(value) !== -1
			},
			handleNodeClick(obj) {
				const query = {
          id: obj.data.id,
          name: obj.data.label,
          ...this.queryParam
        }
				uni.navigateTo({
					url: `./wokersList?query=${encodeURIComponent(JSON.stringify(query))}`
				})
			}
		}
	}
</script>

<style>
	.page-container__inner {
		padding-top: 10px;
	}
	::v-deep .ly-tree-node__label {
		font-size: 38upx;
	}
</style>