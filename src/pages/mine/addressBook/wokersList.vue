<template>
	<view class="page-container">
		<view class="page-container__inner">
			<view class="search-input">
				<input class="uni-input" @input="onKeyInput" placeholder="搜索" />
				<uni-icons type="search" class="search-icon"></uni-icons>
			</view>
			<template v-if="queryInfo.path">
				<view class="user" v-for="(item, index) in workerList" :key="index" @click.stop="itemClick(item)">
					<view class="user-name">
						<uni-tooltip :content="item.userName" placement="top">
							<view class="user-name__inner">{{ item.userName }}</view>
						</uni-tooltip>
					</view>
					<view class="user-name ancestors-name">
						<uni-tooltip :content="item.dept.ancestorsName" placement="top">
							<view class="user-name__inner">{{ item.dept.ancestorsName }}</view>
						</uni-tooltip>
					</view>
					<view class="user-number">
						<view>{{ item.phonenumber || '无电话信息' }}</view>
					</view>
				</view>
			</template>
			<template v-else>
				<view class="user" v-for="(item, index) in workerList" :key="index">
					<view class="user-name">
						<uni-tooltip :content="item.userName" placement="top">
							<view class="user-name__inner">{{ item.userName }}</view>
						</uni-tooltip>
					</view>
					<view class="user-name ancestors-name">
						<uni-tooltip :content="item.dept.ancestorsName" placement="top">
							<view class="user-name__inner">{{ item.dept.ancestorsName }}</view>
						</uni-tooltip>
					</view>
					<view class="user-number" @click.stop="makePhone(item.phonenumber)">
						<view v-show="item.phonenumber">
							<i class="iconfont icon-Phone" style="color: #4cd964; font-size: 16px; margin-right: 10upx" />
						</view>

						<view>{{ item.phonenumber || '无电话信息' }}</view>
					</view>
				</view>
			</template>
			<!-- 完成工作确认弹窗 -->
			<modal ref="confirmRef" title="提示" :show="confirmShow" @confirm="confirmConfirm" @cancel="confirmCancel">
				<u--text text="确认选择当前人员吗？" />
			</modal>
		</view>
	</view>
</template>

<script>
import modal from '@/pages/components/modal.vue';
import { getUsersList } from '@/api/system/user';
import { switchTask } from '@/api/check/index.js';
export default {
	components: {
		modal
	},
	data() {
		return {
			workerList: [],
			queryWorker: '',
			filterText: '',
			deptId: '',
			list: '/sys/user/list',
			isShow: true,
			queryInfo: null,
			confirmShow: false,
			currentPerson: {}
		};
	},
	onLoad(option) {
		this.queryInfo = JSON.parse(decodeURIComponent(option.query));
		uni.setNavigationBarTitle({
			title: this.queryInfo.name + '成员'
		});
		this.deptId = this.queryInfo.id;
		this.getUserList();
	},
	methods: {
		itemClick(info) {
			this.confirmShow = true;
			this.currentPerson = info;
		},
		confirmConfirm() {
			let params = {};
			params.taskId = this.queryInfo?.taskId;
			params.assignee = this.currentPerson?.userId;
			this.switchTaskTo(params);
		},
		// 转派任务
		async switchTaskTo(params) {
			try {
				const res = await switchTask(params);
				// this.$modal.msgSuccess(res.msg);
				this.$refs.uToast.show(this.$setToastMsg({
					message: res.msg,
					type:'success'
				}));
				setTimeout(() => {
					// this.$tab.navigateTo(`/pages/common/myTaskList/index?title=${this.queryInfo.title}&path=${this.queryInfo.path}`)
					uni.navigateBack({
						delta: 3
					});
				}, 800);
			} catch (error) {}
		},
		confirmCancel() {
			this.confirmShow = false;
			this.currentPerson = {};
			setTimeout(() => {
				// this.$tab.navigateTo(`/pages/common/myTaskList/index?title=${this.queryInfo.title}&path=${this.queryInfo.path}`)
				uni.navigateBack({
					delta: 2
				});
			}, 800);
		},
		//获取输入框的值
		onKeyInput(event) {
			this.filterText = event.target.value;
			this.getUserList();
		},
		makePhone(value) {
			if (!value) {
				uni.showToast({
					title: '该用户没有电话信息',
					icon: 'error',
					mask: true,
					duration: 1500
				});
				return;
			}
			uni.makePhoneCall({
				phoneNumber: value,
				success: (res) => {
					console.log(res);
				},
				// 失败回调
				fail: (res) => {
					console.log(res);
				}
			});
		},
		async getUserList() {
			try {
				const res = await getUsersList({ userName: this.filterText ? this.filterText : '', deptId: this.deptId });
				this.workerList = res.rows;
			} catch (e) {
				//TODO handle the exception
			}
		}
		// // 2022.5.27-点击跳转，展示该人员的详细信息
		// showPersonDetail(item){
		// 	this.isShow = !this.isShow;
		// 	console.log("detail",item)
		// }
	}
};
</script>

<style lang="scss" scoped>
page {
	background-color: #ffffff;
}

.page-container__inner {
	padding-top: 10px;
}

.user {
	height: 120upx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 20upx;
	padding: 0 20upx;
	border-bottom: 1upx solid #c0c0c0;

	.user-name {
		font-size: 18px;
		display: flex;
		flex: 1;
		width: 100px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		.user-name__inner {
			width: 100px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	.ancestors-name {
		font-size: 12px;
		color: #7f7f7ffe;
		display: flex;
		align-items: center;
	}
	.user-number {
		font-size: 16px;
		display: flex;
	}
}

::v-deep .uni-input {
	margin-bottom: 6upx;
}
</style>
