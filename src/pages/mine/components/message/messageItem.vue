<template>
	<view>
		<uni-card style="padding: 0">
			<u-collapse :border="false" @open="collapseOpen" @close="collapseClose">
				<u-collapse-item :name="itemData.noticeId">
					<view slot="title">
						<u-row justify="space-between" class="row-item">
							<u-col span="6" class="title-text">
								<view>{{ itemData.noticeTitle }}</view>
								<!-- readFlag : 0 未读， 1已读 -->
								<u-badge :isDot="!parseInt(itemData.readFlag)" />
							</u-col>
							<u-col span="6" text-align="right" class="content-time">
								<uni-dateformat :date="itemData.createTime"></uni-dateformat>
							</u-col>
						</u-row>
						<u--text v-if="describeShow" :text="itemData.noticeContent"></u--text>
						<u--text v-else :lines="1" :text="itemData.noticeContent"></u--text>
						<!-- <view :class="describeShow ? 'show-more-title' : 'title-describe'">{{ itemData.noticeContent }}</view> -->
					</view>

					<u-row>
						<!-- <u--text lineHeight="24" wordWrap="anywhere" size="16" type="info" :text="itemData.noticeContent"></u--text> -->
						<!-- <view class="content-text">{{ itemData.noticeContent }}</view> -->
					</u-row>
					<u-row class="row-item">
						<u-col span="9" text-align="right" class="to-detail"></u-col>
						<u-col span="3" text-align="right" class="right-btn">
							<!-- <u--text type="error" text="删除"></u--text> -->
							<u--text type="primary" v-if="noticeInfoType == 'todo' && categoryType != 'null'" text="去处理" @click="handleRead"></u--text>
							<u--text type="primary" v-else text="查看详情" @click="handleRead"></u--text>
							<!-- <view class="iconfont icon-right menu-icon"></view> -->
						</u-col>
					</u-row>
					<u-line color="#333333" />
				</u-collapse-item>
			</u-collapse>
		</uni-card>
	</view>
</template>

<script>
import { getNoticeInfo } from '@/api/system/user';
export default {
	data() {
		return {
			noticeInfoType: '',
			categoryType: '',
			noticeInfo: {},
			describeShow: false
		};
	},
	props: {
		itemData: Object
	},
	methods: {
		handleRead() {
			this.itemData.noticeInfoType = this.noticeInfoType;
			this.itemData.categoryType = this.categoryType;
			const payload = { ...this.itemData, ...this.noticeInfo };
			this.$emit('handleRead', payload);
		},
		async collapseOpen(noticeId) {
			this.$emit('showLoading', true);
			this.describeShow = true;
			const noticeInfo = await getNoticeInfo(noticeId);
			this.$emit('showLoading', false);
			this.noticeInfo = noticeInfo.data;
			this.noticeInfoType = noticeInfo.data.type.substring(noticeInfo.data.type.lastIndexOf('_') + 1);
			this.categoryType = noticeInfo.data.type.substring(0, noticeInfo.data.type.lastIndexOf('_'));
		},
		collapseClose() {
			this.describeShow = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.row-item {
	height: 50px;
}

.title-text {
	font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
	font-weight: 700;
	font-size: 18px;
	color: #0d0101;
	display: flex;
	flex-direction: row;
	align-items: center !important;
}
.title-describe {
	width: 18.75rem;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.show-more-title {
	white-space: unset;
}

.u-badge {
	margin-left: 8px;
}

.content-time {
	font-family: '微软雅黑', sans-serif;
	font-weight: 400;
	font-size: 14px;
	color: #7f7f7f;
}

.content-text {
	font-family: '微软雅黑', sans-serif;
	font-weight: 400;
	font-size: 16px;
	color: #7f7f7f;
	// white-space: nowrap;
	// overflow: hidden;
	// text-overflow: ellipsis;
}

.to-detail {
	font-family: '微软雅黑', sans-serif;
	font-weight: 400;
	font-size: 14px;
	color: #0d0101;
}
.right-btn {
	flex-direction: row;
}
</style>
