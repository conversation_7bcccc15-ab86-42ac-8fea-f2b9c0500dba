<template>
  <view class="topBgBox problemReportPage">
    <top-navbar
      bgColor="#AA001E"
      :title="'人脸识别照片上传'"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="bgCard">
      <view class="faceImgBox">
        <view class="faceImgBoxTitle">上传照片</view>
        <view class="faceImgBoxInfo">当前用户: {{ userName }}</view>
        <view>
          <u-form
            :model="itemData"
            label-width="80"
            ref="formRef"
            @submit.prevent
          >
            <u-form-item label="" prop="ignorePicture" borderBottom>
              <view style="position: relative">
                <u-avatar
                  :src="faceImage"
                  @click="bindChange"
                  shape="square"
                  size="200rpx"
                ></u-avatar>
                <view class="setIcon" v-show="faceImage">
                  <u-icon
                    name="close"
                    @click="deleteIcon"
                    class="iconStyle"
                    size="15"
                  ></u-icon>
                </view>
              </view>
              <!-- <u-upload
                :fileList="fileList1"
                @afterRead="afterRead"
                @delete="deletePic"
                @beforeRead="beforeRead"
                name="1"
                :multiple="false"
                :maxCount="1"
                accept="image"
                capture="['camera ', 'album']"
                uploadText="上传人脸照片"
              /> -->
            </u-form-item>
          </u-form>
        </view>
        <view>
          1、不要配戴眼镜；<br />2、头发不要遮住额头和眉骨；<br />3、背景纯色（白墙即可）；<br />4、显示区域为肩膀以上人脸部分（勿半身）；<br />5、光线充足。
        </view>
      </view>
      <u-toast ref="uToast"></u-toast>
      <u-loading-page
        :loading="showLoding"
        loading-color="#19616b"
        fontSize="16"
        icon-size="36"
        bg-color="rgba(232,232,232,0.6)"
        loadingText="照片上传中"
      ></u-loading-page>
    </view>
  </view>
</template>

<script>
import { getApkUpdate } from '@/api/cloudData/index.js';
import { getFaceImage, setFaceImage } from '@/api/system/user.js';
import config from '@/config.js';
import { getToken } from '@/utils/auth.js';
export default {
  data() {
    return {
      itemData: {},
      leftText: '',
      autoBack: true,
      fileList1: [],
      showLoding: false,
      faceImage: '',
      header: {
        Authorization: 'Bearer ' + getToken(),
      },
      uploadUrl: config.baseUrl + '/transitAuth/setFaceImage',

      version: getApp().globalData.config.appInfo.version,
      list: [
        {
          icon: 'iconfont icon-help',
          title: '其他问题',
          childList: [
            {
              title: '如何退出登录？',
              content: '请点击[我的] - [应用设置] - [退出登录]即可退出登录',
            },
            {
              title: '如何修改用户头像？',
              content: '请点击[我的] - [选择头像] - [点击提交]即可更换用户头像',
            },
            {
              title: '如何修改登录密码？',
              content: '请点击[我的] - [应用设置] - [修改密码]即可修改登录密码',
            },
          ],
        },
      ],
    };
  },
  computed: {
    userName() {
      return this.$store.state.user.name;
    },
  },
  onUnload() {
    this.$closePreview();
  },
  methods: {
    /* 打开图片组件*/
    showPreFile() {
      this.$openPreview(this.faceImage);
    },
    deleteIcon() {
      this.faceImage = null;
    },
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1);
    },
    beforeRead(file, list) {
      console.log(file);
      return true;
    },
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileListLen = this[`fileList${event.name}`].length;
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中',
        });
      });

      for (let i = 0; i < lists.length; i++) {
        this.isShowOverlay = true;
        const result = await this.uploadFilePromise(lists[i].url);

        this.isShowOverlay = false;
        let item = this[`fileList${event.name}`][fileListLen];
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            ...result,
          })
        );
        fileListLen++;
      }
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: this.uploadUrl,
          filePath: url,
          name: 'file',
          header: this.header,
          success: (res) => {
            this.showLoding = false;
            if (res.code == 200) {
              this.$refs.uToast.show(
                this.$setToastMsg({
                  message: '上传成功',
                  type: 'success',
                })
              );

              setTimeout(() => {
                this.getFaceData();
              }, 1000);
            } else {
              this.$setToastMsg({
                message: res.msg,
                type: 'error',
              });
            }
          },
        });
      });
    },
    bindChange(e) {
      if (this.faceImage) {
        this.showPreFile();
        return;
      }
      const sourceType = ['camera ', 'album'];
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        sourceType,
        success: (res) => {
          console.log(res);
          uni.getImageInfo({
            src: res.tempFilePaths[0],
            success: (image) => {
              this.showLoding = true;
              let type;
              // #ifdef MP-WEIXIN
              type = image.type;
              // #endif
              // #ifdef H5
              type = res.tempFiles[0].type;
              // #endif
              this.$compressImageFile({
                file: res.tempFiles[0],
                path: res.tempFiles[0].path,
                type,
                size: res.tempFiles[0].size,
                quality: 0.9,
                width: image.width,
                height: image.height,
                threshold: 200,
                name: res.tempFiles[0].name || '',
                compressUntilSizeBelowThreshold: true,
              }).then((res) => {
                if ([2, 4, 5, 6].includes(res.status)) {
                  this.showLoding = false;
                  this.$modal.msgError(res.msg);
                } else {
                  this.uploadFilePromise(res.src).catch((err) => {
                    this.showLoding = false;
                  });
                }
              });
            },
          });
        },
      });
    },
    async getFaceData() {
      let res = await getFaceImage();
      this.faceImage = res.data.faceImageUrl;
    },
    // async setFaceData(data) {
    //   let res = await setFaceImage({ faceImageUrl: data });
    //   if (res.code == 200) {
    //     this.$refs.uToast.show(
    //       this.$setToastMsg({
    //         message: '上传成功',
    //         type: 'success',
    //       })
    //     );
    //     this.getFaceData();
    //   }
    // },
    // handleUpdate() {
    //   //#ifdef APP-PLUS
    //   // 获取本地应用资源版本号
    //   plus.runtime.getProperty(plus.runtime.appid, (inf) => {
    //     let platform = uni.getSystemInfoSync().platform;
    //     //获取服务器的版本号
    //     let params = {
    //       type: platform == 'android' ? 1 : 2,
    //       editionNumber: inf.versionCode,
    //     };
    //     getApkUpdate(params).then((res) => {
    //       if (res.data) {
    //         let updateData = {
    //           describe: res.data.describe,
    //           edition_url: res.data.editionUrl, // //安装包下载地址或者通用应用市场地址
    //           edition_force: Number(res.data.editionForce), //是否强制更新 0代表否 1代表是
    //           package_type: Number(res.data.packageType), // 0是整包升级 1是wgt升级
    //           edition_number: Number(res.data.editionNumber),
    //           edition_name: res.data.editionName,
    //           edition_issue: Number(res.data.editionIssue),
    //         };

    //         if (
    //           Number(updateData.edition_number) > Number(inf.versionCode) &&
    //           updateData.edition_issue == 1
    //         ) {
    //           if (platform == 'ios') {
    //             setTimeout(() => {
    //               uni.navigateTo({
    //                 url:
    //                   '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
    //                   JSON.stringify(updateData),
    //               });
    //             }, 1000);
    //           } else if (platform == 'android') {
    //             setTimeout(() => {
    //               uni.navigateTo({
    //                 url:
    //                   '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' +
    //                   JSON.stringify(updateData),
    //               });
    //             }, 1000);
    //           }
    //         }
    //       } else {
    //         this.$modal.confirm('已是最新版本').then(() => {});
    //       }
    //     });
    //   });
    //   // #endif
    //   //#ifdef H5
    //   this.$modal.confirm('已是最新版本').then(() => {});
    //   //#endif
    // },
    // handleText(item) {
    //   this.$tab.navigateTo(
    //     `/pages/common/textview/index?title=${item.title}&content=${item.content}`
    //   );
    // },
  },
  onShow() {
    //#ifdef APP-PLUS
    plus.runtime.getProperty(plus.runtime.appid, (inf) => {
      this.version = inf.version;
    });
    // #endif
  },
  onLoad() {
    this.getFaceData();
  },
};
</script>

<style lang="scss" scoped>
.setIcon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: #ccc;
  border-radius: 0 0 0 100%;
  padding: 4rpx 0 4rpx 8rpx;
  .iconStyle {
  }
}
.faceImgBox {
  font-size: 32rpx;
  width: 60%;
  margin: 0 auto;
  padding-top: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .faceImgBoxTitle {
    font-weight: bold;
    font-size: 46rpx;
    margin-bottom: 20rpx;
  }
  .faceImgBoxInfo {
    margin-bottom: 20rpx;
  }
}
</style>
