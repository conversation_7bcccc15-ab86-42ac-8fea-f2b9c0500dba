<template>
	<view class="topBgBox problemReportPage">
		<top-navbar bgColor="#AA001E" :title="'版本信息'" :autoBack="autoBack" :leftText="leftText"></top-navbar>
		<view class="bgCard">
			<view class="list-cell">
				<text>今世缘</text>
				<text>当前版本：v{{ version }}</text>
			</view>
			<view class="list-cell list-cell-arrow" @click="handleUpdate">
				<text>版本更新</text>
			</view>
		</view>

		<!--   <view v-for="(item, findex) in list" :key="findex" :title="item.title" class="list-title">
      <view class="text-title">
        <view :class="item.icon"></view>{{ item.title }}
      </view>
      <view class="childList">
        <view v-for="(child, zindex) in item.childList" :key="zindex" class="question" hover-class="hover"
          @click="handleText(child)">
          <view class="text-item">{{ child.title }}</view>
          <view class="line" v-if="zindex !== item.childList.length - 1"></view>
        </view>
      </view>
    </view> -->
	</view>
</template>

<script>
import { getApkUpdate } from '@/api/cloudData/index.js';
export default {
	data() {
		return {
			leftText: '',
			autoBack: true,
			version: getApp().globalData.config.appInfo.version,
			list: [
				{
					icon: 'iconfont icon-help',
					title: '其他问题',
					childList: [
						{
							title: '如何退出登录？',
							content: '请点击[我的] - [应用设置] - [退出登录]即可退出登录'
						},
						{
							title: '如何修改用户头像？',
							content: '请点击[我的] - [选择头像] - [点击提交]即可更换用户头像'
						},
						{
							title: '如何修改登录密码？',
							content: '请点击[我的] - [应用设置] - [修改密码]即可修改登录密码'
						}
					]
				}
			]
		};
	},
	methods: {
		handleUpdate() {
			console.log('ddd');
			//#ifdef APP-PLUS
			// 获取本地应用资源版本号
			plus.runtime.getProperty(plus.runtime.appid, (inf) => {
				let platform = uni.getSystemInfoSync().platform;
				//获取服务器的版本号
				let params = {
					type: platform == 'android' ? 1 : 2,
					editionNumber: inf.versionCode
				};
				getApkUpdate(params).then((res) => {
					if (res.data) {
						let updateData = {
							describe: res.data.describe,
							edition_url: res.data.editionUrl, // //安装包下载地址或者通用应用市场地址
							edition_force: Number(res.data.editionForce), //是否强制更新 0代表否 1代表是
							package_type: Number(res.data.packageType), // 0是整包升级 1是wgt升级
							edition_number: Number(res.data.editionNumber),
							edition_name: res.data.editionName,
							edition_issue: Number(res.data.editionIssue)
						};

						if (Number(updateData.edition_number) > Number(inf.versionCode) && updateData.edition_issue == 1) {
							if (platform == 'ios') {
								setTimeout(() => {
									uni.navigateTo({
										url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' + JSON.stringify(updateData)
									});
								}, 1000);
							} else if (platform == 'android') {
								setTimeout(() => {
									uni.navigateTo({
										url: '/uni_modules/rt-uni-update/components/rt-uni-update/rt-uni-update?obj=' + JSON.stringify(updateData)
									});
								}, 1000);
							}
						}
					} else {
						this.$modal.confirm('已是最新版本').then(() => {});
					}
				});
			});
			// #endif
			//#ifdef H5
			this.$modal.confirm('已是最新版本').then(() => {});
			//#endif
		},
		handleText(item) {
			this.$tab.navigateTo(`/pages/common/textview/index?title=${item.title}&content=${item.content}`);
		}
	},
	onShow() {
		//#ifdef APP-PLUS
		plus.runtime.getProperty(plus.runtime.appid, (inf) => {
			this.version = inf.version;
		});
		// #endif
	}
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.help-container {
	margin-bottom: 100rpx;
	padding: 30rpx;
}
.version-info {
	margin-top: 10px;
	padding: 0;
}
.list-title {
	margin-bottom: 30rpx;
}

.childList {
	background: #ffffff;
	box-shadow: 0px 0px 10rpx rgba(193, 193, 193, 0.2);
	border-radius: 16rpx;
	margin-top: 10rpx;
}

.line {
	width: 100%;
	height: 1rpx;
	background-color: #f5f5f5;
}

.text-title {
	color: #303133;
	font-size: 32rpx;
	font-weight: bold;
	margin-left: 10rpx;

	.iconfont {
		font-size: 16px;
		margin-right: 10rpx;
	}
}

.text-item {
	font-size: 28rpx;
	padding: 24rpx;
}

.question {
	color: #606266;
	font-size: 28rpx;
}
</style>
