<template>
  <view class="mine-container">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="top-box">
        <view class="title-box">
          <u-avatar
            :default-url="globalConfig.iconImgPrefix + 'minedefaultavart.png'"
            size="126rpx"
          ></u-avatar>
          <!-- <view
            v-if="!avatar"
            class="avatarImg bg-white"
          >
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image
            v-else
            :src="avatar"
            class="avatarImg"
            mode="widthFix"
          ></image> -->
          <view v-if="!name" @click="handleToLogin" class="login-tip"
            >点击登录</view
          >
          <view v-if="name" class="user-info">
            <view class="u_title">
              {{ name }}
            </view>
            <view class="sub_title">
              {{ phoneNumber }}
            </view>
            <view class="sub_title">
              {{ deptName }}
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="woker-list">
        <view
          v-for="(item, index) in wokerList"
          :key="index"
          class="woker-list-item"
        >
          <view>
            <text class="woker-list__name">{{ item.name }}</text>
            <text class="woker-list__number">{{ item.number }}</text>
          </view>

          <text v-if="index == 0" class="decollator">/</text>
        </view>
      </view> -->
    </view>
    <view>
      <!-- <scroll-view scroll-y="true"> -->
      <view class="content-section">
        <view class="menu-list">
          <!-- <view
            class="list-cell list-cell-arrow message-box"
            @click="handleMessageCenter"
          >
            <view class="menu-item-box">
              <u--image
                class="menuListIcon"
                :src="globalConfig.iconImgPrefix + 'xiaoxizhongxin.png'"
              ></u--image>
              <view class="menuTitle">消息中心</view>
            </view>
            <u-badge type="error" max="99" :value="unreadNum"></u-badge>
          </view> -->
          <!-- <view class="list-cell list-cell-arrow" @click="handleToAddressBook">
						<view class="menu-item-box">
							<u--image class="menuListIcon" :src="globalConfig.iconImgPrefix + 'tongxunlu.png'"></u--image>
							<view class="menuTitle">通讯录</view>
						</view>
					</view> -->
          <!-- <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
            <view class="menu-item-box">
              <u--image
                class="menuListIcon"
                :src="globalConfig.iconImgPrefix + 'xiugaiziliao.png'"
              ></u--image>
              <view class="menuTitle">修改资料</view>
            </view>
          </view> -->
          <!-- <view class="list-cell list-cell-arrow" @click="handleToPwd">
            <view class="menu-item-box">
              <u--image
                class="menuListIcon pwIcon"
                :src="globalConfig.iconImgPrefix + 'xiugaimima.png'"
              ></u--image>
              <view class="menuTitle">修改密码</view>
            </view>
          </view> -->
          <view class="list-cell list-cell-arrow" @click="handleFace">
            <view class="menu-item-box">
              <u--image
                class="menuListIcon"
                :src="globalConfig.iconImgPrefix + 'renlian.png'"
              ></u--image>
              <view class="menuTitle">人脸识别照片上传</view>
            </view>
          </view>
          <view class="list-cell list-cell-arrow" @click="handleVersion">
            <view class="menu-item-box">
              <u--image
                width="30px"
                height="30px"
                mode="aspectFit"
                class="menuListIcon"
                :src="globalConfig.iconImgPrefix + 'mineversion.png'"
              ></u--image>
              <view class="menuTitle">版本信息</view>
            </view>
          </view>

          <view class="list-cell list-cell-arrow" @click="handleAbout">
            <view class="menu-item-box">
              <!-- <view class="iconfont icon-aixin menu-icon"></view> -->
              <u--image
                width="30px"
                height="30px"
                mode="aspectFit"
                class="menuListIcon"
                :src="globalConfig.iconImgPrefix + 'mineabout.png'"
              ></u--image>
              <view>关于我们</view>
            </view>
          </view>
          <!-- <view class="list-cell list-cell-arrow" @click="handleLogout">
					<view class="menu-item-box">
						<view class="iconfont icon-setting menu-icon"></view>
						<view>注销账号</view>
					</view>
				</view> -->
        </view>
        <u-button
          type="primary"
          text="退出当前账号"
          @click="handleQuit"
          class="customBtn"
          size="large"
        ></u-button>
      </view>
    </view>
    <!-- </scroll-view> -->
  </view>
</template>

<script>
import storage from '@/utils/storage';
import { getUnreadNum } from '@/api/aboutTask/index';
export default {
  data() {
    return {
      globalConfig: getApp().globalData.config,
      name: this.$store.state.user.name,
      version: getApp().globalData.config.appInfo.version,
      deptName: this.$store.state.user.deptName,
      phoneNumber: this.$store.state.user.phoneNumber,
      wokerList: [
        {
          number: 0,
          name: '已办工单',
        },
        {
          number: 0,
          name: '发起工单',
        },
      ],
      unreadNum: 0,
    };
  },
  computed: {
    avatar() {
      return this.$store.state.user.avatar;
    },
  },
  onShow() {
    this.$store.dispatch('GetInfo').then((res) => {});
    this.getUnread();
  },
  methods: {
    handleToInfo() {
      uni.navigateTo({
        url: '/pages/mine/info/edit?log=new',
      });
    },
    handleToEditInfo() {
      uni.navigateTo({
        url: '/pages/mine/info/edit?log=edit',
      });
      // this.$tab.navigateTo({
      // 	url: '/pages/mine/info/edit',
      // 	query: {
      // 		log: "new"
      // 	}
      // })
    },
    handleMessageCenter() {
      uni.navigateTo({
        url: '/pages/mine/messageCenter/index',
      });
    },
    handleToAddressBook() {
      this.$tab.navigateTo('/pages/mine/addressBook/index');
    },
    handleToSetting() {
      this.$tab.navigateTo('/pages/mine/setting/index');
    },
    handleToLogin() {
      this.$tab.reLaunch('/pages/login');
    },
    handleToAvatar() {
      this.$tab.navigateTo('/pages/mine/avatar/index');
    },
    handleToPwd() {
      this.$tab.navigateTo('/pages/mine/pwd/index');
    },
    handleLogout() {
      this.$modal.confirm('确定注销并退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$tab.reLaunch('/pages/index');
        });
      });
    },
    handleQuit() {
      this.$modal.confirm('确定退出系统吗？').then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$tab.reLaunch('/pages/index');
        });
      });
    },
    handleVersion() {
      this.$tab.navigateTo('/pages/mine/help/index');
    },
    handleFace() {
      this.$tab.navigateTo('/pages/mine/faceData/index');
    },
    handleAbout() {
      this.$tab.navigateTo('/pages/mine/about/index');
    },
    handleBuilding() {
      this.$modal.showToast('模块建设中~');
    },
    async getUnread() {
      try {
        const res = await getUnreadNum();
        this.unreadNum = res?.data;
      } catch (error) {}
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  background-image: url('@/static/images/bg.png');
  background-repeat: repeat;
  background-size: contain;
}
.header-section {
  background-image: url(@/static/images/iconImg/minebg.png);
  background-size: cover;
}
</style>
