<template>
	<view class="topBgBox problemReportPage">
		<top-navbar bgColor="#AA001E" :title="'修改资料'" :autoBack="autoBack" :leftText="leftText"></top-navbar>
		<view class="bgCard">
			<view class="flex justify-center" style="padding-top: 10px">
				<view v-if="!avatar" @click="handleToAvatar" class="cu-avatar xl round bg-white">
					<view class="iconfont icon-people text-gray icon"></view>
				</view>
				<image v-else @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix"></image>
			</view>
			<view class="example">
				<uni-forms ref="form" :model="user" labelWidth="80px">
					<uni-forms-item label="用户名称" name="userName">
						<u--input disabled v-model="user.userName" placeholder="请输入用户名称" />
					</uni-forms-item>
					<uni-forms-item label="用户昵称" name="nickName">
						<u--input :disabled="pageLog" v-model="user.nickName" placeholder="请输入昵称" />
					</uni-forms-item>
					<uni-forms-item label="手机号码" name="phonenumber">
						<u--input v-model="user.phonenumber" placeholder="请输入手机号码" />
					</uni-forms-item>
					<uni-forms-item label="邮箱" name="email">
						<u--input :disabled="pageLog" v-model="user.email" placeholder="请输入邮箱" />
					</uni-forms-item>
					<uni-forms-item label="所属部门" name="deptName">
						<u--input disabled v-model="user.deptName" placeholder="请输入部门" />
					</uni-forms-item>
					<uni-forms-item label="创建日期" name="createTime">
						<uni-dateformat :date="user.createTime"></uni-dateformat>
					</uni-forms-item>
					<uni-forms-item label="性别" name="sex" required>
						<uni-data-checkbox selectedColor="#AA001E" :disabled="pageLog" v-model="user.sex" :localdata="sexs" />
					</uni-forms-item>
				</uni-forms>
				<view class="probelmReportBtnBox">
					<u-button v-if="!pageLog" class="customBtn" type="primary" text="提交" @click="submit" />
				</view>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { getUserId } from '@/utils/auth';
import { getUserProfile, updateUserProfile, getUserInfo } from '@/api/system/user';

export default {
	data() {
		return {
			leftText: '',
			autoBack: true,
			userId: getUserId(),
			user: {
				userName: '',
				nickName: '',
				phonenumber: '',
				email: '',
				deptName: '',
				createTime: '',
				sex: ''
			},
			sexs: [
				{
					text: '男',
					value: '1'
				},
				{
					text: '女',
					value: '2'
				}
			],
			deptNameList: [],
			rules: {
				nickName: {
					rules: [
						{
							required: true,
							errorMessage: '用户昵称不能为空'
						}
					]
				},
				phonenumber: {
					rules: [
						{
							required: true,
							errorMessage: '手机号码不能为空'
						},
						{
							pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
							errorMessage: '请输入正确的手机号码'
						}
					]
				},
				email: {
					rules: [
						{
							required: true,
							errorMessage: '邮箱地址不能为空'
						},
						{
							format: 'email',
							errorMessage: '请输入正确的邮箱地址'
						}
					]
				}
			},
			pageLog: ''
		};
	},
	onLoad(option) {
		const log = option.log;
		this.pageLog = log === 'new';
		this.setTitle();
	},
	onReady() {
		this.$refs.form.setRules(this.rules);
	},
	computed: {
		avatar() {
			return this.$store.state.user.avatar;
		},
		windowHeight() {
			return uni.getSystemInfoSync().windowHeight - 50;
		}
	},
	methods: {
		handleToAvatar() {
			this.$tab.navigateTo('/pages/mine/avatar/index');
		},
		submit(ref) {
			this.$refs.form.validate().then((res) => {
				updateUserProfile(this.user).then((response) => {
					// this.$modal.msgSuccess('修改成功');
					this.$refs.uToast.show(this.$setToastMsg({
						message:'修改成功',
						type:'success'
					}));
					
				});
			});
		},
		async getUserInfo() {
			try {
				this.loading = true;
				const res = await getUserInfo(this.userId);
				this.user = res.data.user;
				this.deptNameList = res.data.posts;
				this.deptNameList.forEach((item, index) => {
					item.value = item.postId;
					item.text = item.postName;
				});
			} catch (err) {}
			this.loading = false;
		},
		// 设置title
		setTitle() {
			let title = this.pageLog ? '个人信息' : '编辑资料';
			uni.setNavigationBarTitle({
				title
			});
			this.getUserInfo();
		}
	}
};
</script>

<style lang="scss" scoped>
page {
	background-color: #ffffff;
}

::v-deep .uni-forms-item__content {
	display: flex;
	align-items: center;
}

.cu-avatar {
	border: 2px solid #eaeaea;
	width: 120px;
	height: 120px;
}

.example {
	padding: 15px;
	background-color: #fff;
}

.segmented-control {
	margin-bottom: 15px;
}

.button-group {
	margin-top: 15px;
	display: flex;
	justify-content: space-around;
}

.form-item {
	display: flex;
	align-items: center;
	flex: 1;
}

.button {
	display: flex;
	align-items: center;
	height: 35px;
	line-height: 35px;
	margin-left: 10px;
}
</style>
