<template>
	<view class="content_container topBgBox">
		<top-navbar bgColor="#AA001E" :title="topTitle" :autoBack="autoBack" :leftText="leftText">
			<view slot="title-right" class="rightbar-right">
				<u--text text="全部已读" :type="isallReaded ? 'info' : 'success'" @click="handleAllRead" />
				<u-icon name="checkmark-circle" :color="isallReaded ? 'info' : 'success'" />
			</view>
		</top-navbar>
		<view class="bgCard">
			<u-list class="content-down" @scrolltolower="scrolltolower">
				<u-list-item v-for="item in noticeList" :key="item.noticeId">
					<message-item :item-data="item" @handleRead="handleRead" @showLoading="showLoading" />
				</u-list-item>
			</u-list>
			<!-- <view class="content-down">
				<view v-for="item in noticeList" :key="item.noticeId">
					<message-item :item-data="item" @handleRead="handleRead" />
				</view>
			</view> -->
		</view>
		<u-loading-page :loading="loading" loadingText="请稍后" />
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import MessageItem from '@/pages/mine/components/message/messageItem.vue';
import TopNavbar from '@/components/top-navbar/top-navbar.vue';
import { getUserId } from '@/utils/auth';
import { getNoticeList, updateReadStatus } from '@/api/system/user';
export default {
	components: {
		MessageItem,
		TopNavbar
		// detailPopup
	},
	data() {
		return {
			leftText: '',
			autoBack: true,
			loading: false,
			topTitle: '消息中心',
			userId: getUserId(),
			noticeList: [],
			isallReaded: false,
			totalPage: 0,
			queryData: {
				pageNum: 1,
				pageSize: 10
			}
		};
	},
	onLoad() {
		this.getNoticeListInfo();
	},
	methods: {
		showLoading(data) {
			this.loading = data;
		},
		scrolltolower() {
			this.queryData.pageNum++;
			if (this.queryData.pageNum > this.totalPage) {
				return this.$refs.uToast.show(this.$setToastMsg({
					message:'已加载全部',
					type:'success'
				}));
				// this.$modal.msgError('已加载全部');
			}
			this.getNoticeListInfo();
		},
		// 全部已读
		async handleRead(item) {
			let params = {};
			// 全选
			if (item === 'all') {
				let unreadList = this.noticeList.filter((item) => item.readFlag === '0');
				let unreadNoticeIds = [];
				unreadList.forEach((element) => {
					unreadNoticeIds.push(element.noticeId);
				});
				if (unreadNoticeIds.length === 0) return;
				params = {
					userId: this.userId,
					noticeIdList: []
				};
			} else {
				params = {
					userId: this.userId,
					noticeId: item.noticeId,
					noticeIdList: [item.noticeId]
				};
			}
			try {
				await updateReadStatus(params);
				if (item !== 'all') {
					if (item.noticeInfoType == 'notice' && !item.categoryType) {
						uni.navigateTo({
							url: `/pages/mine/messageCenter/messageDetail?query=${encodeURIComponent(JSON.stringify(item))}`
						});
					} else {
						this.handleMessageDetail(item);
					}
				} else {
					this.noticeList = [];
					this.getNoticeListInfo();
				}
			} catch (error) {}
		},
		// 全部已读
		handleAllRead() {
			if (this.loading == false) {
				let resder = 'all';
				this.handleRead(resder);
			}
		},
		// 去消息详情
		handleMessageDetail(data) {
			switch (data.categoryType) {
				case 'deviceExpiration':
					uni.navigateTo({
						url: `/pages/common/deviceManage/deviceDetail/index?deviceId=${data.deviceId}`
					});
					break;
				case 'alarm':
					uni.navigateTo({
						url: `/pages/common/warnManage/warnDetail/index?alarmId=${data.alarmId}`
					});
					break;
				default:
					this.checkMsgDetail(data);
			}
		},

		checkMsgDetail(data) {
			let queryData = {
				procInsId: data.procInstId,
				deployId: data.deployId,
				taskId: data.taskId || '',
				taskName: data.taskName,
				startUser: data.startUserName,
				category: data.categoryType,
				procDefId: data.procDefId,
				taskDefKey: data.taskDefKey,
				path: data.noticeInfoType === 'todo' ? 'todoList' : ''
			};
			if (data.noticeInfoType === 'todo') {
				queryData.taskId = data.taskId;
			} else {
				queryData.taskId = data.procInstId;
			}
			const { categoryType } = data;

			if (['device_poll', 'device_upkeep'].includes(categoryType)) {
				//巡检保养
				uni.navigateTo({
					url: `/pages/common/checkRecord/detail?query=${encodeURIComponent(JSON.stringify(queryData))}`
				});
			} else if (categoryType === 'other_order') {
				//其他工单（H5）
				uni.navigateTo({
					url: `/pages/common/myTaskList/detail?query=${encodeURIComponent(JSON.stringify(query))}`
				});
			} else {
				let params = {
					...queryData,
					routeOption: {
						path: queryData.path,
						category: queryData.category
					}
				};

				uni.navigateTo({
					url: `/pages/common/myTaskList/detailPopup/index?query=${encodeURIComponent(JSON.stringify(params))}`
				});
			}
		},

		closePopup() {},
		// 获取消息中心列表
		async getNoticeListInfo() {
			this.loading = true;
			try {
				const res = await getNoticeList(this.queryData);
				this.loading = false;
				let taskData = {
					list: [],
					total: 0
				};
				if (res.rows) {
					taskData.list = res.rows;
					taskData.total = res.total;
				}
				this.totalPage = Math.ceil(taskData.total / this.queryData.pageSize);
				if (taskData.list.length) {
					this.noticeList.push(...taskData.list);
					let unreadlist = this.noticeList.filter((item) => item.readFlag === '0');
					this.isallReaded = unreadlist.length === 0;
				}
			} catch (e) {
				console.log(e);
				this.loading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.list-cell {
	margin: 50px 15px 0;
	width: auto;
}

.rightbar-right {
	display: flex;
}
</style>
