<template>
	<view>
		<view>
			<uni-card>
				<u-cell v-for="(item, key) in noticeDetailLabel" :key="item">
					<view slot="label">
						<u-row>
							<u-col :span="4" style="color: #aaaaaa">{{ item }}</u-col>
							<u-col :span="8" v-if="key == 'noticeContent'">
								<rich-text :nodes="noticeDetail[key]"></rich-text>
							</u-col>
							<u-col :span="8" v-else>{{ noticeDetail[key] }}</u-col>
						</u-row>
					</view>
				</u-cell>
			</uni-card>
		</view>
		<u-loading-page :loading="loading" loadingText="请稍后" bg-color="rgba(0, 0, 0, 0.3)" />
	</view>
</template>

<script>
import { getAlarmInfo } from '@/api/system/user';
export default {
	components: {},
	data() {
		return {
			loading: false,
			noticeId: '',
			noticeDetailLabel: {
				noticeTitle: '消息标题',
				businessTypeName: '消息类型',
				noticeContent: '消息内容'
			},
			noticeDetail: {
				noticeTitle: '',
				businessTypeName: '',
				noticeContent: ''
			},
			queryParam: {}
		};
	},
	onLoad(option) {
		this.queryParam = JSON.parse(decodeURIComponent(option.query));
		this.noticeDetail.noticeTitle = this.queryParam.noticeTitle;
		this.noticeDetail.businessTypeName = this.queryParam.businessTypeName;
		this.noticeDetail.noticeContent = this.queryParam.noticeContent;
		// this.getMessageinfo();
	},
	methods: {
		async getMessageinfo() {
			this.loading = true;
			try {
				await getAlarmInfo(this.noticeId);
				this.loading = false;
			} catch (error) {
				this.loading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped></style>
