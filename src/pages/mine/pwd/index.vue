<template>
	<view class="topBgBox problemReportPage">
		<top-navbar bgColor="#AA001E" :title="'修改密码'" :autoBack="autoBack" :leftText="leftText"></top-navbar>
		<view class="bgCard">
			<view class="example">
				<uni-forms ref="form" :value="user" labelWidth="80px" class="page-container__inner">
					<uni-forms-item name="oldPassword" label="旧密码">
						<u--input type="password" v-model="user.oldPassword" placeholder="请输入旧密码" />
					</uni-forms-item>
					<uni-forms-item name="newPassword" label="新密码">
						<u--input type="password" v-model="user.newPassword" placeholder="请输入新密码" />
					</uni-forms-item>
					<uni-forms-item name="confirmPassword" label="确认密码">
						<u--input type="password" v-model="user.confirmPassword" placeholder="请确认新密码" />
					</uni-forms-item>
					<view class="probelmReportBtnBox">
						<u-button class="customBtn" type="primary" text="提交" @click="submit" />
					</view>
				</uni-forms>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import { updateUserPwd } from '@/api/system/user';

export default {
	data() {
		return {
			leftText: '',
			autoBack: true,
			user: {
				oldPassword: undefined,
				newPassword: undefined,
				confirmPassword: undefined
			},
			rules: {
				oldPassword: {
					rules: [
						{
							required: true,
							errorMessage: '旧密码不能为空'
						}
					]
				},
				newPassword: {
					rules: [
						{
							required: true,
							errorMessage: '新密码不能为空'
						},
						{
							minLength: 6,
							maxLength: 20,
							errorMessage: '长度在 6 到 20 个字符'
						}
					]
				},
				confirmPassword: {
					rules: [
						{
							required: true,
							errorMessage: '确认密码不能为空'
						},
						{
							validateFunction: (rule, value, data) => data.newPassword === value,
							errorMessage: '两次输入的密码不一致'
						}
					]
				}
			}
		};
	},
	onReady() {
		this.$refs.form.setRules(this.rules);
	},
	methods: {
		submit() {
			this.$refs.form.validate().then((res) => {
				updateUserPwd(this.user.oldPassword, this.user.newPassword).then((response) => {
					// this.$modal.msgSuccess('修改成功');
					this.$refs.uToast.show(this.$setToastMsg({
						message:'修改成功',
						type:'success'
					}));
				});
			});
		}
	}
};
</script>

<style lang="scss">
.page-container__inner {
	padding: 15px;
}
.example {
	padding: 15px;
	background-color: #fff;
}
</style>
