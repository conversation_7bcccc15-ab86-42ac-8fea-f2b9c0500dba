<template>
    <view class="">
        <uni-section title="告警事件分类">
            <qiun-data-charts type="ring" :opts="typeOpts" :chartData="typeData" />
        </uni-section>
        <uni-section title="告警趋势">
            <qiun-data-charts type="line" :opts="trendOpts" :chartData="trendData" />
        </uni-section>
        <uni-section title="告警区域分布">
            <qiun-data-charts type="column" :opts="areaOpts" :chartData="areaData" :ontouch="true" :inScrollView="true" />
        </uni-section>
    </view>
</template>

<script>
import { queryAlarmEventTypeRequest, queryAlarmTrendRequest, queryAlarmAreaRequest } from '@/api/statistics/index.js';
import { alarmTypeOpts, alarmTrendOpts, alarmAreaOpts } from './options.js';

const nameTransform = {
    deviceCount: '设备故障',
    environmentCount: '环境异常',
    eventCount: '安全事件'
};

const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;

export default {
    data() {
        return {
            typeData: {},
            typeOpts: alarmTypeOpts,

            trendData: {},
            trendOpts: alarmTrendOpts,

            areaData: {},
            areaOpts: alarmAreaOpts
        };
    },
    methods: {
        async getServerData() {
            //后续dateType改为2，默认按月查询
            try {
                const typeRes = await queryAlarmEventTypeRequest({ dateType: 3 });
                this.getTypeData(typeRes.data);
                
                const trendRes = await queryAlarmTrendRequest({ dateType: 3 });
                this.getLineData(trendRes.data)
                
                const areaRes = await queryAlarmAreaRequest({ dateType: 3 });
                this.getBarData(areaRes.data)
            } catch (e) {
                console.log(e);
            }
        },
        getTypeData(val) {
            let data = [];
            Object.keys(nameTransform).forEach((key) => {
                if (val[key]) {
                    data.push({
                        name: nameTransform[key],
                        value: Number(val[key])
                    });
                }
            });
            this.typeData = {
                series: [
                    {
                        data
                    }
                ]
            };
        },

        getLineData(val) {
            //后续改为月
            const categories = val.map((item) => year + '-' + item.dateString);
            const data = val.map((item) => item.count);
            this.trendData = {
                categories,
                series: [
                    {
                        data
                    }
                ]
            };
        },
    
        getBarData(val) {
            const categories = val.map(item => item.areaName)
            const data = val.map(item => item.alarmCount)
            this.areaData = {
                categories,
                series:[
                    {
                        name:"",
                        data
                    }
                ]
            }
        }
    
    },
    mounted() {
        this.getServerData();
    }
};
</script>

<style></style>
