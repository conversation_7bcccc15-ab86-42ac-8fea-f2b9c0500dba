<template>
    <view class="">
        <uni-section title="设备类型分类">
            <qiun-data-charts type="column" :opts="typeOpts" :chartData="typeData" :ontouch="true" :inScrollView="true" />
        </uni-section>
        <uni-section title="设备状态分类">
            <qiun-data-charts type="ring" :opts="statusOpts" :chartData="statusData" />
        </uni-section>
        <uni-section title="设备告警趋势">
            <qiun-data-charts type="line" :opts="trendOpts" :chartData="trendData" :ontouch="true" :inScrollView="true" />
        </uni-section>
    </view>
</template>

<script>
import { queryAlarmTrendEchartsRequest, qureyNatureEchartsRequest, queryTypeEchartsRequest } from '@/api/statistics/index.js';
import { deviceTypeOpts, deviceStatusOpts, deviceAlarmTrendOpt } from './options.js';

const statusEnum = {
    0: '运行中',
    1: '停机离线',
    2: '计划停机',
    3: '故障停机'
};
const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;
export default {
    data() {
        return {
            typeData: {},
            typeOpts: deviceTypeOpts,

            statusData: {},
            statusOpts: deviceStatusOpts,

            trendData: {},
            trendOpts: deviceAlarmTrendOpt
        };
    },
    methods: {
        async getServerData() {
            try {
                const typeRes = await queryTypeEchartsRequest();
                this.typeData = {
                    categories: typeRes.data.typeList.map((item) => item.typeName),
                    series: [
                        {
                            name: '',
                            data: typeRes.data.typeList.map((item) => item.count)
                        }
                    ]
                };
                
                const statusRes = await qureyNatureEchartsRequest();
                this.statusData = {
                    series: [
                        {
                            data: statusRes.data.runningStatus.map((item) => {
                                return {
                                    name: statusEnum[item.type],
                                    value: item.count
                                };
                            })
                        }
                    ]
                };
                
                const trendRes = await queryAlarmTrendEchartsRequest({ dateType: 2 });

                //临时修改数据
                const { deviceAlarm, environAlarm, incidentAlarm } = trendRes.data;
                environAlarm.splice(0, 1);
                trendRes.data.abscissa.forEach((item) => {
                    deviceAlarm.push({ count: Math.floor(Math.random() * 10) });
                    environAlarm.push({ count: Math.floor(Math.random() * 10) });
                    incidentAlarm.push({ count: Math.floor(Math.random() * 10) });
                });
                this.trendData = {
                    categories: trendRes.data.abscissa.map((item) => month + '-' + item),
                    series: [
                        {
                            name: '设备告警',
                            data: deviceAlarm.map((item) => item.count)
                        },
                        {
                            name: '环境告警',
                            data: environAlarm.map((item) => item.count)
                        },
                        {
                            name: '事件告警',
                            data: incidentAlarm.map((item) => item.count)
                        }
                    ]
                };
            } catch (e) {
                console.log(e);
            }
        }
    },
    mounted() {
        this.getServerData();
    }
};
</script>

<style></style>
