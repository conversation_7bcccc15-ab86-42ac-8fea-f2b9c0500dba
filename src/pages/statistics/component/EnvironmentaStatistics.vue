<template>
    <view class="">
        <uni-section title="空气湿度趋势">
            <qiun-data-charts type="area" :opts="airHumidityOpts" :chartData="airHumidityData" :ontouch="true" :inScrollView="true" />
        </uni-section>
        <uni-section title="水质检测">
            <qiun-data-charts type="column" :opts="waterTestingOpts" :chartData="waterTestingData" />
        </uni-section>
        <uni-section title="湖泊水质(13天)">
            <qiun-data-charts type="pie" :opts="lakeWaterQualityDataOpts" :chartData="lakeWaterQualityData" :ontouch="true" :inScrollView="true" />
        </uni-section>
    </view>
</template>

<script>
import { airHumidityOpt, waterTestingOpt, lakeWaterQualityDataOpt } from './options.js';

const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;
export default {
    data() {
        return {
            airHumidityData: {},
            airHumidityOpts: airHumidityOpt,

            waterTestingData: {},
            waterTestingOpts: waterTestingOpt,

            lakeWaterQualityData: {},
            lakeWaterQualityDataOpts: lakeWaterQualityDataOpt,
        };
    },
    methods: {
        async getServerData() {
            this.getPollRecordData()
            this.getPollTemplateData()
            this.getPollTimedisData()    
        },
        async getPollRecordData() {
            this.airHumidityData = {
                categories: ['9-01', '9-02', '9-03', '9-04', '9-05', '9-06', '9-07', '9-08'],
                series: [
                    {
                        name: '湿度',
                        data: [15, 45, 35, 65, 55, 25, 60, 35],
                    }
                ]
            };
        },
        async getPollTemplateData() {
            this.waterTestingData = {
                categories: ["7-25", "7-26", "7-27", "7-28", "7-29", "7-30", "7-31"],
                series: [
                    {
                        name: "总氮(mg/L)",
                        data: [50, 70, 45, 45, 90, 40, 55]
                    },
                    {
                        name: "总磷(mg/L)",
                        data: [120, 130, 90, 110,170, 80, 120]
                    },
                    {
                        name: "叶绿素(μg/L)",
                        data: [170, 165, 140, 155, 175, 100, 140]
                    },
                ]
            };
        },
        async getPollTimedisData() {
            this.lakeWaterQualityData = {
                series: [
                    {
                        data: [
                            {
                                name: 'Ⅰ类',
                                value: 37,
                                duration: '13天'
                            },
                            {
                                name: 'Ⅱ类',
                                value: 13,
                                duration: '13天'
                            },
                            {
                                name: 'Ⅲ类',
                                value: 45,
                                duration: '13天'
                            },
                            {
                                name: 'Ⅳ类',
                                value: 3,
                                duration: '13天'
                            },
                            {
                                name: 'Ⅴ类',
                                value: 1,
                                duration: '13天'
                            },
                            {
                                name: '劣Ⅴ类',
                                value: 1,
                                duration: '13天'
                            },
                        ]
                    }

                ]
            };
            
        }
    },
    mounted() {
        this.getServerData();
    }
};
</script>

<style></style>
