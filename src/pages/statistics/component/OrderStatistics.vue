<template>
    <view class="">
        <uni-section title="工单数量趋势">
            <qiun-data-charts type="line" :opts="countTrendOpts" :chartData="orderCountData" :ontouch="true" :inScrollView="true" />
        </uni-section>
        <uni-section title="工单类型分布">
            <qiun-data-charts type="ring" :opts="orderTypesOpts" :chartData="orderTypesData" />
        </uni-section>
        <uni-section title="工单状态统计">
            <qiun-data-charts type="column" :opts="orderStatusOpts" :chartData="orderStatusData" :ontouch="true" :inScrollView="true" />
        </uni-section>
    </view>
</template>

<script>
import { 
    getOrderNumStatistics,
    getOrderTypeStatistics,
    getOrderStatusStatistics
} from '@/api/statistics/index.js';
import { orderTypesDisOpts,  orderCountTrendOpt, orderStautusOpt } from './options.js';

const statusEnum = {
    finishOrderNum: '已完成',
    todoOrderNum: '进行中',
    loseOrderNum: '已失效',
};
const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;
export default {
    data() {
        return {
            orderCountData: {},
            countTrendOpts: orderCountTrendOpt,

            orderTypesData: {},
            orderTypesOpts: orderTypesDisOpts,

            orderStatusData: {},
            orderStatusOpts: orderStautusOpt,
        };
    },
    methods: {
        async getServerData() {
            try {
                const orderStatusRes = await getOrderStatusStatistics({ dateType: 2 });
                let statusList = []
                for (const key in statusEnum) {
                    if (Object.prototype.hasOwnProperty.call(statusEnum, key)) {
                        statusList.push(
                            orderStatusRes?.data[key]
                        )
                    }
                }
                this.orderStatusData = {
                    categories: ['已完成', '进行中', '已失效'],
                    series: [
                        {
                            name: '',
                            data: statusList
                        }
                    ]
                };
                
                const orderTypesRes = await getOrderTypeStatistics({ dateType: 2 });
                this.orderTypesData = {
                    series: [
                        {
                            data: orderTypesRes?.data?.map((item) => {
                                return {
                                    name: item.categoryName,
                                    value: item.count
                                };
                            })
                        }
                    ]
                };
                
                const orderNumRes = await getOrderNumStatistics({ dateType: 2 })
                let {
                        abscissaList,
                        device_poll,
                        device_repair,
                        device_upkeep,
                        emergency,
                        hidden_trouble,
                        other_order,
                        total
                    } = orderNumRes?.data
                    let len = abscissaList.length
                    const transdata = new Array(len).fill(0);
                this.orderCountData = {
                    categories: abscissaList.map((item) => month + '-' + item),
                    series: [
                        {
                            name: '设备维修',
                            data: this.tranData(abscissaList, device_repair || [])
                        },
                        {
                            name: '问题隐患',
                            data: this.tranData(abscissaList, hidden_trouble || [])
                        },
                        {
                            name: '设备保养',
                            data: this.tranData(abscissaList, device_upkeep || [])
                        },
                        {
                            name: '设备巡检',
                            data: this.tranData(abscissaList, device_poll || []),
                            // data: (device_poll || []).map((item) => item.count)
                        },
                        {
                            name: '应急处置',
                            data: this.tranData(abscissaList, emergency || []),
                            // data: (emergency || []).map((item) => item.count)
                        },
                        {
                            name: '其他工单',
                            data: this.tranData(abscissaList, other_order || []),
                            // data: (other_order || []).map((item) => item.count)
                        }
                    ]
                };
                
            } catch (e) {
                console.log(e);
            }
        },
        tranData (abscissaList, list)  {
            let len = abscissaList.length
            const transdata = new Array(len).fill(0);
            list.forEach((element, index) => {
                const absindex = abscissaList.findIndex(item => item == element.dateString);
                transdata[absindex] = element.count
            });
            return transdata
        }
    },
    mounted() {
        this.getServerData();
    }
};
</script>

<style></style>
