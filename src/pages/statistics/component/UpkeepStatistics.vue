<template>
    <view class="">
        <uni-section title="保养记录趋势">
            <qiun-data-charts type="line" :opts="pollRecordTrendOpts" :chartData="pollRecordTrendData" :ontouch="true" :inScrollView="true" />
        </uni-section>
        <uni-section title="保养模版设备数量分布">
            <qiun-data-charts type="column" :opts="pollTemplateDisOpts" :chartData="pollTemplateDisData" />
        </uni-section>
        <uni-section title="设备类型保养次数分布">
            <qiun-data-charts type="pie" :opts="deviceTypePollDataOpts" :chartData="deviceTypePollData" :ontouch="true" :inScrollView="true" />
        </uni-section>
        <!-- <uni-section title="保养时间分布">
            <qiun-data-charts type="bubble" :opts="pollTimeDisOpts" :chartData="pollTimeDisData" :ontouch="true" :inScrollView="true" />
        </uni-section> -->
    </view>
</template>

<script>
import { 
    getUpkeepRecordStatistics,
    getUpkeepTemplateDeviceRanking,
    getDeviceTypeUpkeepStatistics,
    // getUpkeepTimeStatistics
} from '@/api/statistics/index.js';
import { pollTemplateDisOpt,  pollRecordTrendOpt, pollTimeDisOpt, deviceTypePollDataOpt } from './options.js';

const statusEnum = {
    finishOrderNum: '已完成',
    todoOrderNum: '进行中',
    loseOrderNum: '已失效',
};
const dateTime = new Date();
const year = dateTime.getFullYear();
const month = dateTime.getMonth() + 1;
export default {
    data() {
        return {
            pollRecordTrendData: {},
            pollRecordTrendOpts: pollRecordTrendOpt,

            pollTemplateDisData: {},
            pollTemplateDisOpts: pollTemplateDisOpt,

            deviceTypePollData: {},
            deviceTypePollDataOpts: deviceTypePollDataOpt,

            // pollTimeDisData: {},
            // pollTimeDisOpts: pollTimeDisOpt,
        };
    },
    methods: {
        async getServerData() {
            this.getPollRecordData()
            this.getPollTemplateData()
            this.getDeviceTypePollData()
            // this.getPollTimedisData()    
        },
        async getPollRecordData() {
            const pollRecordRes = await getUpkeepRecordStatistics({ dateType: 2 });
            const newData = pollRecordRes?.data;
            let len = newData.abscissaList.length;
            const categories = newData.abscissaList.map((item) => month + '-' + item);
            const transdata = new Array(len).fill(0);
            newData.dataList.forEach((element, index) => {
                const absindex = newData.abscissaList.findIndex(item => item == element.dateString);
                transdata[absindex] = element.count
            });
            this.pollRecordTrendData = {
                categories,
                series: [
                    {
                        data: transdata
                    }
                ]
            };
        },
        async getPollTemplateData() {
            const res = await getUpkeepTemplateDeviceRanking({ dateType: 2 });
            const newData = res?.data;
            let categories = [];
            let dataList = [];
            newData.forEach(item => {
                categories.push(item.templateName);
                dataList.push(item.count);
            })
            this.pollTemplateDisData = {
                categories,
                series: [
                    {
                        data: dataList
                    }
                ]
            };
        },
        async getDeviceTypePollData() {
            const res = await getDeviceTypeUpkeepStatistics({ dateType: 2 });
            let dataList  = res?.data.filter(item => item.count !== 0)
            this.deviceTypePollData = {
                series: [
                    {
                        data: dataList.map(item => ({name: item.typeName, value: item.count}))
                    } 
                ]
            };
        },
        async getPollTimedisData() {
            const res = await getUpkeepTimeStatistics({ dateType: 2 });
            const newData = res?.data;
            const xData = newData.abscissaList
            const yData = newData.countList
            const newList = newData?.dataList.filter(item => item.count !== 0)
            const itemList = newList?.map(item => {
                item = [item.abscissa*1, item.count, item.bubbleSize, item.count]
                return item
            })
            
            this.pollTimeDisData = {
                xAxis: {
                    min: xData[0]*1,
                    max: xData[xData.length -1]*1,
                    gridEval: xData[1]*1 - xData[0]*1
                },
                yAxis: {
                    data: {
                        min: yData[0],
                        max: yData[1]
                    }
                },
                // categories: xData,
                series: [
                    {
                        data: itemList
                    }
                ]
            };
            
        }
    },
    mounted() {
        this.getServerData();
    }
};
</script>

<style></style>
