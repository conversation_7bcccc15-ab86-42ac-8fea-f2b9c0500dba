export const deviceTypeOpts = {
    enableScroll: true,
    legend: {
        show: false
    },
    xAxis: {
        disableGrid: true,
        rotateLabel: true,
        rotateAngle: -45,
        marginTop: 5,
        itemCount: 5
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2
    },
}

export const deviceStatusOpts = {
    rotate: false,
    rotateLock: false,
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    dataLabel: true,
    enableScroll: false,
    legend: {
        show: true,
        position: "right",
        lineHeight: 25
    },
    title: {
        name: "",
        fontSize: 15,
        color: "#666666"
    },
    subtitle: {
        name: "",
        fontSize: 25,
        color: "#7cb5ec"
    },
    extra: {
        ring: {
            ringWidth: 30,
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: "#FFFFFF"
        }
    }
}

export const deviceAlarmTrendOpt = {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    enableScroll: true,
    dataPointShape: false,
    padding: [15, 15, 0, 15],
    legend: {},
    xAxis: {
        disableGrid: true,
        boundaryGap: "justify",
        itemCount: 8
    },
    yAxis: {
        gridType: "dash",
        dashLength: 2,

    },
    extra: {
        line: {
            type: "curve",
            width: 2,
            activeType: "hollow"
        }
    }
}

export const alarmTypeOpts = {
    rotate: false,
    rotateLock: false,
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    dataLabel: true,
    enableScroll: false,
    legend: {
        show: true,
        position: "bottom",
        lineHeight: 25
    },
    title: {
        name: "",
        fontSize: 15,
        color: "#666666"
    },
    subtitle: {
        name: "",
        fontSize: 25,
        color: "#7cb5ec"
    },
    extra: {
        ring: {
            ringWidth: 30,
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: "#FFFFFF"
        }
    }
}

export const alarmTrendOpts = {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    enableScroll: true,
    dataPointShape: false,
    padding: [15, 15, 0, 15],
    legend: {
        show: false,
    },
    xAxis: {
        disableGrid: true,
        boundaryGap: "center",
        itemCount: 8
    },
    yAxis: {
        gridType: "dash",
        dashLength: 2,

    },
    extra: {
        line: {
            type: "curve",
            width: 2,
            activeType: "hollow"
        }
    }
}

export const alarmAreaOpts = {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    enableScroll: true,
    legend: {
        show: false
    },
    xAxis: {
        disableGrid: false,
        rotateLabel: true,
        rotateAngle: -45,
        marginTop: 5,
        itemCount: 8
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2
    },
}

export const orderCountTrendOpt = {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    enableScroll: true,
    dataPointShape: false,
    padding: [15, 15, 0, 15],
    legend: {
        fontSize: 10,
    },
    xAxis: {
        disableGrid: true,
        boundaryGap: "center",
        itemCount: 8
    },
    yAxis: {
        gridType: "dash",
        dashLength: 2,

    },
    extra: {
        line: {
            type: "curve",
            width: 2,
            activeType: "hollow"
        }
    }
}

export const orderTypesDisOpts = {
    rotate: false,
    rotateLock: false,
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    dataLabel: true,
    enableScroll: false,
    legend: {
        show: true,
        position: "right",
        lineHeight: 25
    },
    title: {
        name: "",
        fontSize: 15,
        color: "#666666"
    },
    subtitle: {
        name: "",
        fontSize: 25,
        color: "#7cb5ec"
    },
    extra: {
        ring: {
            ringWidth: 30,
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: "#FFFFFF"
        }
    }
}

export const orderStautusOpt = {
    enableScroll: true,
    legend: {
        show: false
    },
    xAxis: {
        disableGrid: true,
        rotateLabel: true,
        rotateAngle: -45,
        marginTop: 5,
        itemCount: 5
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2
    },
}

export const pollRecordTrendOpt = {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    enableScroll: true,
    dataPointShape: false,
    padding: [15, 15, 0, 15],
    legend: {
        show: false,
    },
    xAxis: {
        disableGrid: true,
        boundaryGap: "center",
        itemCount: 8
    },
    yAxis: {
        gridType: "dash",
        dashLength: 2,

    },
    extra: {
        line: {
            type: "curve",
            width: 2,
            activeType: "hollow"
        }
    }
}

export const pollTemplateDisOpt = {
    enableScroll: true,
    legend: {
        show: false
    },
    xAxis: {
        disableGrid: true,
        rotateLabel: true,
        rotateAngle: -45,
        marginTop: 5,
        itemCount: 5
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2
    },
}

export const deviceTypePollDataOpt = {
    color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
    legend: {
        fontSize: 10,
        lineHeight: 15
    },
    padding: [5,5,5,5],
    enableScroll: false,
    extra: {
      pie: {
        activeOpacity: 0.5,
        activeRadius: 10,
        offsetAngle: 0,
        border: false,
        borderWidth: 3,
        borderColor: "#FFFFFF"
      }
    }
}


// export const pollTimeDisOpt = {
//     color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
//     padding: [30,15,0,15],
//     height: '90%',
//     enableScroll: false,
//     legend: {
//         show: false
//     },
//     xAxis: {
//       disableGrid: false,
//       gridType: "dash",
//     //   splitNumber: 5,
//       boundaryGap: "justify",
//       gridEval: 3,
//       min: 0,
//       max: 21
//     },
//     yAxis: {
//       disableGrid: false,
//       gridType: "dash",
//       data: [
//         {
//             min: 0,
//             max: 8
//         }
//       ]
//     },
//     extra: {
//       bubble: {
//         border: 2,
//         opacity: 0.5
//       }
//     }
//   }

export const airHumidityOpt = {
    color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
    padding: [15,15,0,15],
    enableScroll: false,
    legend: {
        show: false
    },
    xAxis: {
      disableGrid: true
    },
    yAxis: {
      gridType: "dash",
      dashLength: 2,
      showTitle: true,
    //   titleText: '99',
      data: [
        {
            title: '%'
        }
      ]
    },
    extra: {
      area: {
        type: "curve",
        opacity: 0.2,
        addLine: true,
        width: 2,
        gradient: true,
        activeType: "hollow"
      }
    }
  }

  export const waterTestingOpt = {
    color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
    padding: [15,15,0,5],
    enableScroll: false,
    legend: {},
    xAxis: {
      disableGrid: true
    },
    yAxis: {
      data: [
        {
          min: 0
        }
      ]
    },
    extra: {
      column: {
        type: "stack",
        width: 30,
        activeBgColor: "#000000",
        activeBgOpacity: 0.08,
        labelPosition: "center"
      }
    }
  }

  const lakeWaterQualityDataOpt = {
    color: ["#73a0fa", "#42cece", "#73deb3", "#fbd643", "#f36d83", "#9d69e6"],
    legend: {
        fontSize: 10,
        lineHeight: 15
    },
    padding: [5,5,5,5],
    enableScroll: false,
    extra: {
      pie: {
        activeOpacity: 0.5,
        activeRadius: 10,
        offsetAngle: 0,
        border: false,
        borderWidth: 3,
        borderColor: "#FFFFFF"
      }
    }
}