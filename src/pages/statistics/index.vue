<template>
  <view class="work-container">
    <u-navbar
      :autoBack="false"
      :placeholder="true"
      leftIcon=" "
      :safeAreaInsetTop="true"
    >
      <view slot="center" style="display: flex" @click="selectShow = true">
        <span>统计分析-{{ selected.label }}</span>
        <u-icon name="arrow-down-fill"></u-icon>
      </view>
    </u-navbar>

    <u-picker
      title="请选择"
      :show="selectShow"
      :columns="dataList"
      keyName="label"
      @confirm="confirm"
      @cancel="selectShow = false"
    />

    <device-statistics v-if="selected.value == 1" />
    <alarm-statistics v-else-if="selected.value == 2" />
    <order-statistics v-else-if="selected.value == 3" />
    <poll-statistics v-else-if="selected.value == 4" />
    <upkeep-statistics v-else-if="selected.value == 5" />
    <environmenta-statistics v-else-if="selected.value == 6" />
  </view>
</template>

<script>
import DeviceStatistics from './component/DeviceStatistics.vue';
import AlarmStatistics from './component/AlarmStatistics.vue';
import OrderStatistics from './component/OrderStatistics.vue';
import PollStatistics from './component/PollStatistics.vue';
import UpkeepStatistics from './component/UpkeepStatistics.vue';
import EnvironmentaStatistics from './component/EnvironmentaStatistics.vue';
export default {
  components: {
    DeviceStatistics,
    AlarmStatistics,
    OrderStatistics,
    PollStatistics,
    UpkeepStatistics,
    EnvironmentaStatistics,
  },
  data() {
    return {
      selectShow: false,
      dataList: [
        [
          {
            label: '设备统计',
            value: '1',
          },
          {
            label: '告警统计',
            value: '2',
          },
          {
            label: '工单统计',
            value: '3',
          },
          {
            label: '巡检统计',
            value: '4',
          },
          {
            label: '保养统计',
            value: '5',
          },
          {
            label: '环境统计',
            value: '6',
          },
        ],
      ],
      selected: {
        label: '设备统计',
        value: '1',
      },
    };
  },

  methods: {
    confirm(arr) {
      this.selected = arr.value[0];
      this.selectShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100%;
  height: auto;
}

.popup-height {
  height: 500rpx;
}
</style>
