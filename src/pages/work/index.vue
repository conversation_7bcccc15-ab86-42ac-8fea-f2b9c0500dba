<template>
  <view class="work-container homePage">
    <top-navbar
      bgColor="#AA001E"
      :title="topTitle"
      :autoBack="autoBack"
      :leftText="leftText"
    ></top-navbar>
    <view class="workBody">
      <view
        v-for="(service, index) in curRoutes"
        :key="index"
        class="workMoudle"
      >
        <uni-section
          :title="service.meta.title"
          type="line"
          titleFontSize="16px"
          titleColor="#333"
          class="workSection"
        >
          <u-grid :col="4" :border="false">
            <u-grid-item
              v-for="(item, i) in service.children"
              :key="i"
              v-show="!item.hidden"
              @click="gridItemClick(item)"
            >
              <u--image
                class="gridImgIcon workImgIcon"
                :src="globalConfig.iconImgWork + item.query.customIconPath"
              ></u--image>
              <view class="grid-text">{{ item.meta.title }}</view>
            </u-grid-item>
          </u-grid>
        </uni-section>
      </view>
    </view>
  </view>
</template>

<script>
import TopNavbar from '@/components/top-navbar/top-navbar.vue';

import { pushRouter } from '@/utils/permission';
export default {
  components: {
    TopNavbar,
  },
  data() {
    return {
      globalConfig: getApp().globalData.config,
      leftText: '',
      autoBack: false,
      topTitle: '全部服务',

      actionShow: false,
      taskItem: {},
    };
  },
  computed: {
    curRoutes() {
      return this.$store.getters.addRoutes.filter((item) => {
        if (item.children?.length) {
          return (
            item.hidden == false &&
            item.children.some((every) => every.hidden == false)
          );
        } else if (item.hidden == false) {
          return true;
        }
      });
    },
  },
  methods: {
    gridItemClick(item) {
      pushRouter(item);
    },
  },
};
</script>

<style lang="scss">
.workMoudle {
  background-color: #fff;
  margin-bottom: 32rpx;
}
.workBody {
}
</style>
