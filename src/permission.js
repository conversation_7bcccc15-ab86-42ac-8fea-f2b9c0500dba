import { getToken } from '@/utils/auth';
import { router } from '@/router';
import store from '@/store';
import { isRelogin } from '@/utils/request';
// 登录页面
const loginPage = '/pages/login';

// 页面白名单
const whiteList = [
  '/pages/login',
  '/pages/register',
  '/pages/common/webview/index',
];

// 检查地址白名单
function checkWhite(url) {
  const path = url.split('?')[0];

  return whiteList.includes(path);
}

// 页面跳转验证拦截器
let list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']; // push,replace,replaceAll,pushTab
// list.forEach((item) => {
//   uni.addInterceptor(item, {
//     invoke(to) {
//       if (getToken()) {
//         if (to.url === loginPage) {
//           uni.reLaunch({ url: '/' });
//         } else {
//           if (store.getters.storeRoles.length == 0) {
//             // 判断当前用户是否已拉取完user_info信息
//             store
//               .dispatch('GetInfo')
//               .then(() => {
//                 store.dispatch('GenerateRoutes');
//               })
//               .catch((err) => {
//                 store.dispatch('LogOut').then(() => {
//                   uni.reLaunch({ url: '/pages/login' });
//                 });
//               });
//           }
//         }
//         return true;
//       } else {
//         if (checkWhite(to.url)) {
//           return true;
//         }
//         uni.reLaunch({ url: loginPage });
//         return false;
//       }
//     },
//     fail(err) {
//       console.log(err);
//     },
//   });
// });

//全局路由前置守卫
router.beforeEach(async (to, from, next) => {
  if (getToken()) {
    if (to.path == loginPage) {
      //uni.reLaunch({ url: '/' });
      next({ path: '/pages/index' });
    } else {
      if (store.getters.storeRoles.length == 0) {
        // 判断当前用户是否已拉取完user_info信息
        isRelogin.show = true;
        store
          .dispatch('GetInfo')
          .then(() => {
            isRelogin.show = false;
            store.dispatch('GenerateRoutes').then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表

              // router.addRoutes(accessRoutes); // 动态添加可访问路由表
              next({ path: to.path }); // hack方法 确保addRoutes已完成
            });
          })
          .catch((err) => {
            store.dispatch('LogOut').then(() => {
              next({ path: loginPage });
            });
          });
      } else {
        next();
      }
    }
  } else {
    if (checkWhite(to.path)) {
      next();
    } else {
      next({ path: loginPage });
    }
  }
if (window._hmt) {
      window._hmt.push(['_trackPageview', '/jsyp-frontend-app' + to.fullPath]);
    }
});
// 全局路由后置守卫
router.afterEach((to, from) => {
  console.log('跳转结束');
});
