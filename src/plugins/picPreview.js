import picPreview from '@/pages/components/picPreview.vue';
const preview = {
  install(Vue) {
    const View = Vue.extend(picPreview);

    let currentView;
    const init = () => {
      currentView = new View();
      let box = currentView.$mount().$el;
      document.body.appendChild(box);
    };
    Vue.prototype.$closePreview = function () {
      if (currentView) {
        currentView.closePicPreview();
        currentView = null;
      }
    };
    Vue.prototype.$openPreview = function (option) {
      if (!currentView) {
        init();
      }
      if (typeof option != 'string') {
        currentView = null;
        throw new Error('图片链接非字符串');
      }
      currentView.previewUrl = option;
      return currentView
        .open()
        .then((val) => {
          return Promise.resolve(val);
        })
        .catch((err) => {
          currentView = null;
          return Promise.reject(err);
        });
    };
  },
};
export default preview;
