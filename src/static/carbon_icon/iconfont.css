@font-face {
  font-family: "myiconfont"; /* Project id 4596929 */
  src: url('~@/static/carbon_icon/iconfont.woff2?t=1719214696484') format('woff2'),
       url('~@/static/carbon_icon/iconfont.woff?t=1719214696484') format('woff'),
       url('~@/static/carbon_icon/iconfont.ttf?t=1719214696484') format('truetype');
}

.myiconfont {
  font-family: "myiconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-item-fuel:before {
  content: "\e60e";
}

.icon-reli:before {
  content: "\e69a";
}

.icon-shuiyuan:before {
  content: "\e632";
}

.icon-qiti:before {
  content: "\e6da";
}

.icon-lightning-full:before {
  content: "\e995";
}

.icon-meitan:before {
  content: "\e66d";
}

