/*首页样式开始*/
//自定义滚动通知样式
.homePage {
	
	// min-height: 100vh;
	// background: #eaeded;
	padding-bottom: 20rpx;
	.topNotice {
		.u-image {
			width: 32rpx !important;
			height: 32rpx !important;
			.u-image__image {
				width: 32rpx !important;
				height: 32rpx !important;
			}
		}
	}
	.noticeCustom {
		padding: 12rpx 24rpx 24rpx 24rpx !important;
	}
	.customCard {
		margin: 0 24rpx 20rpx 24rpx !important;
		box-shadow: none !important;
		border: none !important;
		border-radius: 5px !important;
		&:last-child {
			margin-bottom: 0 !important;
		}
	}
	.homeGrid {
		justify-content: center !important;
		.homeTopIcon {
			width: 140rpx !important;
			height: 140rpx !important;
			margin: 24rpx 16rpx;
			color: #fff !important;
			border-radius: 20rpx;
			align-items: flex-start;
			justify-content: flex-start;
			padding: 26rpx 0 0 18rpx;
			font-size: $uni-font-size-base;
			::v-deep .grid-text {
				line-height: 14px;
			}
		 
		}
	 
	}
	.homeCardGrid {
		// font-size: 12px;
		flex-wrap: wrap;
		::v-deep .grid-text {
			line-height: $uni-font-size-base;
			color: #333;
			font-size: $uni-font-size-base;
		}
		
		.homeCardGridItem {
			margin-bottom: 36rpx;
		}
	}
 
}

/*首页样式结束*/

/*工作台样式开始*/
.work-container {
	background-color: #f2f3f3;
	.gridImgIcon.workImgIcon {
		.u-image {
			width: 48rpx !important;
			height: 48rpx !important;
			.u-image__image {
				width: 48rpx !important;
				height: 48rpx !important;
			}
		}
	}
	.workSection {
		.uni-section__content-title {
			font-weight: 500;
		}
	}
	.grid-text {
		font-weight: 400;
		font-size: $uni-font-size-base;
		line-height: $uni-font-size-base;
		color: #333;
		margin-bottom: 32rpx;
	}
	.uni-section {
		.uni-section-header {
			// padding-top: 62rpx !important;
			padding-bottom: 50rpx !important;
		}
	}
}

/*工作台样式结束*/

/*我的样式开始*/
.mine-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	.header-section {
		.top-box {
			margin-bottom: 52rpx;
			.title-box {
				display: flex;
				align-items: center;
				margin-top: 108rpx;
				margin-left: 60rpx;
				.avatarImg {
					width: 128rpx;
					height: 128rpx;
					border-radius: 50%;
					box-shadow: 0px 0px 20px 0px rgba(0, 0, 1, 0.3);
					background: #fff;
				}
				.login-tip,
				.user-info {
					color: #fff;
					margin-left: 46rpx;
				}
				.u_title {
					font-size: 20px;
					line-height: 20px;
				}
				.sub_title {
					margin-top: 9px;
					font-size: $uni-font-size-base;
					line-height: $uni-font-size-base;
					color: #fff;
				}
			}
		}
		.woker-list {
			display: flex;
			padding: 0 60rpx;
			margin-bottom: 14rpx;
			.woker-list-item {
				flex: 1;
				display: flex;
				justify-content: space-between;
				font-size: $uni-font-size-base;
				color: $uni-text-color-grey;
				line-height: $uni-font-size-base;
				&:last-child {
					justify-content: flex-end;
				}
				.woker-list__name {
					margin-right: 32rpx;
				}
				.decollator {
					font-size: 18px;
				}
			}
		}
	}

	.content-section {
		.menu-list {
			margin: 40rpx 0;
			.list-cell {
				display: flex;
				&:last-child {
					&::after {
						height: 0;
					}
				}
				.menu-item-box {
					.pwIcon {
						.u-image {
							height: 51rpx !important;
							.u-image__image {
								height: 51rpx !important;
							}
						}
					}
				}
			}
		}
	}
	.customBtn {
		margin: 92rpx 24rpx 0 24rpx;
		width: calc(100% - 48rpx) !important;
	}
}
/*我的样式结束*/


/*我的待办样式开始*/
.todoListPage {
	.bgCard {
		// background: #f7f7f7;
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 188rpx);
		/*#endif*/
		/*#ifdef H5*/
		height: calc(100vh - 88rpx);
		/*#endif*/
		// padding: 30rpx 25rpx 0;
		position: relative;
		padding-top: 123rpx;
		overflow: hidden;
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
		}
		.u-list {
			/*#ifdef H5*/
			height: calc(100vh - 241rpx) !important;
			/*#endif*/
			/*#ifdef APP-PLUS*/
			height: calc(100vh - 317rpx) !important;
			/*#endif*/
			.uni-scroll-view-content {
				.listCardItem {
					&:first-child {
						.listCard {
							margin-top: 0 !important;
						}
					}
				}
			}
		}
		.publicList {
			.u-empty__text {
				color: rgb(144, 147, 153) !important;
			}
		}
	}
}
/*我的用车记录样式开始*/
.usecarListPage {
	.bgCard {
		// background: #f7f7f7;
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 188rpx);
		/*#endif*/
		/*#ifdef H5*/
		height: calc(100vh - 88rpx);
		/*#endif*/
		// padding: 30rpx 25rpx 0;
		position: relative;
		padding-top: 123rpx;
		overflow: hidden;
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
		}
		.u-list {
			/*#ifdef H5*/
			height: calc(100vh - 40px) !important;
			/*#endif*/
			/*#ifdef APP-PLUS*/
			height: calc(100vh - 317rpx) !important;
			/*#endif*/
			::v-deep .u-list-item:last-child {
        margin-bottom: 20px;
      }
			.uni-scroll-view-content {
				.listCardItem {
					&:first-child {
						.listCard {
							margin-top: 0 !important;
						}
					}
				}
			}
		}
		.publicList {
			.u-empty__text {
				color: rgb(144, 147, 153) !important;
			}
		}
	}
}
 

/*设备报修样式开始*/
.problemReportPage {
	height: 100%;
	.bgCard {
		height: 100%;
		.probelmReportForm {
			padding: 28rpx 30rpx 0 50rpx !important;
			.u-form-item__body {
				padding: 10rpx 0;
			}
		}
		.probelmReportBtnBox {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 148rpx;
			padding: 0 22rpx;
			.customBtn {
				margin: 0 11rpx 0 11rpx;
				&:first-child {
					margin-left: 0;
				}
				&:last-child {
					margin-right: 0;
				}
			}
			.otherBtn {
				background: #4b517d !important;
				color: #fefefe;
				.u-button__text {
					color: #fefefe;
				}
			}
		}
	}
}
.listContent {
	.bgCard {
		height: calc(100vh - 88rpx);
		// height: auto;
		overflow: hidden;
		position: relative;
		padding: 20rpx 30rpx 100rpx 30rpx;
		.customSearch {
			::v-deep .u-search__content {
				background-color: #f2f3f3 !important;
				.u-search__content__input {
					background-color: #f2f3f3 !important;
				}
			}
		}
		.list_containerBox {
			height: calc(100vh - 276rpx) !important;
		}
		.btn_box {
			width: 100%;
			position: fixed;
			bottom: 0;
			left: 0;
			background: #fff;
			padding: 0 0.625rem 0.625rem;
			display: flex;
			.detail_btn {
				// margin-right: 0.5rem;
				border-radius: 20rpx;
			}
			.cancel_btn {
				// margin-left: 0.5rem;
				border-radius: 20rpx;
			}
		}
	}
}
/*设备报修样式结束*/
/*事件告警样式开始*/
.warnListPage {
	.bgCard {
	 
		overflow: hidden;
		position: relative;
		/*#ifdef H5*/
		height: calc(100vh - 70rpx);
		/*#endif*/
		/*#ifdef APP-PLUS*/
		height: calc(100vh);
		/*#endif*/
		//搜索框样式
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
			::v-deep .u-search__content {
				background-color: #f2f3f3 !important;
				.u-search__content__input {
					background-color: #f2f3f3 !important;
				}
			}
		}
		.sectionBtn {
			margin: 80rpx 0 0 0 !important;
		}
		.card-list {
			/*#ifdef H5*/
			max-height: calc(100vh - 200rpx);
			/*#endif*/
			/*#ifdef APP-PLUS*/
			max-height: calc(100vh - 380rpx);
			/*#endif*/
			.u-list-item {
				&:first-child {
					.simpleItem {
						.simpleItemCard {
							margin-top: 24rpx !important;
						}
					}
				}
			}
		}
	}
}
 
 
 

 
 

/*告警详情样式开始*/
.content_container {
	.bgCard {
		/*#ifdef H5*/
		height: calc(100vh - 108rpx);
		/*#endif*/
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 184rpx);
		/*#endif*/
		padding-bottom: 100rpx;
		.btn_box {
			width: 100%;
			position: fixed;
			bottom: 0;
			left: 0;
			// background: #fff;
			padding: 0 0.625rem 0.625rem;
			display: flex;
			.detail_btn {
				margin-right: 0.5rem;
				border-radius: 20rpx;
			}
			.cancel_btn {
				margin-left: 0.5rem;
				border-radius: 20rpx;
			}
		}
	}
	.u-button {
		border-radius: 20rpx !important;
	}
}
/*告警详情样式结束*/
 

/*扫码设备详情样式结束*/
/*顶部标题*/
.u-navbar__content__title {
	font-size: 16px !important;

	font-weight: bold;
}
.u-search__action {
	color: $uni-color-primary !important;
}
.u-toolbar__wrapper__confirm {
	color: $uni-color-primary !important;
	font-size: $uni-font-size-base !important;
}
.u-toolbar__wrapper__cancel {
	font-size: $uni-font-size-base !important;
}
.u-action-sheet__cancel-text {
	background-color: $uni-color-primary !important;
	color: #fff !important;
}
//全局默认按钮颜色
.u-button--large.u-button--square {
	border-radius: 12rpx !important;
}
.u-button--primary {
	background-color: $uni-color-primary !important;
	border-color: $uni-color-primary !important;
	color: #fff !important;
	.u-button__text {
		color: #fff;
	}
}
.u-button--success {
	background-color: $uni-color-primary !important;
	border-color: $uni-color-primary !important;
	color: #fff !important;
	.u-button__text {
		color: #fff;
	}
}
.u-button--warning {
	background-color: $uni-color-warning !important;
	border-color: $uni-color-warning !important;
	color: #fff !important;
	.u-button__text {
		color: #fff;
	}
}
uni-modal {
	.uni-modal__ft {
		font-size: 16px;
	}
	.uni-modal__btn_default {
		color: $uni-text-color-grey !important;
	}
	.uni-modal__btn_primary {
		color: $uni-color-primary !important;
	}
}
/*轻提示样式*/
.u-toast__content {
	background-color: rgba(0, 0, 0, 0.6) !important;
	border-radius: 8px !important;
	flex-direction: column !important;
	.u-icon__icon {
		color: #fff !important;
		width: 36px;
		height: 36px;
		justify-content: center;
		border: 1px dashed rgb(255, 255, 255) !important;
		margin-bottom: 8px;
		span {
			font-size: 36px;
		}
	}
	.u-toast__content__text {
		font-size: $uni-font-size-base !important;
		color: #fff !important;
	}
}
.u-notice {
	.u-notice__left-icon {
		.u-icon__icon {
			color: $uni-color-primary !important;
		}
	}
	.u-notice__content__text {
		uni-text {
			color: $uni-text-color-grey !important;
		}
	}
}
uni-form {
	.uni-forms-item__label {
		color: #666 !important;
	}
	.uni-forms-item__content {
		uni-text {
			color: #333;
		}
	}
}

 

/*填报页面样式开始*/
.fullPopup {
	.u-popup__content {
		padding-bottom: 100rpx;
	}
}
/*填报页面样式结束*/
/*我的工作样式开始*/
.toWorkListPage {
	.bgCard {
	 
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 188rpx);
		/*#endif*/
		/*#ifdef H5*/
		height: calc(100vh - 86rpx);
		/*#endif*/
	 
		position: relative;
		padding-top: 123rpx;
		overflow: hidden;
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
		}
		.u-list {
			/*#ifdef H5*/
			height: calc(100vh - 150rpx) !important;
			/*#endif*/
			/*#ifdef APP-PLUS*/
			height: calc(100vh - 367rpx) !important;
			/*#endif*/
			.uni-scroll-view-content {
				.listCardItem {
					&:first-child {
						.listCard {
							margin-top: 0 !important;
						}
					}
				}
			}
		}
		.publicList {
			.u-empty__text {
				color: rgb(144, 147, 153) !important;
			}
		}
	}
}
/*任务安排样式开始*/
.toTaskListPage {
	.bgCard {
	 
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 188rpx);
		/*#endif*/
		/*#ifdef H5*/
		height: calc(100vh);
		/*#endif*/
		 
		position: relative;
	 
		overflow: hidden;
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
		}
		.u-list {
			/*#ifdef H5*/
			height: calc(100vh - 200rpx) !important;
			/*#endif*/
			/*#ifdef APP-PLUS*/
			height: calc(100vh - 367rpx) !important;
			/*#endif*/
			.uni-scroll-view-content {
				.listCardItem {
					&:first-child {
						.listCard {
							margin-top: 0 !important;
						}
					}
				}
			}
		}
		.publicList {
			.u-empty__text {
				color: rgb(144, 147, 153) !important;
			}
		}
	}
}
/*men岗核验*/
.toGeteListPage {
	.bgCard {
		background: #f7f7f7;
		/*#ifdef APP-PLUS*/
		height: calc(100vh - 188rpx);
		/*#endif*/
		/*#ifdef H5*/
		height: calc(100vh - 86rpx);
		/*#endif*/
		padding: 30rpx 25rpx 0;
		position: relative;
		padding-top: 123rpx;
		overflow: hidden;
		.customSearch {
			position: absolute;
			top: 30rpx;
			width: calc(100% - 50rpx);
		}
		.u-list {
			/*#ifdef H5*/
			// height: calc(100vh - 800rpx) !important;
			/*#endif*/
			/*#ifdef APP-PLUS*/
			// height: calc(100vh - 367rpx) !important;
			height:100%;
			/*#endif*/
			.uni-scroll-view-content {
				.listCardItem {
					&:first-child {
						.listCard {
							margin-top: 0 !important;
						}
					}
				}
			}
		}
		.publicList {
			.u-empty__text {
				color: rgb(144, 147, 153) !important;
			}
		}
	}
}

/*ly树样式开始*/
.ly-checkbox__inner {
	border-color: rgb(170, 0, 30) !important;
}
.ly-checkbox__input.is-checked {
	.ly-checkbox__inner {
		background-color: rgb(170, 0, 30) !important;
		border-color: rgb(170, 0, 30) !important;
	}
}
.ly-checkbox__input.is-disabled.is-checked {
	.ly-checkbox__inner {
		background-color: #f2f6fc !important;
	}
}
.lyTreePopup {
	.u-popup__content {
		.btn-container {
			display: flex;
			justify-content: space-between;
			margin-top: 20rpx;
			.u-button {
				margin: 0;
			}
		}
	}
}
/*ly树样式结束*/
