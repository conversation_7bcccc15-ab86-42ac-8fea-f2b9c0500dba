import storage from '@/utils/storage';
import { getRouters } from '@/api/menu';

import constant from '@/utils/constant';

const permission = {
  state: {
    addRoutes: storage.get(constant.addRoutes) || [],
    routerMenu: storage.get(constant.routerMenu) || [],
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes;
      storage.set(constant.addRoutes, routes);
    },
    SET_ROUTERMENU: (state, routes) => {
      state.routerMenu = routes;
      storage.set(constant.routerMenu, routes);
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes({ commit }) {
      return new Promise((resolve) => {
        // 向后端请求路由数据
        getRouters({ type: 'app' }).then((res) => {
          const sdata = JSON.parse(JSON.stringify(res.data));
          const mdata = JSON.parse(JSON.stringify(res.data));

          const rewriteRoutes = filterAsyncRouter(sdata, false, true, '/page');
          const asyncRoutes = filterMenuRoutes(mdata);

          commit('SET_ROUTES', rewriteRoutes);
          commit('SET_ROUTERMENU', asyncRoutes);

          resolve(rewriteRoutes);
        });
      });
    },
  },
};

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(
  asyncRouterMap,
  lastRouter = false,
  type = false,
  parentPath
) {
  return asyncRouterMap.filter((route) => {
    // if (type && route.children) {
    //   route.children = filterChildren(route.children);
    // }
    route.query = route.query ? JSON.parse(route.query) : {};
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(
        route.children,
        route,
        type,
        route.path
      );
    } else {
      route.path = `/pages/${route.component}`;
      delete route['children'];
      delete route['redirect'];
    }
    return true;
  });
}

// 获取全部菜单路由集合
export function filterMenuRoutes(routes) {
  let res = [];
  routes.forEach((item) => {
    if (item.children != null && item.children?.length) {
      item.children.forEach((every) => {
        every.query = every.query ? JSON.parse(every.query) : {};
        every.path = `/pages/${every.component}`;
      });
      res = res.concat(item.children);
    } else {
      res.path(item);
    }
  });

  return res;
}

export default permission;
