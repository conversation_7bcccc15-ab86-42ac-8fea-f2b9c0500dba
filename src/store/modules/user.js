import config from '@/config';
import storage from '@/utils/storage';
import constant from '@/utils/constant';
import { login, logout, getInfo } from '@/api/login';
import {
  getToken,
  setToken,
  removeToken,
  getUserId,
  setUserId,
  removeUserId,
} from '@/utils/auth';

const baseUrl = config.baseUrl;

const user = {
  state: {
    token: getToken(),
    userId: getUserId(),
    name: storage.get(constant.name),
    avatar: storage.get(constant.avatar),
    roles: storage.get(constant.roles),
    storeRoles: [],
    permissions: storage.get(constant.permissions),
    deptName: storage.get(constant.deptName),
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NAME: (state, name) => {
      state.name = name;
      storage.set(constant.name, name);
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
      storage.set(constant.avatar, avatar);
    },
    SET_PHONENUMBER: (state, num) => {
      state.phoneNumber = num;
		},
    SET_ROLES: (state, roles) => {
      state.roles = roles;
      state.storeRoles = roles;
      storage.set(constant.roles, roles);
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
      storage.set(constant.permissions, permissions);
    },
    SET_USERID: (state, userId) => {
      state.userId = userId;
    },
    SET_DEPTNAME: (state, deptName) => {
      state.deptName = deptName;
      storage.set(constant.deptName, deptName);
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      // const username = userInfo.username.trim();
      // const password = userInfo.password;
      // const code = userInfo.code;
      // const uuid = userInfo.uuid;
      const phonenumber = userInfo.phonenumber.trim();
      const smsCode = userInfo.smsCode;
      return new Promise((resolve, reject) => {
        login(phonenumber, smsCode)
          .then((res) => {
            setToken(res.data.token);
            commit('SET_TOKEN', res.data.token);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.data.user;
            const avatar =
              user == null || user.avatar == '' || user.avatar == null
                ? require('@/static/images/profile.png')
                : user.avatar;
            const username =
              user == null || user.userName == '' || user.userName == null
                ? ''
                : user.userName;
            const userId =
              user == null || user.userId == '' || user.userId == null
                ? ''
                : user.userId;
            const deptName =
              user == null || user.deptName == '' || user.deptName == null
                ? ''
                : user.deptName;
            const phoneNumber = (user == null || user.userId == "" || user.userId == null) ? "" :
              user.phonenumber
            if (res.data.roles && res.data.roles.length > 0) {
              commit('SET_ROLES', res.data.roles);
              commit('SET_PERMISSIONS', res.data.permissions);
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT']);
            }
            commit('SET_NAME', username);
            commit('SET_AVATAR', avatar);
            commit('SET_DEPTNAME', deptName);
            commit('SET_PHONENUMBER', phoneNumber)
            setUserId(userId);

            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            commit('SET_PERMISSIONS', []);
            removeToken();
            removeUserId();
            storage.clean();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
};

export default user;
