/**
 * uni-app内置的常用样式变量
 */

/* 行为相关颜色 */
$uni-color-primary: #aa001e;
$uni-color-success: #40dbad;
$uni-color-warning: #ffcd5d;
$uni-color-error: #ff665a;

/* 文字基本颜色 */
$uni-text-color: #333333; //基本色
$uni-text-color-inverse: #ffffff; //反色
$uni-text-color-grey: #999999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #d2d2d2;
$uni-text-color-disable: #bbbbbb;

/* 背景颜色 */
$uni-bg-color: #f7f7f7;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #e5e5e5;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 4px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 26px;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph: 15px;

/* uni.scss */
@forward 'uview-ui/theme.scss';