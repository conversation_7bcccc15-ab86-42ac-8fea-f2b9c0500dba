@import '~@/uni_modules/lime-style/index.scss';


$floating-panel-border-radius: create-var(floating-panel-border-radius, 32rpx);
$floating-panel-header-height: create-var(floating-panel-header-height, 60rpx);
$floating-panel-z-index: create-var(floating-panel-z-index, 999);
$floating-panel-background: create-var(floating-panel-background, white);
$floating-panel-bar-width: create-var(floating-panel-bar-width, 60rpx);
$floating-panel-bar-height: create-var(floating-panel-bar-height, 6rpx);
$floating-panel-bar-color: create-var(floating-panel-bar-color, #ddd);
$floating-panel-bar-radius: create-var(floating-panel-bar-radius, 6rpx);



.l-floating-panel {
	position: fixed;
	left: 0;
	bottom: -150rpx;
	width: 100%;
	z-index: $floating-panel-z-index;
	display: flex;
	flex-direction: column;
	// touch-action: none;
	border-top-left-radius: $floating-panel-border-radius;
	border-top-right-radius: $floating-panel-border-radius;
	background: $floating-panel-background;
	transition-property: transform;
	// transition-duration: 10ms;
	transition-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
	// pointer-events: auto;
	padding-bottom: 150rpx;
	box-sizing: content-box;
	&__header {
		height: $floating-panel-header-height;
		display: flex;
		justify-content: center;
		align-items: center;
		// #ifndef APP
		cursor: grab;
		user-select: none;
		// #endif
		&-bar {
			height: $floating-panel-bar-height;
			width: $floating-panel-bar-width;
			border-radius: $floating-panel-bar-radius;
			background: $floating-panel-bar-color;
		}
	}
	
	&__content {
		flex: 1;
		background-color: $floating-panel-background;
		// user-select: none;
	}
	
	// &::after {
	//     content: '';
	//     display: block;
	//     position: absolute;
	//     bottom: -100vh;
	//     height: 100vh;
	//     width: 750rpx;
	//     background-color: inherit;
	// }
}