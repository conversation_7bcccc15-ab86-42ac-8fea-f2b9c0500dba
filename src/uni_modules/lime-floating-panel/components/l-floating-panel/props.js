export default {
    height: {
        type: Number,
        default: 0
    },
    anchors: {
        type: Array,
        default: () => []
    },
    defaultAnchor: {
        type: Number,
        default: 0
    },
    animation: {
        type: Boolean,
        default: true
    },
    contentDraggable: {
        type: Boolean,
        default: true
    },
    safeAreaInsetBottom: {
        type: Boolean,
        default: true
    },
    baColor: {
        type: String,
        default: '#fff'
    },
    barColor: {
        type: String,
        default: '#eee'
    }
}; 