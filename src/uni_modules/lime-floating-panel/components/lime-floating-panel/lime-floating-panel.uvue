<template>
	<view class="demo-block">
		<text class="demo-block__title-text ultra">浮动面板</text>
		<text class="demo-block__desc-text">浮动在页面底部的面板，可以上下拖动来浏览内容，常用于提供额外的功能或信息。</text>	
		<view class="demo-block__body">
			<view class="demo-block">
				<view class="demo-block__body">
					<view class="tabs">
						<text class="tab" :class="{active: active == 0}" @click="tabChange(0)">基础用法</text>
						<text class="tab" :class="{active: active == 1}" @click="tabChange(1)">自定义锚点</text>
						<text class="tab" :class="{active: active == 2}" @click="tabChange(2)">仅头部拖拽</text>
					</view>
					<button type="primary" v-if="active == 1" style="margin-top: 20rpx;" @click="to">自定义锚点: 跳到3</button>
					<l-floating-panel v-if="active == 0">
						<scroll-view scroll-y direction="vertical" style="flex: 1" @scrolltolower="scrolltolower">
							<view v-for="(_, index) in list" class="cell" :key="index">内容:{{index}}</view>
							<view class="loading" v-if="loading">
								<text style="color: #999;">loading</text>
							</view>
						</scroll-view>
					</l-floating-panel>
					
					<l-floating-panel v-if="active == 1" v-model:height="height" :anchors="anchors" :defaultAnchor="1" ref="floatingPanelRef">
						<view style="align-items: center; padding: 15px">
							<text>面板显示高度 {{ height.toFixed(0) }} px</text>
						</view>
					</l-floating-panel>
					
					
					<l-floating-panel v-if="active == 2" :content-draggable="false" @height-change="heightChange">
						<view style="align-items: center; padding: 15px">
							<text>面板显示高度 {{ height.toFixed(0) }} px</text>
						</view>
					</l-floating-panel>
				</view>	
			</view>	
		</view>	
	</view>
</template>
<script setup>
	const floatingPanelRef = ref<LFloatingPanelComponentPublicInstance|null>(null)
	const active = ref(0)
	const loading = ref(false)
	const height = ref(0)
	const anchors = ref([50,400,700])
	const list = ref<number[]>([])

	const to = () => {
		if(floatingPanelRef.value == null) return
		floatingPanelRef.value!.toAnchor(2)
	}
	const heightChange = (res: UTSJSONObject) => {
		console.log('heightChange', res)
		height.value = (res['height'] ?? 0) as number
	}
	const tabChange = (index: number) => {
		active.value = index
	}
	
	const getData = () => {
		loading.value = true
		setTimeout(() => {
			for (var i = 0; i < 20; i++) {
				 list.value.push(i)
			}
			loading.value = false
		},500)
	}
	const scrolltolower = () => {
		getData()
	}
	scrolltolower()
</script>
<style lang="scss">
	.tabs {
		display: flex;
		flex-direction: row;
		padding: 15px 60rpx;
		justify-content: space-between;
		background-color: white;
		// background-color: #ffb400;
	}
	.tab{
		margin-right:10px;
		color: rgba(0, 0, 0, 0.6);
		
	}
	.text {
		margin: 30px 0;
		color: rgba(0, 0, 0, 0.6);
	}
	.active {
		font-weight: bold;
		color: rgba(0, 0, 0, 1);
	}
	
	.cell {
		padding: 30rpx;
	}
	.loading {
		padding: 30rpx;
		align-items: center;
	}
	.panel {
		box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.03);
		// background-color: aqua;
	}
	.demo-block {
		margin: 32px 20px 0;
		overflow: visible;
		&__title {
			margin: 0;
			margin-top: 8px;
			&-text {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 14px;
				line-height: 16px;
				
				&.large {
					color: rgba(0, 0, 0, 0.9);
					font-size: 18px;
					font-weight: 700;
					line-height: 26px;
				}
				&.ultra {
					color: rgba(0, 0, 0, 0.9);
					font-size: 24px;
					font-weight: 700;
					line-height: 32px;
				}
			}
		}
		&__desc-text {
			color: rgba(0, 0, 0, 0.6);
			margin: 8px 16px 0 0;
			font-size: 14px;
			line-height: 22px;
		}
		&__body {
			margin: 16px 0;
			overflow: visible;
			.demo-block {
				// margin-top: 0px;
				margin: 0;
			}
		}
	}
</style>
