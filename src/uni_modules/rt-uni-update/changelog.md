## 1.5.2（2023-09-08）
优化文档
## 1.5.1（2023-05-26）
优化文档
## 1.5.0（2023-05-23）
优化文档
## 1.4.9（2023-05-23）
文档新增后台版本管理示例图
## 1.4.8（2023-05-23）
优化当前版本显示
## 1.4.7（2023-05-23）
新增当前运行版本名称和新版本名称显示
## 1.4.6（2023-05-22）
新增显示安装包大小
## 1.4.5（2023-04-27）
优化页面不透明
## 1.4.4（2023-04-25）
新增pages_init.json自动注册页面
## 1.4.3（2023-04-25）
修改app下载链接
## 1.4.2（2023-04-25）
优化
## 1.4.1（2023-04-15）
优化bug
## 1.4.0（2023-04-14）
删除无用代码
## 1.3.9（2023-04-14）
优化
## 1.3.8（2023-04-03）
优化文档
## 1.3.7（2023-03-23）
优化文档
## 1.3.6（2023-03-23）
优化文档
## 1.3.5（2023-03-08）
新增常见问题
## 1.3.4（2023-03-07）
解决应用切换到后台再次打开更新弹窗叠加多个的问题
## 1.3.3（2023-03-02）
优化提示文档
## 1.3.2（2023-02-02）
优化部分wgt包无法安装的提示
## 1.3.1（2023-01-12）
修改示例下载文件地址
## 1.3.0（2022-11-17）
兼容低版本安卓手机，用户拒绝安装后，去掉自动重启，优化体验
## 1.2.9（2022-11-14）
优化插件
## 1.2.8（2022-11-14）
优化整包更新用户体验
## 1.2.7（2022-11-14）
修复apk整包更新时，点击拒绝安装，更新进度还在的bug
## 1.2.6（2022-10-17）
优化问题汇总
## 1.2.5（2022-10-17）
常见问题优化
## 1.2.4（2022-09-21）
文档新增常见问题汇总，方便更快的解决问题
## 1.2.3（2022-09-21）
文档新增常见问题汇总，方便更快的解决问题
## 1.2.2（2022-09-21）
文档新增常见问题汇总，方便更快的解决问题
## 1.2.1（2022-09-21）
文档新增常见问题汇总，方便更快的解决问题
## 1.2.0（2022-08-03）
优化插件，wgt升级重启，整包升级不重启
## 1.1.9（2022-08-01）
新增弹出一个合并页面路由的pages.json修改界面。插件使用者点击确认按钮即可完成插件页面向项目pages.json的注册。HBuilderX 3.5.0+支持
## 1.1.8（2022-07-25）
1、静默更新后提示用户重启应用，以解决样式错乱的问题
2、跳转应用市场下载后，解决更新提示弹窗一直叠加的问题
## 1.1.7（2022-07-22）
优化示例代码
## 1.1.6（2022-07-22）
优化文档
## 1.1.5（2022-07-19）
优化文档
## 1.1.4（2022-07-19）
优化文档
## 1.1.3（2022-07-19）
优化文档
## 1.1.2（2022-07-18）
优化wgt更新文档
## 1.1.1（2022-07-17）
新增wgt包静默更新
## 1.1.0（2022-05-17）
优化readme文档
## 1.0.9（2022-05-14）
优化
## 1.0.8（2022-05-05）
修复图片不显示的bug
## 1.0.7（2022-01-19）
1.0.7 优化readme文档
## 1.0.6（2022-01-19）
正式支持uni_modules
## 1.0.5（2022-01-19）
测试支持uni_models
