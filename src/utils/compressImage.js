import Compressor from 'compressorjs';
const STATUS = {
  not_reach_compress_threshold: 0, //文件未达到压缩值
  compress_success: 1, //压缩成功
  not_image_file: 2, //不是图片的文件
  not_support_compress: 3, //当前环境不支持压缩
  compress_error: 4, //压缩图片出错
  compress_lower_error: 5, //压缩图片出错
  compress_biger_error: 6, //压缩图片出错
};
const STATUSMSG = {
  not_reach_compress_threshold: '文件未达到压缩值', //文件未达到压缩值
  compress_success: '压缩成功', //压缩成功
  not_image_file: '照片格式有误,只可选择jpg图片', //不是图片的文件
  not_support_compress: '当前环境不支持压缩', //当前环境不支持压缩
  compress_error: '压缩照片出错', //压缩图片出错
  compress_lower_error: '照片过小，请重新选择照片',
  compress_biger_error: '照片过大，请重新选择照片', //压缩图片出错
};
/**
 * 
压缩图片后结果
@file 压缩后图片
@isCompressed 是否被压缩
@msg 压缩提示信息
@staus 压缩结果状态值
*/
const compressFileResult = {};
/**
 * 
压缩图片后结果
@params file:文件，threshold：开启压缩阈值，quailty:压缩比列，type:文件类型， 
 
*/
const blobToFile = function (blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = function () {
      const dataUrl = reader.result;
      const mime = dataUrl.split(',')[0].split(':')[1].split(';')[0];

      let binary = atob(dataUrl.split(',')[1]);
      let array = [];
      for (let i = 0; i < binary.length; i++) {
        array.push(binary.charCodeAt(i));
      }
      const file = new File([new Uint8Array(array)], blob.name, {
        type: mime,
      });
      resolve(file);
    };

    reader.onerror = function () {
      reject('Failed to convert blob to file.');
    };

    reader.readAsDataURL(blob);
  });
};

function detectDevice() {
  var userAgent = navigator.userAgent || navigator.vendor || window.opera;

  // 安卓设备的用户代理通常包含 'Android'
  if (/android/i.test(userAgent)) {
    console.log('Android device');
    return 'Android';
  }

  // iOS设备的用户代理通常包含 'iPhone' 或 'iPad'
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    console.log('iOS device');
    return 'iOS';
  }

  // 其他情况，例如Windows Phone或其他设备
  console.log('Other device');
  return 'Other';
}

export const compressImageFile = (props) => {
  const {
    file,
    quality,
    type,
    path,
    width,
    height,
    size,
    threshold,
    name,
    compressWidth,
    compressHeight,
    maxSetWidth,
  } = props;
  const typeMap = ['jpeg', 'jpg', 'image/jpeg', 'image/jpg'];
  const cursize = (size / 1024).toFixed(2);

  if (!typeMap.includes(type)) {
    return Promise.resolve({
      isCompressed: false,
      status: STATUS['not_image_file'],
      msg: STATUSMSG['not_image_file'],
    });
  }
  if (cursize <= 10) {
    return Promise.resolve({
      src: path,
      isCompressed: false,
      status: STATUS['compress_lower_error'],
      msg: STATUSMSG['compress_lower_error'],
    });
  }
  if (cursize <= threshold) {
    return Promise.resolve({
      src: path,
      isCompressed: false,
      status: STATUS['not_reach_compress_threshold'],
      msg: STATUSMSG['not_reach_compress_threshold'],
    });
  }
  // 定义压缩过程出错的返回数据
  const compressErrorContent = {
    src: path,
    isCompressed: false,
    status: STATUS['compress_error'],
    msg: STATUSMSG['compress_error'],
  };
  const device = detectDevice();

  return device == 'iOS'
    ? new Promise((resolve) => {
        //----------------------------------------------使用compressorjs压缩---------------------------------------
        // #ifdef H5
        new Compressor(file, {
          quality,
          width: compressWidth || 560,
          height: compressHeight || 420,
          convertSize‌: true,
          success: async (result) => {
            const newFile = new File([result], name || 'avater', {
              type: type,
            });

            if (newFile.size / 1024 < 10) {
              // 如果压缩后文件过小，取原文件调整裁剪文件大小，继续压缩，直到文件大小高于最小值
              const setWidth = compressWidth ? compressWidth : 560;
              const setHeight = compressHeight ? compressHeight : 420;
              if (setWidth >= 1280) {
                return resolve({
                  isCompressed: false,
                  status: STATUS['compress_lower_error'],
                  msg: STATUSMSG['compress_lower_error'],
                });
              }
              compressImageFile({
                ...props,
                size: file.size,
                file: file,
                maxSetWidth: setWidth + 80 > 1280 ? 1280 : setWidth + 80,
                compressHeight: setHeight + 60 > 960 ? 960 : setHeight + 60,
                quality: quality,
              }).then((res) => {
                resolve(res);
              });
              return;
            }
            if (newFile.size / 1024 >= threshold) {
              // 继续压缩，直到文件大小低于阈值，图片质量会逐渐减低，压缩时间会比较长
              if (quality >= 0.1) {
                compressImageFile({
                  ...props,
                  size: newFile.size,
                  file: newFile,
                  quality: quality - 0.02, // 图片质量递减 0.04
                }).then((res) => {
                  resolve(res);
                });
                return;
              } else {
                return resolve({
                  isCompressed: false,
                  status: STATUS['compress_biger_error'],
                  msg: STATUSMSG['compress_biger_error'],
                });
              }
            } else {
              let imgSrc = window.URL.createObjectURL(result);
              return resolve({
                src: imgSrc,
                isCompressed: true,
                staus: STATUS['compress_success'],
                msg: STATUSMSG['compress_success'],
              });
            }
          },
          error: () => {
            // 压缩过程出错，直接返回原 file 对象和提示信息
            return resolve(compressErrorContent);
          },
        });

        // #endif
      })
    : new Promise((resolve) => {
        // #ifdef MP-WEIXIN
        // uni.compressImage({
        // 	src: path, // 图片路径
        // 	quality, // 压缩质量
        // 	success: function(res) {
        // 		uni.getFileSystemManager().getFileInfo({
        // 			filePath: res.tempFilePath,
        // 			success: r => {

        // 				if (r.size / 1024 > threshold) {
        // 					//继续压缩
        // 					compressImageFile({
        // 						...props,
        // 						path: res.tempFilePath,
        // 						quality: quality - 0.5,
        // 						size: r.size
        // 					})
        // 				} else {

        // 					//压缩完成
        // 					resolve({
        // 						src: res.tempFilePath,
        // 						isCompressed: true,
        // 						status: STATUS['compress_success'],
        // 						msg: STATUSMSG['compress_success']
        // 					})
        // 				}
        // 			},
        // 			fail: err => {
        // 				resolve(compressErrorContent)
        // 			}
        // 		})
        // 	},
        // 	fail: function(err) {

        // 		resolve(compressErrorContent)
        // 	}
        // })

        // #endif
        // #ifdef H5
        let img = new Image();
        img.onload = () => {
          let canvas = document.createElement('canvas');
          let ctx = canvas.getContext('2d');
          const maxWidth = maxSetWidth || 1040; // 限制最大宽度
          const scale = Math.min(maxWidth / img.width, 1);
          canvas.width = img.width * scale;
          canvas.height = img.height * scale;
          // ctx.scale(dpr, dpr);
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          canvas.toBlob(
            (blob) => {
              if (blob) {
                const newFile = new File([blob], name || 'avater', {
                  type: type,
                });
                // 如果开启压缩大小在阈值之下，当前文件大小大于等于阈值，继续压缩质量高于0.2，允许继续压缩
                let imgSrc = window.URL.createObjectURL(blob);
                if (newFile.size / 1024 < 10) {
                  // 如果压缩后文件过小，取原文件调整裁剪文件大小，继续压缩，直到文件大小高于最小值
                  const setWidth = maxSetWidth ? maxSetWidth : 1040;

                  if (setWidth >= 1280) {
                    return resolve({
                      isCompressed: false,
                      status: STATUS['compress_lower_error'],
                      msg: STATUSMSG['compress_lower_error'],
                    });
                  }
                  compressImageFile({
                    ...props,
                    size: file.size,

                    maxSetWidth: setWidth + 80 > 1280 ? 1280 : setWidth + 80,

                    quality: quality,
                  }).then((res) => {
                    resolve(res);
                  });
                  return;
                }
                if (newFile.size / 1024 >= threshold) {
                  // 继续压缩，直到文件大小低于阈值，图片质量会逐渐减低，压缩时间会比较长
                  if (quality >= 0.1) {
                    compressImageFile({
                      ...props,
                      size: newFile.size,
                      path: imgSrc,
                      maxSetWidth: null,
                      quality: quality - 0.04, // 图片质量递减 0.04
                    }).then((res) => {
                      resolve(res);
                    });
                    return;
                  } else {
                    return resolve({
                      isCompressed: false,
                      status: STATUS['compress_biger_error'],
                      msg: STATUSMSG['compress_biger_error'],
                    });
                  }
                } else {
                  return resolve({
                    src: imgSrc,
                    isCompressed: true,
                    staus: STATUS['compress_success'],
                    msg: STATUSMSG['compress_success'],
                  });
                }
              } else {
                // 压缩过程出错，直接返回原 file 对象和提示信息
                return resolve(compressErrorContent);
              }
            },
            type,
            quality
          );
        };
        img.src = path;
        // #endif
      });
};
