export const planExecuteCycle = {
  day: '日',
  week: "周",
  month: "月",
  quarter: "季",
  year: "年",
};

// 计划完成时限
const weeks = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
const months = ["5号", "10号", "15号", "20号", "25号", "最后一天"];
const quarters = ["第一个月", "第二个月", "第三个月"];
const getArrays = function (num, after) {
  const arr = [];
  new Array(num).fill().forEach((item, index) => {
    arr.push({ value: index + 1, label: index + 1 + after });
  });
  return arr;
};

export const planTimeLimit = {
  day: getArrays(24, "点"),
  week: weeks.map((item, index) => {
    return { value: index + 1, label: item };
  }),
  month: months.map((item, index) => {
    return { value: index + 1, label: item };
  }),
  quarter: quarters.map((item, index) => {
    return { value: index + 1, label: item };
  }),
  year: getArrays(12, "月")
};