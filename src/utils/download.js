import config from '@/config';
import { getToken } from '@/utils/auth';
import { toast, showConfirm, tansParams } from '@/utils/common';
let timeout = 10000;
const baseUrl = config.baseUrl;
export const fileDownload = (url) => {
  let index = url.lastIndexOf('.');
  let fileType = url.substring(index + 1, url.length);
  let white_filetype = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'];

  uni.downloadFile({
    url: url,
    header: {
      Authorization: 'Bearer ' + getToken(),
    },
    success: function (res) {
      console.log('res--->', res.tempFilePath);
      var filePath = res.tempFilePath;
      if (white_filetype.indexOf(fileType) != -1) {
        uni.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功');
          },
          fail: function (err) {
            console.log('打开文档失败--->', err);
            uni.showToast({
              title: err.errMsg,
              icon: 'none',
            });
          },
        });
      } else {
        uni.showToast({
          title: '不支持打开该类型文件',
          icon: 'none',
        });
      }
    },
    fail: function (err) {
      console.log('err--->', err);
      uni.showToast({
        title: err.errMsg,
        icon: 'none',
      });
    },
  });
};
export function fileOpenDownload(url) {
  uni.downloadFile({
    url: baseUrl + url,
    header: {
      Authorization: 'Bearer ' + getToken(),
    },
    success: function (res) {
      console.log('res--->', res);
      var filePath = res.tempFilePath;
      uni.openDocument({
        filePath: filePath,
        showMenu: true,
        success: function (res) {
          console.log('打开文档成功');
        },
        fail: function (err) {
          console.log('打开文档失败--->', err);
          uni.showToast({
            title: err.errMsg,
            icon: 'none',
          });
        },
      });
      //saveFile(filePath, 'xlsx');
    },
    fail: function (err) {
      console.log('err--->', err);
      uni.showToast({
        title: err.errMsg,
        icon: 'none',
      });
    },
  });
}
//保存文件
export function saveFile(tempFilePath, fileType) {
  uni.saveFile({
    //使用该功能，以将临时文件保存在手机中可长久存储的区域
    tempFilePath: tempFilePath,
    success: (saveResult) => {
      // 在保存文件成功后，使用 uni.openDocument 方法打开文件
      console.log('文件保存成功:', saveResult);
      openDocument(saveResult.savedFilePath, fileType);
    },
    fail: function (err) {
      console.log('保存文件失败', err);
    },
  });
}

//保存图片
export function savePicture(tempFilePath) {
  uni.saveImageToPhotosAlbum({
    //保存图片到系统相册。
    filePath: tempFilePath, //图片文件路径
    success: function () {
      uni.showToast({
        title: '图片保存成功',
        icon: 'none',
      });
    },
    fail: function (e) {
      console.log(e);
      uni.showToast({
        title: '图片保存失败',
        icon: 'none',
      });
    },
  });
}

//预览文件
export function openDocument(filtPath, fileType) {
  uni.openDocument({
    filePath: filtPath,
    fileType: fileType, //这里把预先限定好的文件类型传来
    success: function () {
      console.log('文件打开成功!');
      //至此，手机会从app中跳转到文件预览界面
      //之后可从预览界面返回app中
    },
    fail: function (err) {
      console.log('打开失败', err);
    },
  });
}

//预览图片
export function picturePreview(picList) {
  uni.previewImage({
    urls: picList,
    longPressActions: {
      itemList: ['保存图片'],
      success: function (data) {
        console.log(
          '选中了第' +
            (data.tapIndex + 1) +
            '个按钮,第' +
            (data.index + 1) +
            '张图片'
        );
      },
      fail: function (err) {
        console.log(err.errMsg);
      },
    },
  });
}

//预览+长按保存
export const imgPreview = (urls) => {
  uni.previewImage({
    urls: urls,
    longPressActions: {
      itemList: ['保存图片'],
      success: function (data) {
        var url = urls[data.index];
        // console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
        // 先下载图片
        uni.downloadFile({
          url,
          success: (res) => {
            // 获取到图片本地地址后再保存图片到相册（因为此方法不支持远程地址）
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({
                  title: '保存成功！',
                  icon: 'none',
                });
              },
              fail: () => {
                uni.showToast({
                  title: '保存失败',
                  icon: 'none',
                });
              },
            });
          },
        });
      },
    },
  });
};
// export const downloadFile = (config) => {
//   // get请求映射params参数
//   if (config.params) {
//     let url = config.url + '?' + tansParams(config.params);
//     url = url.slice(0, -1);
//     config.url = url;
//   }
//   return new Promise((resolve, reject) => {
//     uni
//       .request({
//         method: 'get',
//         timeout: timeout,
//         url: baseUrl + config.url,

//         header: {
//           Authorization: 'Bearer ' + getToken(),
//         },
//         responseType: 'blob',
//       })
//       .then((response) => {
//         let [error, res] = response;
//         if (error) {
//           toast('后端接口连接异常');
//           reject('后端接口连接异常');
//           return;
//         }
//         const code = res.data.code || 200;
//         const msg = res.data.msg;
//         if (code === 401) {
//           showConfirm(
//             '登录状态已过期，您可以继续留在该页面，或者重新登录?'
//           ).then((res) => {
//             if (res.confirm) {
//               store
//                 .dispatch('LogOut')
//                 .then((res) => {
//                   uni.reLaunch({ url: '/pages/login' });
//                 })
//                 .catch((err) => {
//                   uni.reLaunch({ url: '/pages/login' });
//                 });
//             }
//           });
//           reject('无效的会话，或者会话已过期，请重新登录。');
//         } else if (code === 500) {
//           toast(msg);
//           reject('500');
//         } else if (code !== 200) {
//           toast(msg);
//           reject(code);
//         }

//         const blob = new Blob([res.data], {
//           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//         });
//         var a = document.createElement('a');

//         a.style.display = 'none';
//         var url = URL.createObjectURL(blob);
//         a.href = url;
//         a.setAttribute(
//           'download',
//           decodeURIComponent(res.header['download-filename'])
//         );
//         document.body.appendChild(a);
//         a.click();

//         document.body.removeChild(a);
//         URL.revokeObjectURL(url);
//         //resolve(res.data);
//       })
//       .catch((error) => {
//         let { message } = error;
//         if (message === 'Network Error') {
//           message = '后端接口连接异常';
//         } else if (message.includes('timeout')) {
//           message = '系统接口请求超时';
//         } else if (message.includes('Request failed with status code')) {
//           message = '系统接口' + message.substr(message.length - 3) + '异常';
//         }
//         toast(message);
//         reject(error);
//       });
//   });
// };
