// 引入各种表单组件
import TextField from '@/components/formFields/textField.vue';
import RadioField from '@/components/formFields/radioField.vue';
import UploadField from '@/components/formFields/uploadField.vue';
import ConfirmField from '@/components/formFields/confirmField.vue';
import CheckboxField from '@/components/formFields/checkboxField.vue';
import DatetimepickerField from '@/components/formFields/datetimepickerField.vue';
import DatepickerField from '@/components/formFields/datepickerField.vue';

export default {
  getFieldComponent(type) {
    const fieldsMap = {
      'input': TextField,
      'radio': RadioField,
      'upload': UploadField,
      'confirmationbox': ConfirmField,
      'checkbox': CheckboxField,
      'datetimepicker': DatetimepickerField,
      'datepicker': DatepickerField,
      // 如果有更多类型，可以在此继续添加
    };
    return fieldsMap[type] || TextField; // 如果没有匹配的类型，返回 null 或者一个默认的组件
  }
};

const rulesMap = {
  'input': {
    type: 'string',
    required: true,
    message: '请填写',
    trigger: ['blur', 'change']
  },
  'radio': {
    required: true,
    message: '请选择',
    trigger: ['blur', 'change']
  },
  'upload': {
    validator: (rule, value, callback) => {
      if (rule.required) {
        // uploadField组件将绑定的urlList使用了JSON.stringfy
        if (value) {
          return JSON.parse(value).length > 0
        } else {
          return false
        }
      } else {
          return
      }
    },
    message: '请上传照片'
  },
  'confirmationbox': {
    required: true,
    message: '请选择',
    trigger: ['blur', 'change']
  },
  'checkbox': {
    validator: (rule, value, callback) => {
      if (rule.required) {
        // checkboxField组件将绑定的urlList使用了JSON.stringfy
        if (value) {
          return JSON.parse(value).length > 0
        } else {
          return false
        }
      } else {
          return
      }
    },
    message: '请选择'
  },
  'datetimepicker': {
    required: true,
    message: '请选择',
    trigger: ['change']
  },
  'datepicker': {
    required: true,
    message: '请选择',
    trigger: ['change']
  },
};
export const getFieldRule = (info) => {
  if(rulesMap[info.fieldType]) {
    if(info.fieldRemark) {
      rulesMap[info.fieldType].message = info.fieldRemark;
    }
    rulesMap[info.fieldType].required = info.isMust === '1';
    return rulesMap[info.fieldType];
  }
}
