import store from '@/store';
import config from '@/config';
import { getToken } from '@/utils/auth';
import errorCode from '@/utils/errorCode';
import { toast, showConfirm, tansParams } from '@/utils/common';
// 是否显示重新登录
export const isRelogin = { show: false };
let timeout = 10000;
const baseUrl = config.baseUrl;
const signalMap = new Map();

const request = (config) => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false;
  const key = config.url;
  let signal;

  if (signalMap.has(key)) {
    const cotro = signalMap.get(key);

    cotro.abort();
  }
  config.header = config.header || {};
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken();
    if (config.method === "put" || config.method === "delete") {
      // 真实的请求方式（后端根据这个改写）；
      config.headers["X-HTTP-Method-Override"] = config.method;
      // put和delete发起post请求；
      config.method = "post";
    };
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.url = url;
  }
  return new Promise((resolve, reject) => {
    signal = uni.request({
      method: config.method || 'get',
      timeout: config.timeout || timeout,
      url: config.baseUrl || baseUrl + config.url,
      data: config.data,
      header: config.header,

      dataType: 'json',
      success: (res) => {
        if (signalMap.has(key)) signalMap.delete(key);

        //let [error, res] = response;
        // if (error) {
        //   toast('后端接口连接异常');
        //   // reject('后端接口连接异常');
        //   return;
        // }
        const code = res.data.code || 200;
        const msg = errorCode[code] || res.data.msg || errorCode['default'];
        if (code === 401) {
          if (!isRelogin.show) {
            isRelogin.show = true;
            showConfirm(
              '登录状态已过期，您可以继续留在该页面，或者重新登录?'
            ).then((res) => {
              if (res.confirm) {
                isRelogin.show = false;
                store
                  .dispatch('LogOut')
                  .then((res) => {
                    uni.reLaunch({ url: '/pages/login' });
                  })
                  .catch((err) => {
                    isRelogin.show = false;
                    uni.reLaunch({ url: '/pages/login' });
                  });
              }
            });
          }

          // reject('无效的会话，或者会话已过期，请重新登录。');
        } else if (code === 500) {
          toast(msg);
          reject({code, msg});
        } else if (code !== 200) {
          toast(msg);
          reject(code);
        }
        resolve(res.data);
      },
      fail: (error) => {
        if (signalMap.has(key)) signalMap.delete(key);
        let { errMsg } = error;
        if (errMsg.includes('abort')) {
          reject(error);
          return;
        }
        if (errMsg === 'Network Error') {
          errMsg = '后端接口连接异常';
        } else if (errMsg.includes('timeout')) {
          message = '系统接口请求超时';
        } else if (errMsg.includes('Request failed with status code')) {
          errMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常';
        }
        toast(errMsg);
        reject(error);
      },
    });
    // .then((response) => {

    // })
    // .catch();

    if (!config.method || config.method == 'get') {
      signalMap.set(key, signal);
    }
  });
};

export default request;
